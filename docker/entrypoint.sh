#!/bin/sh
set -e

if [ "$1" = 'uwsgi' ]; then
    chown nobody /media
    chown nobody /infocert/tmp
    echo 'Wait for database'
    su-exec nobody python3 /src/manage.py wait_for_database
    echo 'Migrate'
    su-exec nobody python3 /src/manage.py migrate --noinput
    echo 'su-exec nobody uwsgi "${@:6}"'
    exec su-exec nobody uwsgi "${@:6}"
elif [ "$1" = 'celery' ] && [ "$2" = 'flower' ]; then
    chown nobody /data
    echo "$@"
    exec su-exec nobody "$@"
elif [ "$1" = 'celery' ]; then
    if [ "$2" = 'flower' ]; then
        chown nobody /data
    fi
    echo 'Wait for database'
    su-exec nobody python3 /src/manage.py wait_for_database
    echo "$@"
    exec su-exec nobody "$@"
elif [ "$1" = 'nginx' ]; then
    chown nobody /var/tmp/nginx
    exec nginx -c /src/docker/nginx.conf -g "daemon off;"
else
    exec "$@"
fi
