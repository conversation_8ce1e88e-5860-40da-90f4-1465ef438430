user  nobody;
worker_processes  1;

error_log  /dev/stderr warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$request_time"';

    access_log  /dev/stdout  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    server {
        listen 80;

        location /static/
        {
                root /;
        }

        location = /favicon.ico  {
        return 404;
        access_log off;
        log_not_found off;
        }

        location ^~ /apple {
        return 404;
        access_log off;
        log_not_found off;
        }

        location = /robots.txt {
        return 404;
        access_log off;
        log_not_found off;
        }

        location / {
            proxy_pass              http://django:8000;
            client_max_body_size    100M;
            proxy_redirect          off;
            proxy_set_header        Host              $host;
            proxy_set_header        X-Real-IP         $remote_addr;
            proxy_set_header        X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $http_x_forwarded_proto;
            proxy_send_timeout      3600s;
            proxy_read_timeout      3600s;
        }

    }
}
