#!/usr/bin/env python
"""
Test script per verificare la funzionalità di esportazione piano conti aggregato
"""
import os
import sys
import django
from decimal import Decimal

# Aggiungi il path del progetto
sys.path.append('/home/<USER>/workspace/mastergest')

# Configura Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mastergest.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import User
from mastergest.fatturazione.admin import FatturaAdmin
from mastergest.fatturazione.models import Fattura, RigaFattura, PianoContiGamma
from mastergest.fatturazione.tests.factories import (
    FatturaFactory, RigaFatturaFactory, PianoContiGammaFactory
)

def test_export_piano_conti_aggregato():
    """Test della funzione export_piano_conti_aggregato_csv"""
    
    # Crea alcuni piani dei conti di test
    piano1 = PianoContiGammaFactory(nome="Costi Generali", codice="CG001")
    piano2 = PianoContiGammaFactory(nome="Ricavi Vendite", codice="RV001")
    
    # Crea alcune fatture di test
    fattura1 = FatturaFactory()
    fattura2 = FatturaFactory()
    
    # Crea righe fattura con diversi piani dei conti
    riga1 = RigaFatturaFactory(
        fattura=fattura1,
        piano_conti_gamma=piano1,
        quantita=Decimal('2'),
        costo_unitario=Decimal('100.00'),
        contabile=True
    )
    
    riga2 = RigaFatturaFactory(
        fattura=fattura1,
        piano_conti_gamma=piano2,
        quantita=Decimal('1'),
        costo_unitario=Decimal('50.00'),
        contabile=True
    )
    
    riga3 = RigaFatturaFactory(
        fattura=fattura2,
        piano_conti_gamma=piano1,
        quantita=Decimal('1'),
        costo_unitario=Decimal('75.00'),
        contabile=True
    )
    
    # Crea una riga non contabile (dovrebbe essere esclusa)
    riga4 = RigaFatturaFactory(
        fattura=fattura2,
        piano_conti_gamma=piano1,
        quantita=Decimal('1'),
        costo_unitario=Decimal('25.00'),
        contabile=False
    )
    
    print("Dati di test creati:")
    print(f"Piano 1: {piano1.nome} ({piano1.codice})")
    print(f"Piano 2: {piano2.nome} ({piano2.codice})")
    print(f"Riga 1: {riga1.get_totale_riga()} per {piano1.nome}")
    print(f"Riga 2: {riga2.get_totale_riga()} per {piano2.nome}")
    print(f"Riga 3: {riga3.get_totale_riga()} per {piano1.nome}")
    print(f"Riga 4 (non contabile): {riga4.get_totale_riga()} per {piano1.nome}")
    
    # Crea un request factory e un admin
    factory = RequestFactory()
    request = factory.get('/admin/')
    request.user = User.objects.create_superuser('admin', '<EMAIL>', 'password')
    
    # Crea l'admin e testa l'esportazione
    admin_site = AdminSite()
    fattura_admin = FatturaAdmin(Fattura, admin_site)
    
    # Queryset con le fatture di test
    queryset = Fattura.objects.filter(id__in=[fattura1.id, fattura2.id])
    
    # Esegui l'esportazione
    response = fattura_admin.export_piano_conti_aggregato_csv(request, queryset)
    
    # Verifica il contenuto della risposta
    content = response.content.decode('utf-8')
    lines = content.strip().split('\n')
    
    print("\nContenuto CSV generato:")
    for line in lines:
        print(line)
    
    # Verifica che l'header sia corretto
    header = lines[0].split(';')
    expected_header = ['Piano dei Conti', 'Codice Piano Conti', 'Totale']
    assert header == expected_header, f"Header non corretto: {header}"
    
    # Verifica che ci siano le righe per entrambi i piani dei conti
    data_lines = lines[1:]
    assert len(data_lines) == 2, f"Dovrebbero esserci 2 righe di dati, trovate: {len(data_lines)}"
    
    # Analizza i dati
    totali_trovati = {}
    for line in data_lines:
        parts = line.split(';')
        nome_piano = parts[0]
        codice_piano = parts[1]
        totale = Decimal(parts[2].replace(',', '.'))
        totali_trovati[nome_piano] = totale
    
    # Verifica i totali calcolati
    # Piano 1: riga1 (200.00) + riga3 (75.00) = 275.00 (riga4 esclusa perché non contabile)
    expected_piano1 = riga1.get_totale_riga() + riga3.get_totale_riga()
    # Piano 2: riga2 (50.00) = 50.00
    expected_piano2 = riga2.get_totale_riga()
    
    print(f"\nTotali attesi:")
    print(f"{piano1.nome}: {expected_piano1}")
    print(f"{piano2.nome}: {expected_piano2}")
    
    print(f"\nTotali trovati:")
    print(f"{piano1.nome}: {totali_trovati.get(piano1.nome, 'NON TROVATO')}")
    print(f"{piano2.nome}: {totali_trovati.get(piano2.nome, 'NON TROVATO')}")
    
    assert totali_trovati[piano1.nome] == expected_piano1, \
        f"Totale per {piano1.nome} non corretto: {totali_trovati[piano1.nome]} vs {expected_piano1}"
    assert totali_trovati[piano2.nome] == expected_piano2, \
        f"Totale per {piano2.nome} non corretto: {totali_trovati[piano2.nome]} vs {expected_piano2}"
    
    print("\n✅ Test completato con successo!")
    return True

if __name__ == '__main__':
    try:
        test_export_piano_conti_aggregato()
        print("🎉 Tutti i test sono passati!")
    except Exception as e:
        print(f"❌ Test fallito: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
