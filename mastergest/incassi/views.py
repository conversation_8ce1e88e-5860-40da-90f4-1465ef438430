from django.contrib import messages
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.views.generic.base import TemplateView
from mastergest.incassi import forms


class AssociazioneMastertrainingView(TemplateView):
    template_name = "admin/incassi/associazione/change_list.html"

    def post(self, request, *args, **kwargs):
        formset = forms.AbbinamentoMastertrainingWizardFormSet(request.POST, request.FILES)
        if formset.is_valid():
            instances = formset.save()
            if len(instances) == 0:
                messages.warning(request, 'Nessun abbinamento confermato.')
            elif len(instances) == 1:
                msg = '1 abbinamento salvato con successo.'
                messages.success(request, msg)
            else:
                msg = '%i abbinamenti salvati con successo.' % len(instances)
                messages.success(request, msg)
            url = reverse('incassi_associazione_mt')
            return HttpResponseRedirect(url)

    def get(self, request, *args, **kwargs):
        formset = forms.AbbinamentoMastertrainingWizardFormSet()
        context = dict(
            title='Associazione incassi Mastertraining', app_label='incassi', formset=formset,
            gestione_azienda='mastertraining'
        )
        return self.render_to_response(context)


class AssociazioneMagisterView(TemplateView):
    template_name = "admin/incassi/associazione/change_list.html"

    def post(self, request, *args, **kwargs):
        formset = forms.AbbinamentoMagisterWizardFormSet(request.POST, request.FILES)
        if formset.is_valid():
            instances = formset.save()
            if len(instances) == 0:
                messages.warning(request, 'Nessun abbinamento confermato.')
            elif len(instances) == 1:
                msg = '1 abbinamento salvato con successo.'
                messages.success(request, msg)
            else:
                msg = '%i abbinamenti salvati con successo.' % len(instances)
                messages.success(request, msg)
            url = reverse('incassi_associazione_mg')
            return HttpResponseRedirect(url)

    def get(self, request, *args, **kwargs):
        formset = forms.AbbinamentoMagisterWizardFormSet()
        context = dict(
            title='Associazione incassi Magister', app_label='incassi', formset=formset,
            gestione_azienda='magister'
        )
        return self.render_to_response(context)
