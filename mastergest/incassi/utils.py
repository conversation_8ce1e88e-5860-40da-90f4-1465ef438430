from decimal import Decimal
from django.db import models
from mastergest.fatturazione.models import ScadenzaPagamento
from mastergest.incassi.models import Incasso, Abbinamento


def get_abbinamento_wizard_initial(gestione_azienda):
    incassi = Incasso.objects.filter(abbinato=False).order_by(
        'anagrafica__ragione_sociale', 'data')
    initial = []
    for incasso in incassi:
        abbinamenti = incasso.abbinamento_set.all()
        incasso_abbinato = abbinamenti.aggregate(models.Sum('importo'))['importo__sum'] \
            or Decimal('0.00')
        incasso_da_abbinare = incasso.importo - incasso_abbinato
        scadenze = ScadenzaPagamento.objects.filter(
            fattura__cliente=incasso.anagrafica, pagata=False,
            storno__isnull=True, fattura__gestione_azienda=gestione_azienda
        ).order_by('data')
        for scadenza in scadenze:
            # Verifica se esiste un record corrispondente
            if Abbinamento.objects.filter(incasso=incasso, scadenza=scadenza).count() > 0:
                continue
            # Totale abbinato della scadenza
            abbinamenti = scadenza.abbinamento_set.all()
            scadenza_abbinato = abbinamenti.aggregate(models.Sum('importo'))['importo__sum'] \
                or Decimal('0.00')
            scadenze = scadenza.fattura.scadenzapagamento_set.all()
            scadenza_storno = scadenza.storno or Decimal('0.00')
            scadenza_pagato = scadenza_abbinato + scadenza_storno
            scadenza_da_abbinare = scadenza.importo - scadenza_pagato
            importo = min(scadenza_da_abbinare, incasso_da_abbinare)
            storno = Decimal('0.00')
            match = ''
            diff = scadenza_da_abbinare - incasso_da_abbinare
            if diff == Decimal('0.00'):
                match = 'exact'
            elif diff > Decimal('0.00') and diff <= Decimal('10.00'):
                match = 'similar'
                storno = diff
            initial.append(dict(
                incasso=incasso,
                abbinato=incasso_abbinato,
                da_abbinare=incasso_da_abbinare,
                scadenza=scadenza,
                incassato=scadenza_pagato,
                da_incassare=scadenza_da_abbinare,
                importo_proposto=importo,
                storno_proposto=storno,
                match=match,
            ))
    return initial
