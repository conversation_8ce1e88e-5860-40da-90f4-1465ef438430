from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db.models import Sum


def clean_abbinamento_importo(importo, incasso, scadenza, abbinamento_pk):
    if not importo:
        return importo
    # Incasso
    abbinamenti = incasso.abbinamento_set.all()
    if abbinamento_pk:
        abbinamenti = abbinamenti.exclude(pk=abbinamento_pk)
    totale = abbinamenti.aggregate(Sum('importo'))['importo__sum'] or Decimal('0.00')
    totale += importo
    if totale > incasso.importo:
        msg = 'L\'importo totale supera l\'incasso.'
        raise ValidationError(msg)
    # Scadenza
    abbinamenti = scadenza.abbinamento_set.all()
    if abbinamento_pk:
        abbinamenti = abbinamenti.exclude(pk=abbinamento_pk)
    totale = abbinamenti.aggregate(Sum('importo'))['importo__sum'] or Decimal('0.00')
    totale += importo
    scadenza_storno = scadenza.storno or Decimal('0.00')
    if totale > scadenza.importo - scadenza_storno:
        msg = 'L\'importo totale supera la scadenza.'
        raise ValidationError(msg)
    return importo
