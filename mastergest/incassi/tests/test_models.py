from datetime import date
from decimal import Decimal
from django.test import TestCase
from django.core.exceptions import ValidationError
from mastergest.anagrafe.models import Anagrafica
from mastergest.fatturazione.models import Fattura, ScadenzaPagamento
from mastergest.incassi.models import Incasso, IncassoSenzaAnagrafica
from mastergest.incassi.models import Abbinamento


class TestIncassoModel(TestCase):
    fixtures = [
        'anagrafe_test.json',
        'fatturazione_test.json',
    ]

    def setUp(self):
        ScadenzaPagamento.objects.all().delete()
        self.fattura = Fattura.objects.get(pk=1)
        self.scadenza_1 = ScadenzaPagamento.objects.create(
            id=1,
            fattura=self.fattura, importo=Decimal('100.00')
        )
        self.scadenza_2 = ScadenzaPagamento.objects.create(
            id=2,
            fattura=self.fattura, importo=Decimal('20.00')
        )

    def test_unicode(self):
        don_milani = Anagrafica.objects.get(pk=4)
        incasso = Incasso(
            anagrafica=don_milani, importo=Decimal('100.00'),
            data=date(2011, 11, 29)
        )
        self.assertEqual(str(incasso), '100.00 - 29/11/2011 - Don Milani')
        incasso = Incasso(
            importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        self.assertEqual(str(incasso), '100.00 - 29/11/2011')

    def test_fattura_pagata(self):
        # Nessun incasso pervenuto
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Incassi
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('120.00'), data=date(2011, 11, 29)
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Abbinamento 1
        abbinamento_1 = Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1,
            importo=Decimal('100.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Abbinamento 2
        abbinamento_2 = Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_2, importo=Decimal('20.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.count(), 2)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, True)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, True)
        # Aumento importo scadenza
        self.scadenza_1.importo = Decimal('110.00')
        self.scadenza_1.save()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, True)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Diminuzione importo abbinamento
        abbinamento_2.importo = Decimal('15.00')
        abbinamento_2.save()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Aumento importo abbinamento
        incasso.importo = Decimal('130.00')
        incasso.save()
        abbinamento_1.importo = Decimal('110.00')
        abbinamento_1.save()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Eliminazione scadenza 2
        self.scadenza_2.delete()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, True)

    def test_pagata_storno(self):
        self.scadenza_1.storno = Decimal('5.00')
        self.scadenza_1.save()
        # Nessun incasso pervenuto
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Incasso
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('120.00'), data=date(2011, 11, 29)
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Abbinamento 1 alto
        self.assertRaises(
            ValidationError, Abbinamento.objects.create, incasso=incasso,
            scadenza=self.scadenza_1, importo=Decimal('100.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Abbinamento 1 ok
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('95.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)
        # Abbinamento 2
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_2, importo=Decimal('20.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.count(), 2)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, True)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, True)

    def test_incasso_abbinato(self):
        # Aggiungo una nuova scadenza
        self.scadenza_3 = ScadenzaPagamento.objects.create(
            id=3, fattura=self.fattura, importo=Decimal('30.00')
        )
        # Incassi
        incasso_1 = Incasso.objects.create(
            id=1, importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        incasso_2 = Incasso.objects.create(
            id=2, importo=Decimal('50.00'),
            data=date(2011, 11, 30)
        )
        self.assertEqual(Incasso.objects.get(pk=1).abbinato, False)
        self.assertEqual(Incasso.objects.get(pk=2).abbinato, False)
        # Abbinamento 1 (completo il primo incasso)
        Abbinamento.objects.create(
            incasso=incasso_1, scadenza=self.scadenza_1,
            importo=Decimal('100.00')
        )
        self.assertEqual(Incasso.objects.get(pk=1).abbinato, True)
        self.assertEqual(Incasso.objects.get(pk=2).abbinato, False)
        # Abbinamento 2 (solo una parte del secondo incasso)
        Abbinamento.objects.create(
            incasso=incasso_2, scadenza=self.scadenza_2,
            importo=Decimal('20.00')
        )
        self.assertEqual(Incasso.objects.get(pk=1).abbinato, True)
        self.assertEqual(Incasso.objects.get(pk=2).abbinato, False)
        # Abbinamento 3 (completo il secondo incasso)
        Abbinamento.objects.create(
            incasso=incasso_2, scadenza=self.scadenza_3,
            importo=Decimal('30.00')
        )
        self.assertEqual(Incasso.objects.get(pk=1).abbinato, True)
        self.assertEqual(Incasso.objects.get(pk=2).abbinato, True)
        # Elimino una scadenza e il relativo incasso non e' piu' abbinato
        self.scadenza_2.delete()
        self.assertEqual(Incasso.objects.get(pk=1).abbinato, True)
        self.assertEqual(Incasso.objects.get(pk=2).abbinato, False)

    def test_delete_incasso_1(self):
        # Incassi
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('120.00'), data=date(2011, 11, 29)
        )
        # Abbinamenti
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('100.00')
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_2, importo=Decimal('20.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.count(), 2)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, True)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, True)
        # Cancellazione incasso
        incasso.delete()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)

    def test_delete_incasso_2(self):
        # Incassi
        incasso_1 = Incasso.objects.create(
            id=1, importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        incasso_2 = Incasso.objects.create(
            id=2, importo=Decimal('20.00'), data=date(2011, 11, 29)
        )
        # Abbinamenti
        Abbinamento.objects.create(
            incasso=incasso_1, scadenza=self.scadenza_1,
            importo=Decimal('100.00')
        )
        Abbinamento.objects.create(
            incasso=incasso_2, scadenza=self.scadenza_2,
            importo=Decimal('20.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.count(), 2)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, True)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, True)
        # Cancellazione incasso 2
        incasso_2.delete()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).pagata, False)
        self.assertEqual(Fattura.objects.get(pk=1).pagata, False)


class TestIncassoSenzaAnagraficaModel(TestCase):
    fixtures = ['anagrafe_test.json']

    def test_anagrafica_null(self):
        Incasso.objects.create(
            importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        self.assertEqual(Incasso.objects.count(), 1)
        self.assertEqual(IncassoSenzaAnagrafica.objects.count(), 1)

    def test_anagrafica(self):
        anagrafica = Anagrafica.objects.get(pk=1)
        Incasso.objects.create(
            importo=Decimal('100.00'), data=date(2011, 11, 29),
            anagrafica=anagrafica
        )
        self.assertEqual(Incasso.objects.count(), 1)
        self.assertEqual(IncassoSenzaAnagrafica.objects.count(), 0)


class TestAbbinamentoModel(TestCase):
    fixtures = [
        'anagrafe_test.json',
        'fatturazione_test.json',
    ]

    def setUp(self):
        ScadenzaPagamento.objects.all().delete()
        self.fattura = Fattura.objects.get(pk=1)
        self.scadenza_1 = ScadenzaPagamento.objects.create(
            id=1, fattura=self.fattura, importo=Decimal('100.00')
        )
        self.scadenza_2 = ScadenzaPagamento.objects.create(
            id=2, fattura=self.fattura, importo=Decimal('50.00')
        )

    def test_importo_ok(self):
        incasso = Incasso.objects.create(
            importo=Decimal('50.00'), data=date(2012, 1, 1)
        )
        abbinamento = Abbinamento.objects.create(
            id=1, incasso=incasso, scadenza=self.scadenza_1,
            importo=Decimal('20.00')
        )
        abbinamento.importo = Decimal('50.00')
        abbinamento.save()

    def test_importo_supera_incasso(self):
        incasso = Incasso.objects.create(
            importo=Decimal('50.00'), data=date(2011, 11, 29)
        )
        self.assertRaises(
            ValidationError, Abbinamento.objects.create, id=1,
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('70.00')
        )

    def test_importo_totale_supera_incasso(self):
        incasso = Incasso.objects.create(
            importo=Decimal('120.00'), data=date(2011, 11, 29)
        )
        Abbinamento.objects.create(
            id=1, incasso=incasso, scadenza=self.scadenza_1,
            importo=Decimal('100.00')
        )
        self.assertRaises(
            ValidationError, Abbinamento.objects.create, id=2,
            incasso=incasso, scadenza=self.scadenza_2, importo=Decimal('30.00')
        )

    def test_importo_totale_supera_scadenza(self):
        # Primo incasso
        incasso = Incasso.objects.create(
            importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('50.00')
        )
        # Secondo incasso
        incasso = Incasso.objects.create(
            importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        self.assertRaises(
            ValidationError, Abbinamento.objects.create, incasso=incasso,
            scadenza=self.scadenza_1, importo=Decimal('60.00')
        )

    def test_importo_totale_supera_scadenza_storno(self):
        # Aggiungo uno storno alla scadenza 1 (importo 100)
        self.scadenza_1.storno = Decimal('5.00')
        self.scadenza_1.save()
        # Primo incasso
        incasso = Incasso.objects.create(
            importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('50.00')
        )
        # Secondo incasso
        incasso = Incasso.objects.create(
            importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        self.assertRaises(
            ValidationError, Abbinamento.objects.create, incasso=incasso,
            scadenza=self.scadenza_1, importo=Decimal('50.00')
        )
