from decimal import Decimal

from django.utils import timezone

import factory

from .. import models
from mastergest.anagrafe.tests.factories import AnagraficaFactory
from mastergest.fatturazione.tests.factories import BancaAppoggioFactory
from mastergest.fatturazione.tests.factories import ScadenzaPagamentoFactory


class IncassoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.Incasso

    data = timezone.now()
    tipo = 'riba'
    importo = Decimal('100.00')
    anagrafica = factory.SubFactory(AnagraficaFactory)
    banca = factory.SubFactory(BancaAppoggioFactory)
    causale = factory.Sequence(lambda n: 'scadenza %s' % n)


class IncassoSenzaAnagraficaFactory(IncassoFactory):
    class Meta:
        model = models.IncassoSenzaAnagrafica

    tipo = 'bonifico'
    anagrafica = None


class AbbinamentoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.Abbinamento

    incasso = factory.SubFactory(IncassoFactory)
    scadenza = factory.SubFactory(ScadenzaPagamentoFactory)

    @factory.lazy_attribute
    def importo(self):
        return self.incasso.importo
