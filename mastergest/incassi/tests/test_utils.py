from datetime import date
from decimal import Decimal
from django.test import TestCase
from mastergest.fatturazione.models import Fattura, ScadenzaPagamento
from mastergest.incassi.models import Incasso, Abbinamento
from mastergest.incassi.utils import get_abbinamento_wizard_initial


class AbbinamentoWizardInitialTest(TestCase):
    fixtures = [
        'anagrafe_test.json',
        'fatturazione_test.json',
    ]

    def setUp(self):
        ScadenzaPagamento.objects.all().delete()
        self.fattura = Fattura.objects.get(pk=1)
        self.anagrafica = self.fattura.cliente
        self.scadenza_1 = ScadenzaPagamento.objects.create(id=1,
            fattura=self.fattura, importo=Decimal('100.00'))
        self.scadenza_2 = ScadenzaPagamento.objects.create(id=2,
            fattura=self.fattura, importo=Decimal('50.00'))
        self.gestione_azienda = 'mastertraining'

    def test_no_instance_1(self):
        " L'incasso corrrisponde alla prima scadenza non pagata "
        incasso = Incasso.objects.create(id=1, importo=Decimal('100.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('100.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('100.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('100.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], 'exact')
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('100.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_no_instance_2(self):
        " L'incasso e' inferiore alla prima scadenza non pagata "
        incasso = Incasso.objects.create(id=1, importo=Decimal('80.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('80.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('100.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('80.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('80.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_no_instance_3(self):
        """
         L'incasso supera la prima scadenza non pagata ma non copre interamente
         la seconda
        """
        incasso = Incasso.objects.create(id=1, importo=Decimal('130.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('130.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('100.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('100.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('130.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_no_instance_4(self):
        " L'incasso copre esattamente le due scadenze "
        incasso = Incasso.objects.create(id=1, importo=Decimal('150.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('150.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('100.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('100.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('150.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_no_instance_5(self):
        " L'incasso eccede le due scadenze "
        incasso = Incasso.objects.create(id=1, importo=Decimal('190.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('190.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('100.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('100.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('190.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_no_instance_6(self):
        " L'incasso quasi corrrisponde alla prima scadenza non pagata "
        incasso = Incasso.objects.create(id=1, importo=Decimal('90.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('90.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('100.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('90.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('10.00'))
        self.assertEqual(initial['match'], 'similar')
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('90.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_no_instance_storno(self):
        """
        La scadenza stornata non viene proposta
        """
        self.scadenza_1.storno = Decimal('10.00')
        self.scadenza_1.save()
        incasso = Incasso.objects.create(id=1, importo=Decimal('100.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 1)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('100.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        self.assertEqual(initial['match'], '')

    def test_aggiorna_abbinamento(self):
        """
        L'incasso copre la rimanenza della prima scadenza e parte della
        seconda
        """
        incasso = Incasso.objects.create(id=1, importo=Decimal('50.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        Abbinamento.objects.create(id=1, incasso=incasso,
                scadenza=self.scadenza_1, importo=Decimal('20.00'))
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 1)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('20.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('30.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('30.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))

    def test_abbinamento_da_altro_incasso(self):
        """
        Un incasso precedente copre parte della prima scadenza
        """
        incasso = Incasso.objects.create(importo=Decimal('30.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        Abbinamento.objects.create(incasso=incasso, scadenza=self.scadenza_1,
            importo=Decimal('30.00'))
        incasso = Incasso.objects.create(importo=Decimal('90.00'),
            data=date(2011, 11, 30), anagrafica=self.anagrafica)
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 2)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('90.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_1)
        self.assertEqual(initial['incassato'], Decimal('30.00'))
        self.assertEqual(initial['da_incassare'], Decimal('70.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('70.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))
        # 1
        initial = initials[1]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('0.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('90.00'))
        self.assertEqual(initial['scadenza'], self.scadenza_2)
        self.assertEqual(initial['incassato'], Decimal('0.00'))
        self.assertEqual(initial['da_incassare'], Decimal('50.00'))
        self.assertEqual(initial['importo_proposto'], Decimal('50.00'))
        self.assertEqual(initial['storno_proposto'], Decimal('0.00'))

    def TODO_test_incasso_eccede_fatture(self):
        """
        Il cliente ci ha dato piu' soldi del necessario !!!
        """
        incasso = Incasso.objects.create(importo=Decimal('400.00'),
            data=date(2011, 11, 29), anagrafica=self.anagrafica)
        Abbinamento.objects.create(incasso=incasso, scadenza=self.scadenza_1,
            importo=Decimal('100.00'))
        Abbinamento.objects.create(incasso=incasso, scadenza=self.scadenza_2,
            importo=Decimal('50.00'))
        initials = get_abbinamento_wizard_initial(self.gestione_azienda)
        self.assertEqual(len(initials), 1)
        # 0
        initial = initials[0]
        self.assertEqual(initial['incasso'], incasso)
        self.assertEqual(initial['abbinato'], Decimal('150.00'))
        self.assertEqual(initial['da_abbinare'], Decimal('250.00'))
        self.assertEqual(initial['scadenza'], None)
        self.assertEqual(initial['incassato'], None)
        self.assertEqual(initial['da_incassare'], None)
