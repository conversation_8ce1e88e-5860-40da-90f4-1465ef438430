from decimal import Decimal

from django.urls import reverse
from django.test import TestCase

from . import factories
from mastergest.anagrafe.tests.factories import UserFactory
from mastergest.fatturazione.tests.factories import RigaFatturaFactory
from mastergest.fatturazione.tests.factories import ScadenzaPagamentoFactory


class IncassoAdminTest(TestCase):
    def setUp(self):
        UserFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:incassi_incasso_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:incassi_incasso_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        incasso = factories.IncassoFactory()
        url = reverse('admin:incassi_incasso_change', args=(incasso.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        incasso = factories.IncassoFactory()
        url = reverse('admin:incassi_incasso_delete', args=(incasso.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class IncassoSenzaAnagraficaAdminTest(TestCase):
    def setUp(self):
        UserFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:incassi_incassosenzaanagrafica_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:incassi_incassosenzaanagrafica_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        incasso = factories.IncassoSenzaAnagraficaFactory()
        url = reverse('admin:incassi_incassosenzaanagrafica_change', args=(incasso.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        incasso = factories.IncassoSenzaAnagraficaFactory()
        url = reverse('admin:incassi_incassosenzaanagrafica_delete', args=(incasso.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class AbbinamentoAdminTest(TestCase):
    def setUp(self):
        UserFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:incassi_abbinamento_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:incassi_abbinamento_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        riga_fattura = RigaFatturaFactory(costo_unitario=Decimal('100.00'))
        scadenza = ScadenzaPagamentoFactory(fattura=riga_fattura.fattura)
        abbinamento = factories.AbbinamentoFactory(scadenza=scadenza)
        url = reverse('admin:incassi_abbinamento_change', args=(abbinamento.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        riga_fattura = RigaFatturaFactory(costo_unitario=Decimal('100.00'))
        scadenza = ScadenzaPagamentoFactory(fattura=riga_fattura.fattura)
        abbinamento = factories.AbbinamentoFactory(scadenza=scadenza)
        url = reverse('admin:incassi_abbinamento_delete', args=(abbinamento.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
