from datetime import date
from decimal import Decimal
from django.test import TestCase
from mastergest.fatturazione.models import Fattura, ScadenzaPagamento
from mastergest.incassi.forms import AbbinamentoForm, AbbinamentoMastertrainingWizardFormSet
from mastergest.incassi.models import Incasso, Abbinamento


class AbbinamentoFormTest(TestCase):
    fixtures = [
        'anagrafe_test.json',
        'fatturazione_test.json',
    ]

    def setUp(self):
        ScadenzaPagamento.objects.all().delete()
        self.fattura = Fattura.objects.get(pk=1)
        self.scadenza_1 = ScadenzaPagamento.objects.create(
            id=1, fattura=self.fattura, importo=Decimal('100.00')
        )
        self.scadenza_2 = ScadenzaPagamento.objects.create(
            id=2, fattura=self.fattura, importo=Decimal('50.00')
        )
        self.form = AbbinamentoForm

    def test_ok(self):
        Incasso.objects.create(
            id=1, importo=Decimal('100.00'), data=date(2011, 11, 29)
        )
        data = dict(incasso=1, scadenza=1, importo=Decimal('100.00'))
        form = self.form(data)
        self.assertEqual(form.errors, {})

    def test_ok_importo_basso(self):
        Incasso.objects.create(
            id=1, importo=Decimal('50.00'), data=date(2011, 11, 29)
        )
        data = dict(incasso=1, scadenza=1, importo='50')
        form = self.form(data)
        self.assertEqual(form.errors, {})

    def test_ok_storno(self):
        self.scadenza_1.storno = Decimal('5.00')
        self.scadenza_1.save()
        Incasso.objects.create(
            id=1, importo=Decimal('200.00'), data=date(2011, 11, 29)
        )
        data = dict(incasso=1, scadenza=1, importo='95')
        form = self.form(data)
        self.assertEqual(form.errors, {})

    def test_importo_supera_incasso(self):
        Incasso.objects.create(
            id=1, importo=Decimal('50.00'), data=date(2011, 11, 29)
        )
        data = dict(incasso=1, scadenza=1, importo='70')
        form = self.form(data)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera l\'incasso.']
        )

    def test_nuovo_importo_totale_supera_incasso(self):
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('80.00'), data=date(2011, 11, 29)
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('40.00')
        )
        data = dict(incasso=1, scadenza=1, importo=Decimal('50.00'))
        form = self.form(data)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera l\'incasso.']
        )

    def test_modifica_importo_totale_supera_incasso(self):
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('80.00'), data=date(2011, 11, 29)
        )
        abbinamento = Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('40.00')
        )
        abbinamento = Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('40.00')
        )
        # Totale ok
        data = dict(incasso=1, scadenza=1, importo=Decimal('30.00'))
        form = self.form(data, instance=abbinamento)
        self.assertEqual(form.errors, {})
        # Totale alto
        data = dict(incasso=1, scadenza=1, importo=Decimal('50.00'))
        form = self.form(data, instance=abbinamento)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera l\'incasso.']
        )

    def test_importo_supera_scadenza(self):
        Incasso.objects.create(
            id=1, importo=Decimal('250.00'), data=date(2011, 11, 29)
        )
        data = dict(incasso=1, scadenza=1, importo='120')
        form = self.form(data)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera la scadenza.']
        )

    def test_importo_supera_scadenza_storno(self):
        self.scadenza_1.storno = Decimal('5.00')
        self.scadenza_1.save()
        Incasso.objects.create(
            id=1, importo=Decimal('250.00'), data=date(2011, 11, 29)
        )
        data = dict(incasso=1, scadenza=1, importo='97')
        form = self.form(data)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera la scadenza.']
        )

    def test_nuovo_importo_totale_supera_scadenza(self):
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('250.00'), data=date(2011, 11, 29)
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('60.00')
        )
        data = dict(incasso=1, scadenza=1, importo=Decimal('50.00'))
        form = self.form(data)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera la scadenza.']
        )

    def test_modifica_importo_totale_supera_scadenza(self):
        incasso = Incasso.objects.create(
            id=1, importo=Decimal('250.00'), data=date(2011, 11, 29)
        )
        abbinamento = Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('50.00')
        )
        abbinamento = Abbinamento.objects.create(
            incasso=incasso, scadenza=self.scadenza_1, importo=Decimal('40.00')
        )
        # Totale ok
        data = dict(incasso=1, scadenza=1, importo=Decimal('50.00'))
        form = self.form(data, instance=abbinamento)
        self.assertEqual(form.errors, {})
        # Totale alto
        data = dict(incasso=1, scadenza=1, importo=Decimal('60.00'))
        form = self.form(data, instance=abbinamento)
        self.assertEqual(
            form.errors['importo'], ['L\'importo totale supera la scadenza.']
        )


class AbbinamentoWizardFormSetTest(TestCase):
    fixtures = [
        'anagrafe_test.json',
        'fatturazione_test.json',
    ]

    def setUp(self):
        ScadenzaPagamento.objects.all().delete()
        self.fattura = Fattura.objects.get(pk=1)
        self.scadenza_1 = ScadenzaPagamento.objects.create(
            id=1, fattura=self.fattura, importo=Decimal('100.00')
        )
        self.scadenza_2 = ScadenzaPagamento.objects.create(
            id=2, fattura=self.fattura, importo=Decimal('50.00')
        )
        self.incasso = Incasso.objects.create(
            id=1, importo=Decimal('250.00'), data=date(2011, 11, 29)
        )
        self.form = AbbinamentoMastertrainingWizardFormSet
        self.data = {
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-0-incasso': '1',
            'form-0-scadenza': '1',
            'form-0-importo': '90.00',
            'form-0-storno': '10.00',
            'form-0-confermato': True,
            'form-1-incasso': '1',
            'form-1-scadenza': '2',
            'form-1-importo': '50.00',
            'form-1-storno': '',
            'form-1-confermato': True,
        }

    def test_save(self):
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).storno, None)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).storno, None)
        self.assertEqual(Abbinamento.objects.count(), 0)
        form = self.form(self.data)
        self.assertEqual(form.errors, [{}, {}])
        form.save()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).storno, Decimal('10.00'))
        self.assertEqual(ScadenzaPagamento.objects.get(pk=2).storno, None)
        self.assertEqual(Abbinamento.objects.count(), 2)
        abbinamento = Abbinamento.objects.get(scadenza=self.scadenza_1)
        self.assertEqual(abbinamento.importo, Decimal('90.00'))
        abbinamento = Abbinamento.objects.get(scadenza=self.scadenza_2)
        self.assertEqual(abbinamento.importo, Decimal('50.00'))
