# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0001_initial'),
        ('anagrafe', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Abbinamento',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('importo', models.DecimalField(max_digits=9, decimal_places=2)),
            ],
            options={
                'ordering': ['-incasso__data'],
                'verbose_name_plural': 'abbinamenti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Incasso',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('data', models.DateField(db_index=True)),
                ('tipo', models.CharField(blank=True, max_length=50, choices=[(b'bonifico', b'Bonifico'), (b'riba', b'Riba'), (b'rid', b'Rid'), (b'contanti', b'Contanti'), (b'assegno', b'Assegno')])),
                ('importo', models.DecimalField(max_digits=9, decimal_places=2)),
                ('causale', models.CharField(max_length=200, blank=True)),
                ('iban', models.CharField(max_length=50, blank=True)),
                ('abi', models.CharField(max_length=50, blank=True)),
                ('cab', models.CharField(max_length=50, blank=True)),
                ('numero_conto', models.CharField(max_length=50, blank=True)),
                ('abbinato', models.BooleanField(default=False, db_index=True, editable=False)),
                ('anagrafica', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='anagrafe.Anagrafica', null=True)),
                ('banca', models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='fatturazione.BancaAppoggio', null=True)),
                ('scadenze', models.ManyToManyField(to='fatturazione.ScadenzaPagamento', through='incassi.Abbinamento')),
            ],
            options={
                'ordering': ['-data'],
                'verbose_name': 'incasso',
                'verbose_name_plural': 'incassi',
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='abbinamento',
            name='incasso',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='incassi.Incasso'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='abbinamento',
            name='scadenza',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='fatturazione.ScadenzaPagamento'),
            preserve_default=True,
        ),
        migrations.CreateModel(
            name='IncassoSenzaAnagrafica',
            fields=[
            ],
            options={
                'ordering': ['data'],
                'verbose_name': 'incasso senza anagrafica',
                'proxy': True,
                'verbose_name_plural': 'incassi senza anagrafica',
            },
            bases=('incassi.incasso',),
        ),
    ]
