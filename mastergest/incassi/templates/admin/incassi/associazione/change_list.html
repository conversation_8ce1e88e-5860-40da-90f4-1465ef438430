{% extends "admin/change_list.html" %}

{% load admin_list i18n %}
{% load l10n suit_tags %}

{% block extrastyle %}
{{ block.super }}
<style type="text/css">
	td.exact {background-color:lime;}
	td.similar {background-color:yellow;}
    .totale_ok {text-align:right; padding:0.3em 0.5em;}
</style>
{% if gestione_azienda == 'magister' %}
<style>
    body {
        background-color: {{ 'MAGISTER_BACKGROUND_COLOR'|suit_conf }};
    }
</style>
{% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<script type="text/javascript" src="/static/js/jquery.js"></script>
<script type="text/javascript">
function compila_importo(importo, storno, id_importo, id_storno, id_confermato) {
	$(id_importo).val(importo.replace(/,/g, '.'));
	if (storno > 0) {
		$(id_storno).val(storno.replace(/,/g, '.'));
	}
	$(id_confermato).attr('checked', true);
};
function pulisci_importo(id_importo, id_storno, id_confermato) {
	$(id_storno).val('');
	$(id_importo).val('');
	$(id_confermato).attr('checked', false);
};
</script>
{% endblock %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
      <a href="../../">
        {% trans "Home" %}
      </a>
       &rsaquo;
       <a href="../">
         {{ app_label|capfirst }}
      </a>
      &rsaquo;
      Associazioni
    </div>
{% endblock %}

{% block content %}
{% if formset %}
<div id="changelist" class="module">
<form id="changelist-form" action="" method="post">{% csrf_token %}

<div class="hiddenfields">
    {{ formset.management_form }}
</div>

<div class="results">

<table id="result_list" class="table table-striped table-bordered table-hover table-condensed">

<thead>
    <tr>
        <th scope="col">Anagrafica</th>
        <th scope="col">Incasso</th>
        <th scope="col">Fattura</th>
        <th scope="col">Data Scadenza</th>
        <th colspan="3" scope="col">Da incassare / Totale</th>
        <th scope="col"></th>
        <th scope="col">Importo</th>
        <th scope="col">Storno</th>
        <th scope="col">Conferma</th>
    </tr>
</thead>

<tbody>
    {% for form in formset %}
        {% for hidden in form.hidden_fields %}
            {{ hidden }}
        {% endfor %}
        {% if form.non_field_errors %}
            <tr class="error"><td colspan="8">{{ form.non_field_errors }}</td></tr>
        {% endif %}
        <tr>
            <td>{% ifchanged %}{{ form.initial.incasso.anagrafica.get_link|safe }}{% endifchanged %}</td>
            <td>{% ifchanged %}{{ form.initial.incasso.get_link|safe }}{% endifchanged %}</td>
            <td>{% ifchanged %}{{ form.initial.scadenza.fattura.get_link_azienda|safe }}{% endifchanged %}</td>
            <td>{{ form.initial.scadenza.data|date:"d/m/Y" }}</td>
            <td class="{{ form.initial.match }}">
                <div align="right">
                    {{ form.initial.da_abbinare|floatformat:2 }}
                    <input type="button" align="right" value="->" onclick="compila_importo('{{ form.initial.importo_proposto|unlocalize }}', '{{ form.initial.storno_proposto|unlocalize }}', '#{{ form.id_for_importo }}', '#{{ form.id_for_storno }}', '#{{ form.id_for_confermato }}');" />
                </div>
            </td>
            <td class="{{ form.initial.match }}">
                <div align="right">
                    {{ form.initial.da_incassare|floatformat:2 }}
                </div>
            </td>
            <td>
                <div align="right">
                    {{ form.initial.scadenza.importo|floatformat:2 }}
                </div>
            </td>
            <td>
                <input type="button" value="Pulisci" onclick="pulisci_importo('#{{ form.id_for_importo }}', '#{{ form.id_for_storno }}', '#{{ form.id_for_confermato }}');" />
            </td>
            {% for field in form.visible_fields %}
            <td class="{% if field.errors %} error{% endif %}">
                {{ field.errors }}{{ field }}
            </td>
            {% endfor %}
        </tr>
    {% endfor %}
</tbody>

</table>

</div>

<p class="paginator">
    <input type="submit" name="_save" class="default" value="Salva"/>
</p>

</form>
</div>

{% else %}
    <h3>Nessun incasso da associare</h3>
{% endif %}


{% endblock %}
