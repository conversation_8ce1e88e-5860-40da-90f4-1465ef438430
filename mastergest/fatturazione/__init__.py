from django.apps import AppConfig
from django.db.models.signals import post_delete, post_save


default_app_config = 'mastergest.fatturazione.FatturazioneConfig'


class FatturazioneConfig(AppConfig):
    name = 'mastergest.fatturazione'
    verbose_name = 'Fat<PERSON><PERSON>'

    def ready(self):
        from mastergest.fatturazione import callbacks
        from mastergest.fatturazione import models
        from mastergest.sdi.models import TrasmissioneFattura
        post_delete.connect(callbacks.aggiorna_totali_fattura, sender=models.RigaFattura)
        post_delete.connect(callbacks.aggiorna_pagata_fattura, sender=models.ScadenzaPagamento)
        post_save.connect(callbacks.aggiorna_fatturato_cliente_callback, sender=models.Fattura)
        post_delete.connect(callbacks.aggiorna_fatturato_cliente_callback, sender=models.Fattura)
        post_save.connect(callbacks.aggiorna_stato_invio_fattura, sender=TrasmissioneFattura)
        post_delete.connect(callbacks.aggiorna_stato_invio_fattura, sender=TrasmissioneFattura)
