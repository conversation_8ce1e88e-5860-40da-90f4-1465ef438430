import factory
from decimal import Decimal

from django.utils import timezone

from .. import models
from mastergest.anagrafe.tests.factories import AnagraficaFactory


class BancaAppoggioFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.BancaAppoggio

    codice = factory.Sequence(lambda n: 'COD_%s' % n)
    nome = factory.Sequence(lambda n: 'BANCA N.%s' % n)
    abi = factory.Sequence(lambda n: '1230%s' % n)
    cab = factory.Sequence(lambda n: '5670%s' % n)
    conto = factory.Sequence(lambda n: '00000000%s' % n)
    iban = factory.Sequence(lambda n: '1234567890000%s' % n)


class CodiceScaglionePagamentoFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.CodiceScaglionePagamento

    nome = factory.Sequence(lambda n: 'SCAGLIONE %s' % n)
    giorni = factory.Sequence(lambda n: int('%s' % n))
    mesi = factory.Sequence(lambda n: int('%s' % n))
    anni = 0


class TipoPagamentoGammaFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.TipoPagamentoGamma

    nome = factory.Sequence(lambda n: 'PAGAMENTO %s' % n)
    codice = factory.Sequence(lambda n: 'COD_%s' % n)


class TipoPagamentoFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.TipoPagamento

    nome = factory.Sequence(lambda n: 'TIPO PAGAMENTO %s' % n)


class PianoContiGammaFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.PianoContiGamma

    nome = factory.Sequence(lambda n: 'piano conti %s' % n)
    codice = factory.Sequence(lambda n: 'codice_%s' % n)


class EsenzioneIvaFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.EsenzioneIva

    nome = factory.Sequence(lambda n: 'esenzione iva art. %s' % n)
    descrizione = factory.Sequence(lambda n: 'descrizione esenzione iva %s' % n)
    codice_gamma = factory.Sequence(lambda n: 'codice_%s' % n)


class FatturaFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.Fattura

    data = timezone.now().date()
    cliente = factory.SubFactory(AnagraficaFactory)
    banca_appoggio = factory.SubFactory(BancaAppoggioFactory)
    tipo_pagamento_gamma = factory.SubFactory(TipoPagamentoGammaFactory)


class RigaFatturaFactory(factory.DjangoModelFactory):

    class Meta:
        model = models.RigaFattura

    fattura = factory.SubFactory(FatturaFactory)
    descrizione = factory.Sequence(lambda n: 'oggetto %s' % n)
    quantita = 1
    percentuale_iva = '22'
    costo_unitario = Decimal('10.0')
    piano_conti_gamma = factory.SubFactory(PianoContiGammaFactory)


class ScadenzaPagamentoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.ScadenzaPagamento

    fattura = factory.SubFactory(FatturaFactory)
    codice_scaglione_pagamento = factory.SubFactory(CodiceScaglionePagamentoFactory)
    tipo_pagamento = factory.SubFactory(TipoPagamentoFactory)
