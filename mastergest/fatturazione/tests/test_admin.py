from decimal import Decimal
from datetime import date

from django.core import mail
from django.test import TestCase

from mastergest.fatturazione.models import Fattura


class TestFatturaAdmin(TestCase):
    fixtures = ['auth_test', 'anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.client.login(username='tester', password='tester')
        mail.outbox = []

    def test_list(self):
        response = self.client.get('/fatturazione/fattura/')
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get('/fatturazione/fattura/', data)
        self.assertEqual(response.status_code, 200)

    def test_add_page(self):
        response = self.client.get('/fatturazione/fattura/add/')
        self.assertEqual(response.status_code, 200)

    def test_invio_email_ok(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = 'don<PERSON><PERSON>@example.com'
        fattura.cliente.save()
        fattura.save()
        self.assertFalse(fattura.inviata)
        data = dict(action='invio_fatture_email', _selected_action=['1'])
        response = self.client.post('/fatturazione/fattura/', data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, '/fatturazione/fattura/')
        self.assertContains(response, "Inviate 1 fatture.")
        self.assertTrue(Fattura.objects.get(pk=1).inviata)
        self.assertEqual(len(mail.outbox), 1)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1 del 22/07/2011')
        self.assertEqual(
            message.recipients(),
            ['<EMAIL>', '<EMAIL>']
        )
        self.assertEqual(message.attachments[0][0], 'fattura_2011_1.pdf')

    def test_invio_email_ok_2013(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = '<EMAIL>'
        fattura.cliente.save()
        fattura.data = date(2013, 8, 12)
        fattura.save()
        self.assertFalse(fattura.inviata)
        data = dict(action='invio_fatture_email', _selected_action=['1'])
        response = self.client.post('/fatturazione/fattura/', data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, '/fatturazione/fattura/')
        self.assertContains(response, "Inviate 1 fatture.")
        self.assertTrue(Fattura.objects.get(pk=1).inviata)
        self.assertEqual(len(mail.outbox), 1)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1/2013 del 12/08/2013')
        self.assertEqual(
            message.recipients(),
            ['<EMAIL>', '<EMAIL>']
        )
        self.assertEqual(message.attachments[0][0], 'fattura_2013_1.pdf')

    def test_invio_email_ok_2013_bis(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = '<EMAIL>'
        fattura.cliente.save()
        fattura.data = date(2013, 8, 12)
        fattura.postfisso = 'bis'
        fattura.save()
        self.assertFalse(fattura.inviata)
        data = dict(action='invio_fatture_email', _selected_action=['1'])
        response = self.client.post('/fatturazione/fattura/', data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, '/fatturazione/fattura/')
        self.assertContains(response, "Inviate 1 fatture.")
        self.assertTrue(Fattura.objects.get(pk=1).inviata)
        self.assertEqual(len(mail.outbox), 1)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1/2013/bis del 12/08/2013')
        self.assertEqual(
            message.recipients(),
            ['<EMAIL>', '<EMAIL>']
        )
        self.assertEqual(message.attachments[0][0], 'fattura_2013_1_bis.pdf')

    def test_invio_email_ko_no_email_fatturazione(self):
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.cliente.email_fatturazione, '')
        data = dict(action='invio_fatture_email', _selected_action=['1'])
        response = self.client.post('/fatturazione/fattura/', data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, '/fatturazione/fattura/')
        self.assertContains(response, "Sono presenti fatture non abilitate all&#39;invio elettronico.")
        self.assertEqual(len(mail.outbox), 0)

    def test_invio_email_ko_inviata(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = '<EMAIL>'
        fattura.cliente.save()
        fattura.inviata = True
        fattura.save()
        data = dict(action='invio_fatture_email', _selected_action=['1'])
        response = self.client.post('/fatturazione/fattura/', data, follow=True)
        self.assertRedirects(response, '/fatturazione/fattura/')
        self.assertContains(response, "Sono presenti fatture gia&#39; inviate.")
        self.assertEqual(len(mail.outbox), 0)

    def test_pdf(self):
        response = self.client.get('/fatturazione/fattura/1/pdf/')
        self.assertEqual(response.status_code, 200)

    def test_pdf_divisa_estera(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.divisa = 'CHF'
        fattura.cambio_divisa = Decimal('0.91')
        response = self.client.get('/fatturazione/fattura/1/pdf/')
        self.assertEqual(response.status_code, 200)

    def test_change_form(self):
        response = self.client.get('/fatturazione/fattura/1/', follow=True)
        self.assertEqual(response.status_code, 200)

    def test_mass_pdf_anticipi(self):
        data = dict(anticipi=True)
        response = self.client.get('/fatturazione/fattura/', data, follow=True)
        self.assertEqual(response.status_code, 200)

    def test_pdf_anticipi(self):
        data = dict(scaglione='1', banca='1', anticipi='anticipi', data='2011-11-04')
        response = self.client.post('/fatturazione/fattura/', data)
        self.assertEqual(response.status_code, 200)

    def test_export_piano_conti_aggregato_csv(self):
        """Test dell'esportazione CSV aggregata per piano dei conti"""
        # Testa l'azione di esportazione piano conti aggregato
        data = dict(action='export_piano_conti_aggregato_csv', _selected_action=['1'])
        response = self.client.post('/fatturazione/fattura/', data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertEqual(
            response['Content-Disposition'],
            'attachment;filename="export_piano_conti_aggregato.csv"'
        )

        # Verifica che il contenuto CSV contenga le intestazioni corrette
        content = response.content.decode('utf-8')
        lines = content.strip().split('\n')
        self.assertTrue(len(lines) >= 1)  # Almeno l'header

        # Verifica l'header
        header = lines[0].split(';')
        expected_header = ['Piano dei Conti', 'Codice Piano Conti', 'Totale']
        self.assertEqual(header, expected_header)


class TestScadenzaPagamentoAdmin(TestCase):
    fixtures = ['auth_test', 'anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.client.login(username='tester', password='tester')

    def test_list(self):
        response = self.client.get('/fatturazione/scadenzapagamento/')
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get('/fatturazione/scadenzapagamento/', data=data)
        self.assertEqual(response.status_code, 200)

    def test_add_page(self):
        response = self.client.get('/fatturazione/scadenzapagamento/add/')
        self.assertEqual(response.status_code, 200)


class TestBancaAppoggioAdmin(TestCase):
    fixtures = ['auth_test', 'anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.client.login(username='tester', password='tester')

    def test_bancaappoggio(self):
        # List
        response = self.client.get('/fatturazione/bancaappoggio/')
        self.assertEqual(response.status_code, 200)

    def test_bancaappoggio_add_page(self):
        response = self.client.get('/fatturazione/bancaappoggio/add/')
        self.assertEqual(response.status_code, 200)


class TestCodiceScaglionePagamentoAdmin(TestCase):
    fixtures = ['auth_test', 'anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.client.login(username='tester', password='tester')

    def test_codicescaglionepagamento(self):
        # List
        response = self.client.get('/fatturazione/codicescaglionepagamento/')
        self.assertEqual(response.status_code, 200)

    def test_codicescaglionepagamento_add_page(self):
        response = self.client.get('/fatturazione/codicescaglionepagamento/add/')
        self.assertEqual(response.status_code, 200)


class TestEsenzioneIvaAdmin(TestCase):
    fixtures = ['auth_test', 'anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.client.login(username='tester', password='tester')

    def test_esenzioneiva(self):
        # List
        response = self.client.get('/fatturazione/esenzioneiva/')
        self.assertEqual(response.status_code, 200)

    def test_esenzioneiva_add_page(self):
        response = self.client.get('/fatturazione/esenzioneiva/add/')
        self.assertEqual(response.status_code, 200)


class TestTipoPagamentoAdmin(TestCase):
    fixtures = ['auth_test', 'anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.client.login(username='tester', password='tester')

    def test_tipopagamento(self):
        # List
        response = self.client.get('/fatturazione/tipopagamento/')
        self.assertEqual(response.status_code, 200)

    def test_esenzioneiva_add_page(self):
        response = self.client.get('/fatturazione/tipopagamento/add/')
        self.assertEqual(response.status_code, 200)
