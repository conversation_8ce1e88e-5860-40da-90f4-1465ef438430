from datetime import date
from decimal import Decimal
from django.test import TestCase
from mastergest.fatturazione.models import Fattura, ScadenzaPagamento
from mastergest.fatturazione.forms import ScadenzaPagamentoForm
from mastergest.fatturazione.forms import RigaFatturaForm


class ScadenzaPagamentoFormTest(TestCase):
    fixtures = ['anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.fattura = Fattura.objects.get(pk=1)
        self.assertEqual(self.fattura.totale, Decimal('1803.55'))
        ScadenzaPagamento.objects.all().delete()

    def test_ok(self):
        data = dict(fattura='1', data='2011-11-10', importo='1803.55')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(form.errors, {})

    def test_ok_importo_basse(self):
        data = dict(fattura='1', data='2011-11-10', importo='1800.00')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(form.errors, {})

    def test_importo_alto(self):
        data = dict(fattura='1', data='2011-11-10', importo='1850.00')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(
            form.errors['importo'],
            ["L'importo totale delle scadenze pagamenti (1850.00) supera il totale fattura (1803.55)."]
        )

    def test_totale_scadenze_ok(self):
        ScadenzaPagamento.objects.create(
            fattura=self.fattura,
            data=date(2011, 11, 10), importo=Decimal('100.00')
        )
        data = dict(fattura='1', data='2011-11-10', importo='1703.55')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(form.errors, {})

    def test_totale_scadenze_alto(self):
        ScadenzaPagamento.objects.create(
            fattura=self.fattura,
            data=date(2011, 11, 10), importo=Decimal('100.00')
        )
        data = dict(fattura='1', data='2011-11-10', importo='1750.00')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(
            form.errors['importo'],
            ["L'importo totale delle scadenze pagamenti (1850.00) supera il totale fattura (1803.55)."]
        )

    def test_storno_ok(self):
        data = dict(fattura='1', data='2011-11-10', importo='1803.55', storno='5.00')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(form.errors, {})

    def test_storno_ko(self):
        data = dict(fattura='1', data='2011-11-10', importo='1803.55', storno='1850.00')
        form = ScadenzaPagamentoForm(data)
        self.assertEqual(
            form.errors['storno'],
            ["Lo storno non puo' superare l'importo."]
        )


class RigaFatturaFormTest(TestCase):
    fixtures = ['anagrafe_test', 'fatturazione_test']

    def setUp(self):
        self.fattura = Fattura.objects.get(pk=1)

    def test_contabile_ok(self):
        data = dict(
            fattura='1', quantita='2', costo_unitario='1.00',
            percentuale_iva='21', contabile=True, order=0
        )
        form = RigaFatturaForm(data)
        self.assertEqual(form.errors, {})

    def test_contabile_iva_zero(self):
        data = dict(
            fattura='1', quantita='2', costo_unitario='1.00',
            percentuale_iva='0', contabile=True, order=0
        )
        form = RigaFatturaForm(data)
        self.assertEqual(form.errors, {})

    def test_contabile_no_quantita(self):
        data = dict(
            fattura='1', costo_unitario='1.00', percentuale_iva='21',
            contabile=True, order=0
        )
        form = RigaFatturaForm(data)
        self.assertEqual(
            form.errors['contabile'],
            ["Inserire quantita', costo unitario e percentuale iva"]
        )

    def test_contabile_no_costo(self):
        data = dict(
            fattura='1', quantita='2', percentuale_iva='21',
            contabile=True, order=0
        )
        form = RigaFatturaForm(data)
        self.assertEqual(
            form.errors['contabile'],
            ["Inserire quantita', costo unitario e percentuale iva"]
        )

    def test_contabile_no_iva(self):
        data = dict(
            fattura='1', quantita='2', costo_unitario='1.00',
            contabile=True, order=0
        )
        form = RigaFatturaForm(data)
        self.assertEqual(
            form.errors['contabile'],
            ["Inserire quantita', costo unitario e percentuale iva"]
        )

    def test_non_contabile_ok(self):
        data = dict(
            fattura='1', contabile=False, order=0, costo_unitario=None,
            percentuale_iva=None
        )
        form = RigaFatturaForm(data)
        self.assertEqual(form.errors, {})

    def test_non_contabile_errori(self):
        data = dict(
            fattura='1', contabile=False, order=0
        )
        form = RigaFatturaForm(data)
        self.assertEqual(
            form.errors['__all__'],
            ["Costo unitario e percentuale iva non sono ammessi nelle righe non contabili"]
        )

    def test_non_contabile_quantita(self):
        data = dict(
            fattura='1', quantita='2', contabile=False, order=0,
            costo_unitario=None, percentuale_iva=None
        )
        form = RigaFatturaForm(data)
        self.assertEqual(form.errors, {})

    def test_non_contabile_costo(self):
        data = dict(fattura='1', costo_unitario='1.00', contabile=False)
        form = RigaFatturaForm(data)
        self.assertEqual(
            form.errors['contabile'],
            ["Costo unitario e percentuale iva non sono ammessi nelle righe non contabili"]
        )

    def test_non_contabile_iva(self):
        data = dict(fattura='1', percentuale_iva='21', contabile=False)
        form = RigaFatturaForm(data)
        self.assertEqual(
            form.errors['contabile'],
            ["Costo unitario e percentuale iva non sono ammessi nelle righe non contabili"]
        )
