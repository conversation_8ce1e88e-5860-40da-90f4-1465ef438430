from django.core.exceptions import ValidationError
from django.test import TestCase
from decimal import Decimal
from datetime import date

from mastergest.fatturazione.models import Fattura, RigaFattura
from mastergest.fatturazione.models import ScadenzaPagamento, EsenzioneIva
from mastergest.fatturazione.models import CodiceScaglionePagamento
from mastergest.fatturazione.models import BancaAppoggio, TipoPagamentoGamma
from mastergest.anagrafe.models import Anagrafica, Sede

from mastergest.anagrafe.tests.factories import SedeFactory, AnagraficaFactory
from mastergest.fatturazione.tests.factories import TipoPagamentoGammaFactory, BancaAppoggioFactory


class TestRigaFatturaModel(TestCase):
    fixtures = [
        'anagrafe_test',
        'fatturazione_test',
    ]

    def test_get_quantita_stringa(self):
        fattura = Fattura.objects.get(pk=1)
        riga = RigaFattura(fattura=fattura)
        self.assertEqual(riga.get_quantita_stringa(), '1')
        # change quantita
        riga.quantita = Decimal('1.00')
        self.assertEqual(riga.get_quantita_stringa(), '1')
        # change quantita
        riga.quantita = Decimal('1.0')
        self.assertEqual(riga.get_quantita_stringa(), '1')
        # change quantita
        riga.quantita = Decimal('0.0')
        self.assertEqual(riga.get_quantita_stringa(), '')
        # change quantita
        riga.quantita = Decimal('0.00')
        self.assertEqual(riga.get_quantita_stringa(), '')
        # change quantita
        riga.quantita = Decimal('1.1')
        self.assertEqual(riga.get_quantita_stringa(), '1,1')
        # change quantita
        riga.quantita = Decimal('60')
        self.assertEqual(riga.get_quantita_stringa(), '60')
        # change quantita
        riga.quantita = Decimal('2300')
        self.assertEqual(riga.get_quantita_stringa(), '2300')
        # change quantita
        riga.quantita = Decimal('8.07')
        self.assertEqual(riga.get_quantita_stringa(), '8,07')
        # change quantita
        riga.quantita = Decimal('8.17')
        self.assertEqual(riga.get_quantita_stringa(), '8,17')

    def test_get_descrizione_xml(self):
        fattura = Fattura.objects.get(pk=1)
        riga = RigaFattura(fattura=fattura)
        riga.descrizione = 'Canone di assistenza per il sistema mastercom valido dal 01/01/2013 al 31/12/2013 - riferimento CIG 12345 CUP 5467'
        self.assertEqual(len(riga.descrizione), 114)
        desc_xml = riga.get_descrizione_xml()
        self.assertEqual(
            desc_xml,
            'Canone di assistenza per il sistema mastercom valido dal 01/01/2013 al 31/12/2013 - riferimento CIG 12345 CUP 5467'
        )

    def test_get_descrizione_xml_lunghissima(self):
        fattura = Fattura.objects.get(pk=1)
        riga = RigaFattura(fattura=fattura)
        descrizione_lunghissima = 'a' * 1200
        riga.descrizione = descrizione_lunghissima
        self.assertEqual(len(riga.descrizione), 1200)
        desc_xml = riga.get_descrizione_xml()
        self.assertEqual(len(desc_xml), 1000)
        self.assertEqual(desc_xml, 'a' * 997 + '...')


class TestFatturaModel(TestCase):
    fixtures = ['anagrafe_test', 'fatturazione_test']

    def test_get_banche_incassi(self):
        from mastergest.incassi.models import Incasso, Abbinamento
        fattura = Fattura.objects.get(pk=1)
        banche_incassi = fattura.get_banche_incassi()
        self.assertEqual(tuple(banche_incassi), tuple())
        scadenza_1 = fattura.get_scadenze_pagamenti()[0]
        scadenza_2 = fattura.get_scadenze_pagamenti()[1]
        incasso = Incasso.objects.create(
            data=date(2011, 11, 11), importo=Decimal('111.11')
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=scadenza_1, importo=Decimal('11.11')
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=scadenza_2, importo=Decimal('22.22')
        )
        banche_incassi = fattura.get_banche_incassi()
        self.assertEqual(tuple(banche_incassi), tuple())
        banca_appoggio = BancaAppoggio.objects.get(pk=1)
        incasso.banca = banca_appoggio
        incasso.save()
        banche_incassi = fattura.get_banche_incassi()
        self.assertEqual(tuple(banche_incassi), ('BPER',))
        incasso = Incasso.objects.create(
            data=date(2011, 11, 11), importo=Decimal('111.11'),
            banca=banca_appoggio
        )
        Abbinamento.objects.create(
            incasso=incasso, scadenza=scadenza_1, importo=Decimal('11.11')
        )
        banche_incassi = fattura.get_banche_incassi()
        self.assertEqual(tuple(banche_incassi), ('BPER',))
        incasso.banca = BancaAppoggio.objects.get(pk=2)
        incasso.save()
        banche_incassi = fattura.get_banche_incassi()
        self.assertEqual(tuple(banche_incassi), ('BPER', 'BPV'))

    def test_fattura_totali(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.totale, Decimal('1803.55'))

    def test_fattura_iva_tutte_20(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        for riga in elenco_righe:
            riga.percentuale_iva = 20
            riga.save()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('298.11'))

    def test_fattura_riga_senza_costo(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga = elenco_righe[0]
        riga.percentuale_iva = 20
        riga.costo_unitario = Decimal('0.00')
        riga.save()
        riga = elenco_righe[1]
        riga.percentuale_iva = 21
        riga.costo_unitario = Decimal('100.00')
        riga.save()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.imponibile, Decimal('100.00'))
        self.assertEqual(fattura.iva, Decimal('21.00'))

    def test_fattura_riga_senza_iva(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga = elenco_righe[0]
        riga.percentuale_iva = 0
        riga.costo_unitario = Decimal('10.00')
        riga.save()
        riga = elenco_righe[1]
        riga.percentuale_iva = 20
        riga.costo_unitario = Decimal('100.00')
        riga.save()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.imponibile, Decimal('110.00'))
        self.assertEqual(fattura.iva, Decimal('20.00'))

    def test_fattura_iva_varie(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga_1 = elenco_righe[0]
        riga_1.percentuale_iva = 10
        riga_1.save()
        riga_2 = elenco_righe[1]
        riga_2.percentuale_iva = 30
        riga_2.save()
        fattura = Fattura.objects.all()[0]
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('169.16'))

    def test_fattura_iva_zero(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        for riga in elenco_righe:
            riga.percentuale_iva = 0
            riga.save()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('0.00'))

    def test_nuova_fattura(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        self.assertEqual(fattura.numero, 1)
        nuova_fattura = Fattura.objects.create(
            cliente=Anagrafica.objects.get(pk=2),
            data=date(year=2011, month=8, day=12)
        )
        self.assertEqual(nuova_fattura.numero, 2)
        self.assertEqual(nuova_fattura.ragione_sociale, 'Allnet')
        self.assertEqual(nuova_fattura.anno_numero, '2011_00000002')
        nuova_fattura.numero = 1234
        nuova_fattura.save()
        self.assertEqual(nuova_fattura.anno_numero, '2011_00001234')
        nuova_fattura_2 = Fattura.objects.create(
            cliente=Anagrafica.objects.get(pk=2),
            data=date(year=2011, month=8, day=12)
        )
        self.assertEqual(nuova_fattura_2.numero, 1235)
        self.assertEqual(nuova_fattura_2.ragione_sociale, 'Allnet')
        self.assertEqual(nuova_fattura_2.anno_numero, '2011_00001235')
        nuova_fattura_2.numero = 100
        nuova_fattura_2.postfisso = 'bis'
        nuova_fattura_2.save()
        self.assertEqual(nuova_fattura_2.anno_numero, '2011_00000100_bis')
        nuova_fattura_3 = Fattura.objects.create(
            cliente=Anagrafica.objects.get(pk=2),
            data=date(year=2011, month=8, day=13)
        )
        self.assertEqual(nuova_fattura_3.numero, 1235)
        self.assertEqual(nuova_fattura_3.ragione_sociale, 'Allnet')
        self.assertEqual(nuova_fattura_3.anno_numero, '2011_00001235')
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 4)
        fattura = elenco_fatture[0]
        self.assertEqual(fattura.numero, 1235)
        self.assertEqual(fattura.get_numero(), '1235')
        fattura = elenco_fatture[1]
        self.assertEqual(fattura.numero, 1234)
        self.assertEqual(fattura.get_numero(), '1234')
        fattura = elenco_fatture[2]
        self.assertEqual(fattura.numero, 100)
        self.assertEqual(fattura.get_numero(), '100/bis')
        fattura = elenco_fatture[3]
        self.assertEqual(fattura.numero, 1)
        self.assertEqual(fattura.get_numero(), '1')

    def test_nuova_fattura_2013(self):
        Fattura.objects.all().delete()
        nuova_fattura = Fattura.objects.create(
            cliente=Anagrafica.objects.get(pk=2),
            data=date(year=2013, month=8, day=12)
        )
        self.assertEqual(nuova_fattura.numero, 1)
        self.assertEqual(nuova_fattura.ragione_sociale, 'Allnet')
        self.assertEqual(nuova_fattura.anno_numero, '2013_00000001')
        nuova_fattura.numero = 1234
        nuova_fattura.save()
        self.assertEqual(nuova_fattura.anno_numero, '2013_00001234')
        nuova_fattura_2 = Fattura.objects.create(
            cliente=Anagrafica.objects.get(pk=2),
            data=date(year=2013, month=8, day=12)
        )
        self.assertEqual(nuova_fattura_2.numero, 1235)
        self.assertEqual(nuova_fattura_2.ragione_sociale, 'Allnet')
        self.assertEqual(nuova_fattura_2.anno_numero, '2013_00001235')
        nuova_fattura_2.numero = 100
        nuova_fattura_2.postfisso = 'bis'
        nuova_fattura_2.save()
        self.assertEqual(nuova_fattura_2.anno_numero, '2013_00000100_bis')
        nuova_fattura_3 = Fattura.objects.create(
            cliente=Anagrafica.objects.get(pk=2),
            data=date(year=2013, month=8, day=13)
        )
        self.assertEqual(nuova_fattura_3.numero, 1235)
        self.assertEqual(nuova_fattura_3.ragione_sociale, 'Allnet')
        self.assertEqual(nuova_fattura_3.anno_numero, '2013_00001235')
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 3)
        fattura = elenco_fatture[0]
        self.assertEqual(fattura.numero, 1235)
        self.assertEqual(fattura.get_numero(), '1235/2013')
        fattura = elenco_fatture[1]
        self.assertEqual(fattura.numero, 1234)
        self.assertEqual(fattura.get_numero(), '1234/2013')
        fattura = elenco_fatture[2]
        self.assertEqual(fattura.numero, 100)
        self.assertEqual(fattura.get_numero(), '100/2013/bis')

    def test_nuovo_numero(self):
        Fattura.objects.all().delete()
        fattura = Fattura.objects.create(
            data=date(2011, 9, 20),
            cliente=Anagrafica.objects.get(ragione_sociale='Allnet')
        )
        self.assertEqual(fattura.numero, 1)
        fattura.numero = None
        fattura.save()
        self.assertEqual(fattura.numero, 1)

    def test_fattura_omaggio(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        fattura.omaggio = True
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('0.00'))
        self.assertEqual(fattura.iva, Decimal('0.00'))
        self.assertEqual(fattura.totale, Decimal('0.00'))

    def test_nota_di_credito(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.scadenzapagamento_set.all().delete()
        fattura.rigafattura_set.all().delete()
        RigaFattura.objects.create(
            fattura=fattura, quantita=1,
            costo_unitario=Decimal('-100.00'), percentuale_iva=20,
            contabile=True
        )
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.imponibile, Decimal('-100.00'))
        self.assertEqual(fattura.iva, Decimal('-20.00'))
        self.assertEqual(fattura.totale, Decimal('-120.00'))
        self.assertEqual(fattura.scadenze_complete, True)

    def test_fattura_esente_iva(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        fattura.esenzione_iva = EsenzioneIva.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('0.00'))
        self.assertEqual(fattura.totale, Decimal('1490.54'))

    def test_fattura_esente_iva_nobollo(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        esenzione = EsenzioneIva.objects.get(pk=1)
        esenzione.natura_esenzione_sdi = 'N3.1'
        esenzione.save()
        fattura.esenzione_iva = esenzione
        fattura.save()
        self.assertEqual(fattura.bollo_virtuale, False)
        self.assertEqual(fattura.importo_bollo, Decimal('0.00'))

    def test_fattura_esente_iva_intento(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.bollo_virtuale, False)
        self.assertEqual(fattura.importo_bollo, Decimal('0.00'))
        esenzione = EsenzioneIva.objects.get(pk=1)
        esenzione.natura_esenzione_sdi = 'N3.5'
        esenzione.save()
        fattura.esenzione_iva = esenzione
        fattura.save()
        self.assertEqual(fattura.bollo_virtuale, True)
        self.assertEqual(fattura.importo_bollo, Decimal('2.00'))

    def test_fattura_esente_iva_intento_sotto_soglia_importo(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.bollo_virtuale, False)
        self.assertEqual(fattura.importo_bollo, Decimal('0.00'))
        esenzione = EsenzioneIva.objects.get(pk=1)
        esenzione.natura_esenzione_sdi = 'N3.5'
        esenzione.save()
        fattura.esenzione_iva = esenzione
        fattura.save()
        self.assertEqual(fattura.bollo_virtuale, True)
        self.assertEqual(fattura.importo_bollo, Decimal('2.00'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga = elenco_righe[0]
        riga.costo_unitario = Decimal('1.00')
        riga.quantita = Decimal('1.00')
        riga.save()
        riga = elenco_righe[1]
        riga.costo_unitario = Decimal('1.00')
        riga.quantita = Decimal('1.00')
        riga.save()
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('2.00'))
        self.assertEqual(fattura.iva, Decimal('0.00'))
        self.assertEqual(fattura.bollo_virtuale, False)
        self.assertEqual(fattura.importo_bollo, Decimal('0.00'))

    def test_fattura_esente_iva_intento_sotto_soglia_bollo_custom(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.bollo_virtuale, False)
        self.assertEqual(fattura.importo_bollo, Decimal('0.00'))
        esenzione = EsenzioneIva.objects.get(pk=1)
        esenzione.natura_esenzione_sdi = 'N3.5'
        esenzione.save()
        fattura.esenzione_iva = esenzione
        fattura.importo_bollo = Decimal('2.50')
        fattura.save()
        self.assertEqual(fattura.bollo_virtuale, True)
        self.assertEqual(fattura.importo_bollo, Decimal('2.50'))
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga = elenco_righe[0]
        riga.costo_unitario = Decimal('1.00')
        riga.quantita = Decimal('1.00')
        riga.save()
        riga = elenco_righe[1]
        riga.costo_unitario = Decimal('1.00')
        riga.quantita = Decimal('1.00')
        riga.save()
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('2.00'))
        self.assertEqual(fattura.iva, Decimal('0.00'))
        self.assertEqual(fattura.bollo_virtuale, True)
        self.assertEqual(fattura.importo_bollo, Decimal('2.50'))

    def test_fattura_arrotondamento(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga = elenco_righe[0]
        riga.percentuale_iva = 21
        riga.costo_unitario = Decimal('4.90')
        riga.quantita = Decimal('145.00')
        riga.save()
        riga = elenco_righe[1]
        riga.delete()
        fattura = Fattura.objects.all()[0]
        self.assertEqual(fattura.imponibile, Decimal('710.50'))
        self.assertEqual(fattura.iva, Decimal('149.21'))
        self.assertEqual(fattura.totale, Decimal('859.71'))

    def test_fattura_arrotondamento_iva(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        self.assertEqual(elenco_righe.count(), 2)
        riga = elenco_righe[0]
        riga.percentuale_iva = 21
        riga.costo_unitario = Decimal('4.90')
        riga.quantita = Decimal('185.00')
        riga.save()
        riga = elenco_righe[1]
        riga.delete()
        fattura = Fattura.objects.all()[0]
        self.assertEqual(fattura.imponibile, Decimal('906.50'))
        self.assertEqual(fattura.iva, Decimal('190.37'))
        self.assertEqual(fattura.totale, Decimal('1096.87'))

    def test_fattura_arrotondamento_traffico_1(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        fattura.data = date(month=7, year=2012, day=18)
        fattura.save()
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        elenco_righe.delete()
        self.assertEqual(elenco_righe.count(), 0)
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('11.074')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='RICARICO GESTIONE VoIP PROVIDER 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('1.107')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 1 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('136.156')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='RICARICO GESTIONE VoIP PROVIDER 1 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('13.616')
        )
        nuova_riga.save()
        fattura = Fattura.objects.all()[0]
        self.assertEqual(fattura.imponibile, Decimal('161.96'))
        self.assertEqual(fattura.iva, Decimal('34.01'))
        self.assertEqual(fattura.totale, Decimal('195.97'))
        fattura.data = date(month=6, year=2012, day=17)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('161.95'))
        self.assertEqual(fattura.iva, Decimal('34.01'))
        self.assertEqual(fattura.totale, Decimal('195.96'))

    def test_fattura_arrotondamento_traffico_2(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        fattura.data = date(month=7, year=2012, day=18)
        fattura.save()
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        elenco_righe.delete()
        self.assertEqual(elenco_righe.count(), 0)
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('4.805')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='RICARICO GESTIONE VoIP PROVIDER 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('0.480')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 1 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('219.277')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='RICARICO GESTIONE VoIP PROVIDER 1 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('21.928')
        )
        nuova_riga.save()
        fattura = Fattura.objects.all()[0]
        self.assertEqual(fattura.imponibile, Decimal('246.50'))
        self.assertEqual(fattura.iva, Decimal('51.77'))
        self.assertEqual(fattura.totale, Decimal('298.27'))
        fattura.data = date(month=6, year=2012, day=17)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('246.49'))
        self.assertEqual(fattura.iva, Decimal('51.76'))
        self.assertEqual(fattura.totale, Decimal('298.25'))

    def test_fattura_arrotondamento_traffico_3(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        fattura.data = date(month=7, year=2012, day=18)
        fattura.save()
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        elenco_righe.delete()
        self.assertEqual(elenco_righe.count(), 0)
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('1.366')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='RICARICO GESTIONE VoIP PROVIDER 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('0.137')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 1 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('197.054')
        )
        nuova_riga.save()
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='RICARICO GESTIONE VoIP PROVIDER 1 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('19.705')
        )
        nuova_riga.save()
        fattura = Fattura.objects.all()[0]
        self.assertEqual(fattura.imponibile, Decimal('218.27'))
        self.assertEqual(fattura.iva, Decimal('45.84'))
        self.assertEqual(fattura.totale, Decimal('264.11'))
        fattura.data = date(month=6, year=2012, day=17)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('218.26'))
        self.assertEqual(fattura.iva, Decimal('45.84'))
        self.assertEqual(fattura.totale, Decimal('264.10'))

    def test_fattura_arrotondamento_traffico_quarta_cifra(self):
        elenco_fatture = Fattura.objects.all()
        self.assertEqual(elenco_fatture.count(), 1)
        fattura = elenco_fatture[0]
        fattura.data = date(month=7, year=2012, day=18)
        fattura.save()
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        elenco_righe.delete()
        self.assertEqual(elenco_righe.count(), 0)
        elenco_righe = RigaFattura.objects.filter(fattura=fattura)
        nuova_riga = RigaFattura.objects.create(
            fattura=fattura,
            quantita=Decimal('1.00'), contabile=True,
            descrizione='TRAFFICO VoIP Provider 2 relativo a 4/2012',
            percentuale_iva=21, costo_unitario=Decimal('218.26')
        )
        nuova_riga.save()
        fattura = Fattura.objects.all()[0]
        self.assertEqual(fattura.imponibile, Decimal('218.26'))
        self.assertEqual(fattura.iva, Decimal('45.83'))
        self.assertEqual(fattura.totale, Decimal('264.09'))
        fattura.data = date(month=6, year=2012, day=17)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('218.26'))
        self.assertEqual(fattura.iva, Decimal('45.83'))
        self.assertEqual(fattura.totale, Decimal('264.09'))

    def test_aggiorna_abicab_cliente_senza_abicab(self):
        cliente = Anagrafica.objects.create(ragione_sociale='Cliente test')
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.abi, '')
        self.assertEqual(fattura.cab, '')
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26),
            abi='54321', cab='56789'
        )
        self.assertEqual(fattura.abi, '54321')
        self.assertEqual(fattura.cab, '56789')
        # update
        Fattura.objects.filter(pk=fattura.pk).update(abi='11111', cab='22222')
        fattura = Fattura.objects.get(pk=fattura.pk)
        self.assertEqual(fattura.abi, '11111')
        self.assertEqual(fattura.cab, '22222')
        # update
        fattura.abi = '33333'
        fattura.cab = '44444'
        fattura.save()
        self.assertEqual(fattura.abi, '33333')
        self.assertEqual(fattura.cab, '44444')

    def test_aggiorna_abicab_cliente_con_abicab(self):
        cliente = Anagrafica.objects.create(
            ragione_sociale='Cliente test', abi='99999', cab='88888',
            banca='Unicredit'
        )
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.abi, '99999')
        self.assertEqual(fattura.cab, '88888')
        self.assertEqual(fattura.banca_cliente, 'Unicredit')
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26),
            abi='54321', cab='56789', banca_cliente='Bper'
        )
        self.assertEqual(fattura.abi, '54321')
        self.assertEqual(fattura.cab, '56789')
        self.assertEqual(fattura.banca_cliente, 'Unicredit')
        # update
        Fattura.objects.filter(pk=fattura.pk).update(abi='11111', cab='22222')
        fattura = Fattura.objects.get(pk=fattura.pk)
        self.assertEqual(fattura.abi, '11111')
        self.assertEqual(fattura.cab, '22222')
        # update
        fattura.abi = '33333'
        fattura.cab = '44444'
        fattura.save()
        self.assertEqual(fattura.abi, '33333')
        self.assertEqual(fattura.cab, '44444')
        # update without abi and cab
        fattura.abi = ''
        fattura.cab = ''
        fattura.save()
        self.assertEqual(fattura.abi, '99999')
        self.assertEqual(fattura.cab, '88888')

    def test_aggiorna_dati_rid(self):
        cliente = Anagrafica.objects.create(
            ragione_sociale='Cliente test', abi_rid='99999', cab_rid='88888',
            banca_rid='Unicredit', iban_rid='1234567890123456'
        )
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.iban_rid, '1234567890123456')
        self.assertEqual(fattura.abi_rid, '67890')
        self.assertEqual(fattura.cab_rid, '12345')
        self.assertEqual(fattura.banca_cliente_rid, 'Unicredit')
        fattura = Fattura.objects.create(
            cliente=cliente,
            data=date(year=2011, month=10, day=26), abi_rid='54321',
            cab_rid='56789', banca_cliente_rid='Bper'
        )
        self.assertEqual(fattura.abi_rid, '54321')
        self.assertEqual(fattura.cab_rid, '56789')
        self.assertEqual(fattura.banca_cliente_rid, 'Unicredit')
        # update
        Fattura.objects.filter(pk=fattura.pk).update(abi_rid='11111', cab_rid='22222')
        fattura = Fattura.objects.get(pk=fattura.pk)
        self.assertEqual(fattura.abi_rid, '11111')
        self.assertEqual(fattura.cab_rid, '22222')
        # update
        fattura.abi_rid = '33333'
        fattura.cab_rid = '44444'
        fattura.save()
        self.assertEqual(fattura.abi_rid, '33333')
        self.assertEqual(fattura.cab_rid, '44444')
        # update without abi_rid and cab_rid
        fattura.abi_rid = ''
        fattura.cab_rid = ''
        fattura.save()
        self.assertEqual(fattura.abi_rid, '67890')
        self.assertEqual(fattura.cab_rid, '12345')

    def test_aggiorna_banca_appoggio_con_banca_default(self):
        banca_default = BancaAppoggio.objects.get(pk=1)
        banca_traffico = BancaAppoggio.objects.get(pk=2)
        cliente = Anagrafica.objects.create(
            ragione_sociale='Cliente test',
            banca_appoggio=banca_default,
            banca_appoggio_traffico=banca_traffico
        )
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.banca_appoggio, banca_default)
        self.assertEqual(fattura.tipo_pagamento_gamma, None)
        fattura_traffico = Fattura.objects.create(
            cliente=cliente,
            data=date(year=2010, month=11, day=16), traffico_voip=True
        )
        self.assertEqual(fattura_traffico.banca_appoggio, banca_traffico)
        self.assertEqual(fattura.tipo_pagamento_gamma, None)

    def test_aggiorna_tipo_pagamento_con_pagamento_default(self):
        pagamento_default = TipoPagamentoGamma.objects.create(nome='riba 30ggfmdf')
        pagamento_traffico = TipoPagamentoGamma.objects.create(nome='rid 30/60/90ggfmdf')
        cliente = Anagrafica.objects.create(
            ragione_sociale='Cliente test',
            pagamento_default_fatture=pagamento_default,
            pagamento_default_traffico_voip=pagamento_traffico
        )
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.tipo_pagamento_gamma, pagamento_default)
        self.assertEqual(fattura.banca_appoggio, None)
        fattura_traffico = Fattura.objects.create(
            cliente=cliente,
            data=date(year=2010, month=11, day=16), traffico_voip=True
        )
        self.assertEqual(fattura_traffico.tipo_pagamento_gamma, pagamento_traffico)
        self.assertEqual(fattura.banca_appoggio, None)

    def test_invio_elettronico(self):
        # No email_fattura
        cliente = Anagrafica.objects.create(ragione_sociale='Test')
        self.assertEqual(cliente.email_fatturazione, '')
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.invio_elettronico, False)
        # Email fattura
        cliente.email_fatturazione = '<EMAIL>'
        cliente.save()
        fattura = Fattura.objects.create(
            cliente=cliente, data=date(year=2011, month=10, day=26)
        )
        self.assertEqual(fattura.invio_elettronico, True)

    def test_delete(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.delete()

    def test_get_righe_fattura_raggruppate(self):
        fattura = Fattura.objects.get(pk=1)
        righe = fattura.get_righe_fattura()
        self.assertEqual(righe.count(), 2)
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 2)
        self.assertEqual(righe_raggruppate[0]['seriale'], [])
        RigaFattura.objects.filter(pk=1).update(seriale='29091985')
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 2)
        self.assertEqual(righe_raggruppate[0]['descrizione'], 'Canone di assistenza dal 15/01/2010')
        self.assertEqual(righe_raggruppate[0]['quantita'], '1')
        self.assertEqual(righe_raggruppate[0]['seriale'], ['29091985'])

        self.assertEqual(righe_raggruppate[1]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[1]['quantita'], '1')
        RigaFattura.objects.filter(pk=2).update(seriale='25011988')
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 2)
        self.assertEqual(righe_raggruppate[1]['seriale'], ['25011988'])
        # create some new RigaFattura
        riga = RigaFattura.objects.create(
            descrizione='Snom 300', fattura=fattura,
            costo_unitario=Decimal('100.54'), percentuale_iva=21, order=3
        )
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 2)
        self.assertEqual(righe_raggruppate[1]['seriale'], ['25011988'])
        riga.seriale = '151721'
        riga.save()
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 2)
        self.assertEqual(righe_raggruppate[1]['seriale'], ['25011988', '151721'])
        self.assertEqual(righe_raggruppate[1]['quantita'], '2')
        # try to change costo unitario
        riga.costo_unitario = Decimal("1111")
        riga.save()
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 3)
        self.assertEqual(righe_raggruppate[0]['quantita'], '1')
        self.assertEqual(righe_raggruppate[1]['quantita'], '1')
        self.assertEqual(righe_raggruppate[2]['quantita'], '1')
        self.assertEqual(righe_raggruppate[0]['totale_riga'], Decimal("1390.00"))
        self.assertEqual(righe_raggruppate[1]['totale_riga'], Decimal("100.54"))
        self.assertEqual(righe_raggruppate[2]['totale_riga'], Decimal("1111"))
        self.assertEqual(righe_raggruppate[0]['seriale'], ['29091985'])
        self.assertEqual(righe_raggruppate[1]['seriale'], ['25011988'])
        self.assertEqual(righe_raggruppate[2]['seriale'], ['151721'])
        riga = RigaFattura.objects.create(
            descrizione='Snom 300', fattura=fattura,
            costo_unitario=Decimal('1111'), seriale='1735112011',
            quantita=Decimal('2.67'), percentuale_iva=21, order=4
        )
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 3)
        self.assertEqual(righe_raggruppate[2]['seriale'], ['151721', '1735112011'])
        self.assertEqual(righe_raggruppate[2]['quantita'], '3,67')
        RigaFattura.objects.create(
            descrizione='Snom 300', fattura=fattura,
            costo_unitario=Decimal('1111'), seriale='1111111111',
            percentuale_iva=21, order=5
        )
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 3)
        self.assertEqual(righe_raggruppate[0]['costo'], Decimal('1390.00'))
        self.assertEqual(righe_raggruppate[1]['costo'], Decimal('100.54'))
        self.assertEqual(righe_raggruppate[2]['costo'], Decimal('1111.00'))
        self.assertEqual(righe_raggruppate[0]['totale_riga'], Decimal('1390.00'))
        self.assertEqual(righe_raggruppate[1]['totale_riga'], Decimal('100.54'))
        self.assertEqual(righe_raggruppate[2]['totale_riga'], Decimal('5188.37'))
        self.assertEqual(righe_raggruppate[2]['seriale'], ['151721', '1735112011', '1111111111'])
        self.assertEqual(righe_raggruppate[2]['quantita'], '4,67')
        # change descrizione
        riga.descrizione = 'Snom 301'
        riga.quantita = Decimal('1.00')
        riga.save()
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 5)
        self.assertEqual(righe_raggruppate[0]['costo'], Decimal('1390.00'))
        self.assertEqual(righe_raggruppate[1]['costo'], Decimal('100.54'))
        self.assertEqual(righe_raggruppate[2]['costo'], Decimal('1111.00'))
        self.assertEqual(righe_raggruppate[2]['descrizione'], u'Snom 300')
        self.assertEqual(righe_raggruppate[3]['descrizione'], u'Snom 301')
        self.assertEqual(righe_raggruppate[4]['descrizione'], u'Snom 300')
        self.assertEqual(righe_raggruppate[2]['seriale'], [u'151721'])
        self.assertEqual(righe_raggruppate[3]['seriale'], [u'1735112011'])
        self.assertEqual(righe_raggruppate[4]['seriale'], [u'1111111111'])
        # change contabile
        riga.descrizione = 'Snom 300'
        riga.contabile = False
        riga.costo_unitario = None
        riga.percentuale_iva = None
        riga.save()
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 5)
        self.assertEqual(righe_raggruppate[2]['contabile'], True)
        self.assertEqual(righe_raggruppate[3]['contabile'], False)
        self.assertEqual(righe_raggruppate[4]['contabile'], True)
        self.assertEqual(righe_raggruppate[2]['totale_riga'], Decimal('1111.00'))
        self.assertEqual(righe_raggruppate[3]['totale_riga'], Decimal('0.0'))
        self.assertEqual(righe_raggruppate[4]['totale_riga'], Decimal("1111"))
        self.assertEqual(righe_raggruppate[2]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[3]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[4]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[2]['seriale'], ['151721'])
        self.assertEqual(righe_raggruppate[3]['seriale'], ['1735112011'])
        self.assertEqual(righe_raggruppate[4]['seriale'], ['1111111111'])
        # set non contabile and quantita None (necessity explained by Simone)
        riga.quantita = None
        riga.save()
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 5)
        self.assertEqual(righe_raggruppate[2]['contabile'], True)
        self.assertEqual(righe_raggruppate[3]['contabile'], False)
        self.assertEqual(righe_raggruppate[4]['contabile'], True)
        self.assertEqual(righe_raggruppate[2]['totale_riga'], Decimal('1111.00'))
        self.assertEqual(righe_raggruppate[3]['totale_riga'], Decimal('0.0'))
        self.assertEqual(righe_raggruppate[4]['totale_riga'], Decimal("1111"))
        self.assertEqual(righe_raggruppate[2]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[3]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[4]['descrizione'], 'Snom 300')
        self.assertEqual(righe_raggruppate[2]['quantita'], '1')
        self.assertEqual(righe_raggruppate[3]['quantita'], '')
        self.assertEqual(righe_raggruppate[4]['quantita'], '1')
        self.assertEqual(righe_raggruppate[2]['seriale'], ['151721'])
        self.assertEqual(righe_raggruppate[3]['seriale'], ['1735112011'])
        self.assertEqual(righe_raggruppate[4]['seriale'], ['1111111111'])
        # change percentuale_iva
        riga.contabile = True
        riga.quantita = Decimal('1.00')
        riga.costo_unitario = Decimal('40.00')
        riga.percentuale_iva = 22
        riga.save()
        righe_raggruppate = fattura.get_righe_fattura_raggruppate()
        self.assertEqual(len(righe_raggruppate), 5)
        self.assertEqual(righe_raggruppate[2]['percentuale_iva'], 21)
        self.assertEqual(righe_raggruppate[3]['percentuale_iva'], 22)
        self.assertEqual(righe_raggruppate[4]['percentuale_iva'], 21)
        self.assertEqual(righe_raggruppate[2]['seriale'], ['151721'])
        self.assertEqual(righe_raggruppate[3]['seriale'], ['1735112011'])
        self.assertEqual(righe_raggruppate[4]['seriale'], ['1111111111'])

    def test_indirizzi_no_anagrafica(self):
        fattura = Fattura.objects.create(
            ragione_sociale='Capuozzo Inc',
            indirizzo='Via Roma, 32',
            citta='Siracusa',
            provincia='SI',
            cap='12345',
        )
        # Intestatario
        righe = fattura.get_indirizzo().splitlines()
        self.assertEqual(righe[0], 'Capuozzo Inc')
        self.assertEqual(righe[1], 'Via Roma, 32')
        self.assertEqual(righe[2], '12345 Siracusa (SI)')
        # Destinazione documento
        righe = fattura.destinazione_documento.splitlines()
        self.assertEqual(righe[0], 'Capuozzo Inc')
        self.assertEqual(righe[1], 'Via Roma, 32')
        self.assertEqual(righe[2], '12345 Siracusa (SI)')

    def test_indirizzi_sede_legale(self):
        anagrafica = Anagrafica.objects.get(pk=1)
        anagrafica.destinazione_documenti = None
        anagrafica.save()
        fattura = Fattura.objects.create(cliente=anagrafica)
        # Intestatario
        righe = fattura.get_indirizzo().splitlines()
        self.assertEqual(righe[0], 'Spett. Mastertraining')
        self.assertEqual(righe[1], 'Via Timolini 18')
        self.assertEqual(righe[2], '42015 Correggio (RE)')
        # Destinazione documento
        righe = fattura.destinazione_documento.splitlines()
        self.assertEqual(righe[0], 'Spett. Mastertraining')
        self.assertEqual(righe[1], 'Via Timolini 18')
        self.assertEqual(righe[2], '42015 - Correggio (RE)')

    def test_indirizzi_sede_operativa(self):
        anagrafica = Anagrafica.objects.get(pk=1)
        anagrafica.destinazione_documenti = Sede.objects.get(pk=11)
        anagrafica.save()
        fattura = Fattura.objects.create(cliente=anagrafica)
        # Intestatario
        righe = fattura.get_indirizzo().splitlines()
        self.assertEqual(righe[0], 'Spett. Mastertraining')
        self.assertEqual(righe[1], 'Via Timolini 18')
        self.assertEqual(righe[2], '42015 Correggio (RE)')
        # Destinazione documento
        righe = fattura.destinazione_documento.splitlines()
        self.assertEqual(righe[0], 'Spett. Mastertraining')
        self.assertEqual(righe[1], 'Via Sani 9')
        self.assertEqual(righe[2], '42100 - Reggio Emilia (RE)')

    def test_indirizzi_titolo(self):
        anagrafica = Anagrafica.objects.get(pk=1)
        anagrafica.titolo = 'Egr.'
        anagrafica.destinazione_documenti = Sede.objects.get(pk=11)
        anagrafica.save()
        fattura = Fattura.objects.create(cliente=anagrafica)
        # Intestatario
        righe = fattura.get_indirizzo().splitlines()
        self.assertEqual(righe[0], 'Egr. Mastertraining')
        self.assertEqual(righe[1], 'Via Timolini 18')
        self.assertEqual(righe[2], '42015 Correggio (RE)')
        # Destinazione documento
        righe = fattura.destinazione_documento.splitlines()
        self.assertEqual(righe[0], 'Egr. Mastertraining')
        self.assertEqual(righe[1], 'Via Sani 9')
        self.assertEqual(righe[2], '42100 - Reggio Emilia (RE)')

    def test_altra_sede_riferimento_amministrazione(self):
        anagrafica = AnagraficaFactory(riferimento_amministrazione='#12345#')
        fattura = Fattura.objects.create(cliente=anagrafica)
        self.assertEqual(fattura.riferimento_amministrazione, '#12345#')
        altra_sede = SedeFactory(anagrafica=anagrafica, riferimento_amministrazione='#54678#')
        fattura.sede_cliente = altra_sede
        fattura.ragione_sociale = None
        fattura.riferimento_amministrazione = None
        fattura.save()
        self.assertEqual(fattura.riferimento_amministrazione, '#54678#')

    def test_altra_sede_riferimento_amministrazione_2(self):
        anagrafica = AnagraficaFactory(riferimento_amministrazione='#12345#')
        altra_sede = SedeFactory(anagrafica=anagrafica, riferimento_amministrazione='#54678#')
        fattura = Fattura.objects.create(cliente=anagrafica, sede_cliente=altra_sede)
        self.assertEqual(fattura.riferimento_amministrazione, '#54678#')

    def test_altra_sede_pagamento(self):
        pagamento_gamma_1 = TipoPagamentoGammaFactory()
        pagamento_gamma_2 = TipoPagamentoGammaFactory()
        anagrafica = AnagraficaFactory(pagamento_default_fatture=pagamento_gamma_1)
        altra_sede = SedeFactory(anagrafica=anagrafica, pagamento_default_fatture=pagamento_gamma_2)
        fattura = Fattura.objects.create(cliente=anagrafica, sede_cliente=altra_sede)
        self.assertEqual(fattura.tipo_pagamento_gamma, pagamento_gamma_2)

    def test_altra_sede_banca_appoggio(self):
        banca_1 = BancaAppoggioFactory()
        banca_2 = BancaAppoggioFactory()
        anagrafica = AnagraficaFactory(banca_appoggio=banca_1)
        altra_sede = SedeFactory(anagrafica=anagrafica, banca_appoggio=banca_2)
        fattura = Fattura.objects.create(cliente=anagrafica, sede_cliente=altra_sede)
        self.assertEqual(fattura.banca_appoggio, banca_2)

    def test_altra_sede_iban(self):
        anagrafica = AnagraficaFactory(
            iban='IT1234567890',
            banca='BPER',
        )
        altra_sede = SedeFactory(
            anagrafica=anagrafica,
            iban='DE9876543210',
            banca='CAVOLA',
        )
        fattura = Fattura.objects.create(cliente=anagrafica, sede_cliente=altra_sede)
        self.assertEqual(fattura.iban, 'DE9876543210')
        self.assertEqual(fattura.banca_cliente, 'CAVOLA')

    def test_altra_sede_iban_rid(self):
        anagrafica = AnagraficaFactory(
            iban_rid='DE9876543210',
            banca_rid='BPER',
        )
        altra_sede = SedeFactory(
            anagrafica=anagrafica,
            iban_rid='IT1234567890',
            banca_rid='CREDEM',
        )
        fattura = Fattura.objects.create(cliente=anagrafica, sede_cliente=altra_sede)
        self.assertEqual(fattura.iban_rid, 'IT1234567890')
        self.assertEqual(fattura.banca_cliente_rid, 'CREDEM')


class TestScadenzaPagamentoModel(TestCase):
    fixtures = ['anagrafe_test', 'fatturazione_test']

    def test_scadenza_pagamento(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.totale, Decimal('1803.55'))
        self.assertEqual(fattura.scadenze_complete, True)
        # Elimino la seconda scadenza
        ScadenzaPagamento.objects.get(pk=2).delete()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.scadenze_complete, False)
        # Se non e' presente l'importo della scadenza viene usato il totale
        # fattura
        scadenza = ScadenzaPagamento.objects.get(pk=1)
        scadenza.importo = None
        scadenza.save()
        scadenza = ScadenzaPagamento.objects.get(pk=1)
        self.assertEqual(scadenza.importo, fattura.totale)
        self.assertEqual(fattura.scadenze_complete, False)
        rimessa_diretta = CodiceScaglionePagamento.objects.get(nome='rimessa diretta')
        scadenza = ScadenzaPagamento.objects.all()[0]
        scadenza.codice_scaglione_pagamento = rimessa_diretta
        scadenza.data = None
        scadenza.save()
        self.assertEqual(scadenza.data, fattura.data)
        trenta_giorni_fine_mese = CodiceScaglionePagamento.objects.get(nome='30ggfmdf')
        scadenza = ScadenzaPagamento.objects.all()[0]
        scadenza.codice_scaglione_pagamento = trenta_giorni_fine_mese
        scadenza.data = None
        scadenza.save()
        self.assertEqual(scadenza.data, date(2011, 8, 31))
        sessanta_giorni = CodiceScaglionePagamento.objects.get(nome='60gg')
        scadenza = ScadenzaPagamento.objects.all()[0]
        scadenza.codice_scaglione_pagamento = sessanta_giorni
        scadenza.data = None
        scadenza.save()
        self.assertEqual(scadenza.data, date(2011, 9, 22))
        trenta_giorni_fine_mese_piu_15 = CodiceScaglionePagamento.objects.get(nome='30ggfmdf')
        trenta_giorni_fine_mese_piu_15.giorni_oltre_fine_mese = 15
        trenta_giorni_fine_mese_piu_15.save()
        scadenza = ScadenzaPagamento.objects.all()[0]
        scadenza.codice_scaglione_pagamento = trenta_giorni_fine_mese_piu_15
        scadenza.data = None
        scadenza.save()
        self.assertEqual(scadenza.data, date(2011, 9, 15))

    def test_scadenza_pagamento_split_payment(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.totale, Decimal('1803.55'))
        self.assertEqual(fattura.scadenze_complete, True)
        # Elimino la seconda scadenza
        ScadenzaPagamento.objects.get(pk=2).delete()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.scadenze_complete, False)
        # Se non e' presente l'importo della scadenza viene usato il totale
        # fattura
        fattura.split_payment = True
        fattura.save()
        scadenza = ScadenzaPagamento.objects.get(pk=1)
        scadenza.importo = None
        scadenza.save()
        scadenza = ScadenzaPagamento.objects.get(pk=1)
        self.assertEqual(scadenza.importo, fattura.imponibile)
        self.assertEqual(fattura.scadenze_complete, False)

    def test_scadenza_pagamento_bassa(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.save()
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.totale, Decimal('1803.55'))
        self.assertEqual(fattura.scadenze_complete, True)
        # Abbasso l'importo della seconda scadenza
        scadenza = ScadenzaPagamento.objects.get(pk=2)
        self.assertEqual(scadenza.importo, Decimal('100.00'))
        scadenza.importo = Decimal('90.00')
        scadenza.save()
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.imponibile, Decimal('1490.54'))
        self.assertEqual(fattura.iva, Decimal('313.01'))
        self.assertEqual(fattura.totale, Decimal('1803.55'))
        self.assertEqual(fattura.scadenze_complete, False)

    def test_get_pagamenti(self):
        fattura = Fattura.objects.get(pk=1)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'RID - Rimessa Diretta')
        ScadenzaPagamento.objects.filter(pk=2).update(tipo_pagamento=None)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'Rimessa Diretta')
        ScadenzaPagamento.objects.all().update(tipo_pagamento=4)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'Rimessa Diretta')
        ScadenzaPagamento.objects.all().update(tipo_pagamento=1)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'RIBA')
        ScadenzaPagamento.objects.all().update(tipo_pagamento=2)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'RID')
        ScadenzaPagamento.objects.all().update(tipo_pagamento=6)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'N/A')
        ScadenzaPagamento.objects.all().update(tipo_pagamento=None)
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'N/A')
        ScadenzaPagamento.objects.all().delete()
        pagamenti = fattura.get_pagamenti()
        self.assertEqual(pagamenti, 'N/A')

    def test_totale_scadenze_alto(self):
        fattura = Fattura.objects.get(pk=1)
        ScadenzaPagamento.objects.all().delete()
        ScadenzaPagamento.objects.create(
            fattura=fattura, data=date(2011, 11, 10), importo=Decimal('1000.00')
        )
        self.assertRaises(
            ValidationError, ScadenzaPagamento.objects.create,
            fattura=fattura, importo=Decimal('803.56'),
            storno=Decimal('120.00')
        )

    def test_storno_1_scadenza(self):
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.storno, None)
        ScadenzaPagamento.objects.all().delete()
        ScadenzaPagamento.objects.create(
            fattura=fattura, importo=Decimal('100.00'), storno=Decimal('5.00')
        )
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.storno, Decimal('5.00'))

    def test_storno_2_scadenze(self):
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.storno, None)
        ScadenzaPagamento.objects.all().delete()
        ScadenzaPagamento.objects.create(
            fattura=fattura, importo=Decimal('100.00'), storno=Decimal('5.00')
        )
        ScadenzaPagamento.objects.create(
            fattura=fattura, importo=Decimal('100.00'), storno=Decimal('3.00')
        )
        fattura = Fattura.objects.get(pk=1)
        self.assertEqual(fattura.storno, Decimal('8.00'))

    def test_storno_superiore_ad_importo(self):
        fattura = Fattura.objects.get(pk=1)
        ScadenzaPagamento.objects.all().delete()
        self.assertRaises(
            ValidationError, ScadenzaPagamento.objects.create,
            fattura=fattura, importo=Decimal('100.00'),
            storno=Decimal('120.00')
        )


class TestScadenzaPagamentoAbbinamentoModel(TestCase):
    fixtures = ['anagrafe_test', 'fatturazione_test']

    def setUp(self):
        from mastergest.incassi.models import Incasso
        fattura = Fattura.objects.get(pk=1)
        ScadenzaPagamento.objects.all().delete()
        self.scadenza = ScadenzaPagamento.objects.create(
            id=1, fattura=fattura, importo=Decimal('100.00'),
            storno=Decimal('10.00')
        )
        self.incasso = Incasso.objects.create(
            importo=Decimal('200.00'), data=date(2012, 1, 1)
        )

    def test_alzo_storno_ok(self):
        from mastergest.incassi.models import Abbinamento
        Abbinamento.objects.create(
            id=1, incasso=self.incasso, scadenza=self.scadenza,
            importo=Decimal('80.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.scadenza.storno = Decimal('20.00')
        self.scadenza.save()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)

    def test_alzo_storno_ko(self):
        from mastergest.incassi.models import Abbinamento
        Abbinamento.objects.create(
            id=1, incasso=self.incasso, scadenza=self.scadenza,
            importo=Decimal('80.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
        self.scadenza.storno = Decimal('25.00')
        self.assertRaises(ValidationError, self.scadenza.save)
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)

    def test_abbasso_storno(self):
        from mastergest.incassi.models import Abbinamento
        Abbinamento.objects.create(
            id=1, incasso=self.incasso, scadenza=self.scadenza,
            importo=Decimal('90.00')
        )
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, True)
        self.scadenza.storno = Decimal('5.00')
        self.scadenza.save()
        self.assertEqual(ScadenzaPagamento.objects.get(pk=1).pagata, False)
