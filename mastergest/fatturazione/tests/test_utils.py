# -*- coding: utf-8 -*-
from datetime import date

from django.core import mail
from django.test import TestCase

from mastergest.anagrafe.tests.factories import StatoFactory
from mastergest.fatturazione.models import Fattura
from mastergest.fatturazione.utils import send_fattura
from mastergest.fatturazione.tests.factories import FatturaFactory, RigaFatturaFactory
from mastergest.fatturazione.tests.factories import EsenzioneIvaFactory
from mastergest.fatturazione.utils import get_xml_fattura


class TestMail(TestCase):
    fixtures = [
        'anagrafe_test',
        'fatturazione_test',
    ]

    def setUp(self):
        mail.outbox = []

    def test_ok(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = 'don<PERSON><PERSON>@example.com'
        fattura.cliente.save()
        send_fattura(fattura)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1 del 22/07/2011')
        self.assertEqual(
            message.recipients(),
            ['<PERSON><PERSON><PERSON>@example.com', '<EMAIL>']
        )

    def test_no_recipients(self):
        fattura = Fattura.objects.get(pk=1)
        self.assertRaises(RuntimeError, send_fattura, fattura)

    def test_destinatario_custom(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.numero = 1234
        fattura.save()
        send_fattura(fattura, recipients=['<EMAIL>'])
        self.assertEqual(len(mail.outbox), 1)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1234 del 22/07/2011')
        self.assertEqual(
            message.recipients(),
            ['<EMAIL>', '<EMAIL>']
        )

    def test_email_cc(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = '<EMAIL>'
        fattura.cliente.email_fatturazione_cc = '<EMAIL>'
        fattura.cliente.save()
        send_fattura(fattura)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1 del 22/07/2011')
        self.assertEqual(
            message.recipients(),
            ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        )

    def test_solo_email_cc(self):
        fattura = Fattura.objects.get(pk=1)
        fattura.cliente.email_fatturazione = ''
        fattura.cliente.email_fatturazione_cc = '<EMAIL>'
        fattura.cliente.save()
        fattura.save()
        self.assertEqual(fattura.invio_elettronico, True)
        send_fattura(fattura)
        message = mail.outbox[0]
        self.assertEqual(message.subject, 'Fattura n. 1 del 22/07/2011')
        self.assertEqual(
            message.recipients(),
            ['<EMAIL>', '<EMAIL>']
        )


class TestXml(TestCase):

    def test_no_codice(self):
        fattura = FatturaFactory()
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(fattura_xml, None)
        self.assertTrue(msg.startswith('Manca Codice PA del Cliente'))

    def test_no_cap(self):
        fattura = FatturaFactory()
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        fattura.cliente.codice_pa = 'A123456'
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(fattura_xml, None)
        self.assertEqual(msg, 'ERRORE: la fattura n.1 non ha il CAP')

    def test_ok(self):
        fattura = FatturaFactory()
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        fattura.cap = '12345'
        fattura.citta = 'San Nicolo\' sull\' Adige'
        fattura.cliente.codice_pa = 'A123456'
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(msg, '')
        self.assertIn('Simon &amp; Simon', fattura_xml)
        self.assertIn('Via dell&#39;Adige/Etschweg 15', fattura_xml)
        self.assertIn('San Nicolo&#39; sull&#39; Adige', fattura_xml)

    def test_xml_con_riga_fattura(self):
        fattura = FatturaFactory()
        descrizione_riga = 'Adesione al servizio di manutenzione e assistenza tecnica per piattaforma MasterVoice valida dal 01/01/2019 al 31/12/2019 Sede di Cortaccia  (BZ) sita in Via dell’Adige/Etschweg 15'
        RigaFatturaFactory(
            fattura=fattura, descrizione=descrizione_riga
        )
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        fattura.cap = '12345'
        fattura.citta = 'San Nicolo\' sull\' Adige'
        fattura.cliente.codice_pa = 'A123456'
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(msg, '')
        self.assertIn('Simon &amp; Simon', fattura_xml)
        self.assertIn('Via dell&#39;Adige/Etschweg 15', fattura_xml)
        self.assertIn('San Nicolo&#39; sull&#39; Adige', fattura_xml)
        self.assertIn(
            'Sede di Cortaccia  (BZ) sita in Via dell&#39;Adige/Etschweg 15',
            fattura_xml
        )
        self.assertIn(
            'Adesione al servizio di manutenzione e assistenza tecnica per piattaforma MasterVoice valida dal 01/01/2019 al 31/12/2019',
            fattura_xml
        )

    def test_stato(self):
        stato = StatoFactory(codice='DE', descrizione='Germania')
        fattura = FatturaFactory()
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        fattura.cap = '12345'
        fattura.citta = 'San Nicolo\' sull\' Adige'
        fattura.cliente.codice_pa = 'A123456'
        fattura.partita_iva = '1234567890'
        fattura.stato = stato
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(msg, '')
        self.assertIn('Simon &amp; Simon', fattura_xml)
        self.assertIn('Via dell&#39;Adige/Etschweg 15', fattura_xml)
        self.assertIn('San Nicolo&#39; sull&#39; Adige', fattura_xml)
        self.assertIn('<IdPaese>DE</IdPaese>', fattura_xml)
        self.assertIn('<Nazione>DE</Nazione>', fattura_xml)

    def test_xml_con_esenzione_custom(self):
        esenzione_iva = EsenzioneIvaFactory()
        esenzione_iva.descrizione = 'Esente IVA art. 8/C del D.P.R. 633/72 - Rif. ns. Prot. n. 4/2019 del 19/12/2018 - Rif. Vs. Prot. n. 176/2019 - Data emissione dichiarazione 17/12/2018'
        esenzione_iva.natura_esenzione_sdi = 'N5'
        esenzione_iva.save()
        fattura = FatturaFactory()
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        fattura.cap = '12345'
        fattura.citta = 'San Nicolo\' sull\' Adige'
        fattura.cliente.codice_pa = 'A123456'
        fattura.numero = 7843
        fattura.data = date(2018, 8, 12)
        fattura.esenzione_iva = esenzione_iva
        fattura.save()
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(msg, '')
        self.assertIn(
            'Esente IVA art. 8/C del D.P.R. 633/72 - Rif. ns. Prot. n. 4/2019 del 19/12/2018 - Rif. Vs. Prot. n..',
            fattura_xml
        )
        self.assertIn('7843/2018', fattura_xml)
        self.assertIn('<Natura>N5</Natura>', fattura_xml)

    def test_xml_con_esenzione(self):
        esenzione_iva = EsenzioneIvaFactory()
        fattura = FatturaFactory()
        fattura.ragione_sociale = 'Simon & Simon'
        fattura.indirizzo = 'Via dell’Adige/Etschweg 15'
        fattura.cap = '12345'
        fattura.citta = 'San Nicolo\' sull\' Adige'
        fattura.cliente.codice_pa = 'A123456'
        fattura.numero = 7843
        fattura.data = date(2018, 8, 12)
        fattura.esenzione_iva = esenzione_iva
        fattura.save()
        msg, fattura_xml = get_xml_fattura(fattura, 'A0001')
        self.assertEqual(msg, '')
        self.assertIn('<Natura>N4</Natura>', fattura_xml)
