# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion
import datetime


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BancaAppoggio',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('codice', models.CharField(max_length=50)),
                ('nome', models.CharField(max_length=200)),
                ('abi', models.CharField(max_length=5, null=True, blank=True)),
                ('cab', models.CharField(max_length=5, null=True, blank=True)),
                ('conto', models.CharField(max_length=50, null=True, blank=True)),
                ('iban', models.CharField(max_length=50)),
            ],
            options={
                'ordering': ('codice',),
                'verbose_name_plural': 'banche appoggio',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='CodiceScaglionePagamento',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nome', models.CharField(max_length=50)),
                ('giorni', models.PositiveIntegerField(default=0, verbose_name=b'giorni dilazione', blank=True)),
                ('mesi', models.PositiveIntegerField(default=0, verbose_name=b'mesi dilazione', blank=True)),
                ('anni', models.PositiveIntegerField(default=0, verbose_name=b'anni dilazione', blank=True)),
                ('fine_mese', models.BooleanField(default=True, verbose_name=b'fine mese')),
                ('giorni_oltre_fine_mese', models.PositiveIntegerField(default=0, verbose_name=b'giorni oltre fine mese', blank=True)),
            ],
            options={
                'ordering': ('mesi', 'anni'),
                'verbose_name_plural': 'codici scaglioni pagamenti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='EsenzioneIva',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nome', models.CharField(max_length=50)),
                ('descrizione', models.CharField(max_length=200)),
                ('codice_gamma', models.CharField(max_length=50, null=True, blank=True)),
            ],
            options={
                'ordering': ('nome',),
                'verbose_name_plural': 'esenzioni iva',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Fattura',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('data', models.DateField(default=datetime.date.today)),
                ('numero', models.PositiveIntegerField(db_index=True, null=True, blank=True)),
                ('postfisso', models.CharField(max_length=50, null=True, verbose_name=b'postfisso (bis)', blank=True)),
                ('ragione_sociale', models.CharField(db_index=True, max_length=200, null=True, blank=True)),
                ('partita_iva', models.CharField(max_length=200, blank=True)),
                ('codice_fiscale', models.CharField(max_length=50, blank=True)),
                ('indirizzo', models.CharField(max_length=200, blank=True)),
                ('citta', models.CharField(max_length=200, blank=True)),
                ('provincia', models.CharField(max_length=50, blank=True)),
                ('cap', models.CharField(max_length=50, blank=True)),
                ('destinazione_documento', models.TextField(blank=True)),
                ('ritenuta_dacconto', models.DecimalField(null=True, verbose_name="ritenuta d'acconto (\u20ac)", max_digits=9, decimal_places=2, blank=True)),
                ('omaggio', models.BooleanField(default=False)),
                ('iva_esigibilita_differita', models.BooleanField(default=False, verbose_name=b"IVA ad esigibilita' differita")),
                ('note', models.TextField(blank=True)),
                ('invio_elettronico', models.BooleanField(default=False)),
                ('inviata', models.BooleanField(default=False)),
                ('inviata_pa', models.BooleanField(default=False)),
                ('data_anticipo', models.DateField(null=True, verbose_name=b'data anticipo', blank=True)),
                ('scaricata', models.BooleanField(default=False)),
                ('accompagnatoria', models.BooleanField(default=False)),
                ('vettore', models.CharField(max_length=50, blank=True)),
                ('aspetto_esteriore', models.CharField(max_length=50, verbose_name=b'aspetto esteriore', blank=True)),
                ('numero_colli', models.CharField(max_length=50, verbose_name=b'numero colli', blank=True)),
                ('peso', models.CharField(max_length=50, blank=True)),
                ('telefono', models.CharField(max_length=200, blank=True)),
                ('anno', models.PositiveIntegerField(editable=False, db_index=True)),
                ('anno_numero', models.CharField(max_length=50, editable=False, db_index=True)),
                ('imponibile', models.DecimalField(default=0, verbose_name='imponibile (\u20ac)', max_digits=9, decimal_places=2)),
                ('iva', models.DecimalField(default=0, verbose_name='totale iva (\u20ac)', max_digits=9, decimal_places=2)),
                ('totale', models.DecimalField(default=0, verbose_name='totale fattura (\u20ac)', max_digits=9, decimal_places=2)),
                ('scadenze_complete', models.BooleanField(default=False, db_index=True, editable=False)),
                ('pagata', models.BooleanField(default=False, db_index=True, editable=False)),
                ('storno', models.DecimalField(null=True, editable=False, max_digits=9, decimal_places=2, blank=True)),
                ('iban', models.CharField(max_length=50, blank=True)),
                ('abi', models.CharField(max_length=5, blank=True)),
                ('cab', models.CharField(max_length=5, blank=True)),
                ('banca_cliente', models.CharField(max_length=200, blank=True)),
                ('iban_rid', models.CharField(max_length=50, verbose_name=b'Iban', blank=True)),
                ('abi_rid', models.CharField(max_length=5, verbose_name=b'Abi', blank=True)),
                ('cab_rid', models.CharField(max_length=5, verbose_name=b'Cab', blank=True)),
                ('banca_cliente_rid', models.CharField(max_length=200, verbose_name=b'Banca cliente', blank=True)),
                ('note_pagamento', models.TextField(verbose_name=b'note sul pagamento', blank=True)),
                ('traffico_voip', models.BooleanField(default=False)),
                ('codice_invio_xml', models.CharField(max_length=10, null=True, blank=True)),
                ('cig', models.CharField(max_length=15, null=True, blank=True)),
                ('cup', models.CharField(max_length=15, null=True, blank=True)),
                ('numero_documento_ordine', models.CharField(max_length=20, null=True, verbose_name=b"Num. buono d'ordine", blank=True)),
                ('split_payment', models.BooleanField(default=False)),
                ('bollo_virtuale', models.BooleanField(default=False)),
                ('importo_bollo', models.DecimalField(default=0, verbose_name='importo bollo (\u20ac)', max_digits=9, decimal_places=2)),
                ('banca_appoggio', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='banca_appoggio_pk', blank=True, to='fatturazione.BancaAppoggio', null=True)),
                ('cliente', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='anagrafe.Anagrafica', null=True)),
                ('esenzione_iva', models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='fatturazione.EsenzioneIva', null=True)),
            ],
            options={
                'ordering': ('-anno_numero',),
                'verbose_name_plural': 'fatture',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='PianoContiGamma',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nome', models.CharField(max_length=255)),
                ('codice', models.CharField(max_length=50)),
            ],
            options={
                'ordering': ('nome',),
                'verbose_name_plural': 'Piano dei conti Gamma',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='RigaFattura',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('seriale', models.CharField(max_length=50, blank=True)),
                ('descrizione', models.TextField(blank=True)),
                ('quantita', models.DecimalField(default=1, null=True, max_digits=9, decimal_places=2, blank=True)),
                ('costo_unitario', models.DecimalField(null=True, verbose_name='costo unitario (\u20ac)', max_digits=9, decimal_places=3, blank=True)),
                ('percentuale_iva', models.PositiveIntegerField(default=22, null=True, verbose_name=b'percentuale iva', blank=True)),
                ('contabile', models.BooleanField(default=True, verbose_name=b'contabile')),
                ('order', models.PositiveIntegerField(default=0)),
                ('fattura', models.ForeignKey(on_delete=models.deletion.CASCADE, to='fatturazione.Fattura')),
                ('piano_conti_gamma', models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='fatturazione.PianoContiGamma', null=True)),
            ],
            options={
                'ordering': ('fattura', 'order', 'id'),
                'verbose_name_plural': 'righe fattura',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ScadenzaPagamento',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('data', models.DateField(null=True, blank=True)),
                ('importo', models.DecimalField(null=True, verbose_name='importo (\u20ac)', max_digits=9, decimal_places=2, blank=True)),
                ('storno', models.DecimalField(null=True, max_digits=9, decimal_places=2, blank=True)),
                ('pagata', models.BooleanField(default=False, db_index=True, editable=False)),
                ('codice_scaglione_pagamento', models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='fatturazione.CodiceScaglionePagamento', null=True)),
                ('fattura', models.ForeignKey(on_delete=models.deletion.CASCADE, to='fatturazione.Fattura')),
            ],
            options={
                'ordering': ('-data', '-fattura'),
                'verbose_name_plural': 'scadenze pagamenti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ScaglionePagamentoTemplate',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('percentuale_importo', models.PositiveIntegerField(default=100)),
                ('codice_scaglione_pagamento', models.ForeignKey(on_delete=models.deletion.CASCADE, to='fatturazione.CodiceScaglionePagamento')),
            ],
            options={
                'ordering': ('tipo_pagamento', 'codice_scaglione_pagamento'),
                'verbose_name_plural': 'Template scaglioni pagamento',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='TipoPagamento',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nome', models.CharField(max_length=50)),
            ],
            options={
                'verbose_name_plural': 'Tipi Pagamento',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='TipoPagamentoGamma',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nome', models.CharField(max_length=255)),
                ('codice', models.CharField(max_length=50, verbose_name=b'codice gamma')),
            ],
            options={
                'ordering': ('nome',),
                'verbose_name_plural': "Modalita' di pagamento",
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='scaglionepagamentotemplate',
            name='modalita_pagamento',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='fatturazione.TipoPagamentoGamma'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='scaglionepagamentotemplate',
            name='tipo_pagamento',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='fatturazione.TipoPagamento'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='scadenzapagamento',
            name='tipo_pagamento',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='fatturazione.TipoPagamento', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='fattura',
            name='tipo_pagamento_gamma',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='fatturazione.TipoPagamentoGamma', null=True),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name='fattura',
            unique_together=set([('anno', 'numero', 'postfisso')]),
        ),
        migrations.CreateModel(
            name='FatturaScadenze',
            fields=[
            ],
            options={
                'ordering': ('-anno_numero',),
                'proxy': True,
                'verbose_name_plural': 'Associa Pagamenti GAMMA',
            },
            bases=('fatturazione.fattura',),
        ),
        migrations.CreateModel(
            name='RigaPianoContiGamma',
            fields=[
            ],
            options={
                'ordering': ('fattura', 'descrizione'),
                'proxy': True,
                'verbose_name_plural': 'Associa Piano Conti Gamma',
            },
            bases=('fatturazione.rigafattura',),
        ),
    ]
