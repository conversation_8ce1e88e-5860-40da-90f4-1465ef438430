# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-01 08:43
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0011_auto_20190506_1731'),
    ]

    operations = [
        migrations.AlterField(
            model_name='codicescaglionepagamento',
            name='anni',
            field=models.PositiveIntegerField(blank=True, default=0, verbose_name='anni dilazione'),
        ),
        migrations.AlterField(
            model_name='codicescaglionepagamento',
            name='fine_mese',
            field=models.BooleanField(default=True, verbose_name='fine mese'),
        ),
        migrations.AlterField(
            model_name='codicescaglionepagamento',
            name='giorni',
            field=models.PositiveIntegerField(blank=True, default=0, verbose_name='giorni dilazione'),
        ),
        migrations.AlterField(
            model_name='codicescaglionepagamento',
            name='giorni_oltre_fine_mese',
            field=models.PositiveIntegerField(blank=True, default=0, verbose_name='giorni oltre fine mese'),
        ),
        migrations.AlterField(
            model_name='codicescaglionepagamento',
            name='mesi',
            field=models.PositiveIntegerField(blank=True, default=0, verbose_name='mesi dilazione'),
        ),
        migrations.AlterField(
            model_name='esenzioneiva',
            name='natura_esenzione_sdi',
            field=models.CharField(choices=[('N1', 'N1 escluse ex art. 15'), ('N2', 'N2 non soggette'), ('N3', 'N3 non imponibili'), ('N4', 'N4 esenti'), ('N5', 'N5 regime del margine / IVA non esposta in fattura'), ('N6', 'N6 inversione contabile (per le operazioni in reverse charge ovvero nei casi di autofatturazione per acquisti extra UE di servizi ovvero per importazioni di beni nei soli casi previsti)'), ('N7', 'N7 IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies lett. f, g, art. 74-sexies DPR 633/72)')], default='N4', max_length=50),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='abi_rid',
            field=models.CharField(blank=True, max_length=5, verbose_name='Abi'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='aspetto_esteriore',
            field=models.CharField(blank=True, max_length=50, verbose_name='aspetto esteriore'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='banca_cliente_rid',
            field=models.CharField(blank=True, max_length=200, verbose_name='Banca cliente'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='cab_rid',
            field=models.CharField(blank=True, max_length=5, verbose_name='Cab'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='data_anticipo',
            field=models.DateField(blank=True, null=True, verbose_name='data anticipo'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='iban_rid',
            field=models.CharField(blank=True, max_length=50, verbose_name='Iban'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='inviata_pa',
            field=models.BooleanField(default=False, verbose_name='inviata SDI'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='iva_esigibilita_differita',
            field=models.BooleanField(default=False, verbose_name="IVA ad esigibilita' differita"),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='note_fattura',
            field=models.TextField(blank=True, verbose_name='note in fattura'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='note_pagamento',
            field=models.TextField(blank=True, verbose_name='note sul pagamento'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='numero_colli',
            field=models.CharField(blank=True, max_length=50, verbose_name='numero colli'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='numero_documento_ordine',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name="Num. buono d'ordine"),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='postfisso',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='postfisso (bis)'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='stato_invio_sdi',
            field=models.CharField(blank=True, choices=[('nuova', 'Creata'), ('firmata', 'Fattura Firmata'), ('inviata', 'Inviata'), ('attestazione_trasmissione', 'Inviata con imp. di recapito'), ('scartata', 'Fattura Scartata'), ('mancata_consegna', 'Mancata Consegna'), ('consegnata', 'Consegnata'), ('rifiutata', 'Fatt. RIFIUTATA'), ('accettata', 'Fatt. ACCETTATA'), ('decorrenza_termini', 'Decorrenza Termini')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='rigafattura',
            name='contabile',
            field=models.BooleanField(default=True, verbose_name='contabile'),
        ),
        migrations.AlterField(
            model_name='rigafattura',
            name='percentuale_iva',
            field=models.PositiveIntegerField(blank=True, default=22, null=True, verbose_name='percentuale iva'),
        ),
        migrations.AlterField(
            model_name='tipopagamentogamma',
            name='codice',
            field=models.CharField(max_length=50, verbose_name='codice gamma'),
        ),
    ]
