# Generated by Django 2.2.28 on 2024-04-22 07:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0022_auto_20221011_0851'),
    ]

    operations = [
        migrations.CreateModel(
            name='PianoContiAttivo',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('fatturazione.pianocontigamma',),
        ),
        migrations.CreateModel(
            name='PianoContiCosti',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('fatturazione.pianocontigamma',),
        ),
        migrations.AlterModelOptions(
            name='pianocontigamma',
            options={'ordering': ('nome',), 'verbose_name_plural': 'Piano dei conti (Gamma)'},
        ),
        migrations.AddField(
            model_name='pianocontigamma',
            name='attivo',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='pianocontigamma',
            name='natura_conto',
            field=models.CharField(blank=True, choices=[('patrimoniale', 'PATRIMONIALE'), ('liquidita', "LIQUIDITA'"), ('economico', 'ECONOMICO'), ('costi', 'COSTO'), ('ricavi', 'RICAVO'), ('ordine', "D'ORDINE")], max_length=200, null=True),
        ),
    ]
