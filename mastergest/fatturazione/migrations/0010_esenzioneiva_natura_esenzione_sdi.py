# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0009_fattura_stato'),
    ]

    operations = [
        migrations.AddField(
            model_name='esenzioneiva',
            name='natura_esenzione_sdi',
            field=models.CharField(default=b'N4', max_length=50, choices=[(b'N1', b'escluse ex art. 15'), (b'N2', b'non soggette'), (b'N3', b'non imponibili'), (b'N4', b'esenti'), (b'N5', b'regime del margine / IVA non esposta in fattura'), (b'N6', b'inversione contabile (per le operazioni in reverse charge ovvero nei casi di autofatturazione per acquisti extra UE di servizi ovvero per importazioni di beni nei soli casi previsti)'), (b'N7', b'IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies lett. f, g, art. 74-sexies DPR 633/72)')]),
        ),
    ]
