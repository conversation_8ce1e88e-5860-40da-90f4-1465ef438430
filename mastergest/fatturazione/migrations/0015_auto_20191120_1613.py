# Generated by Django 2.2.7 on 2019-11-20 15:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0014_auto_20191008_0939'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fattura',
            name='banca_appoggio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_pk', to='fatturazione.BancaAppoggio'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='cliente',
            field=models.ForeignKey(blank=True, limit_choices_to={'attivo': True, 'cliente': True, 'tipo_cliente': 'cliente'}, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagrafe.Anagrafica'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='esenzione_iva',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fatturazione.EsenzioneIva'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='stato',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Stato'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='tipo_pagamento_gamma',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fatturazione.TipoPagamentoGamma'),
        ),
        migrations.AlterField(
            model_name='rigafattura',
            name='piano_conti_gamma',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fatturazione.PianoContiGamma'),
        ),
        migrations.AlterField(
            model_name='rigafattura',
            name='sede_cliente',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Sede'),
        ),
        migrations.AlterField(
            model_name='scadenzapagamento',
            name='codice_scaglione_pagamento',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fatturazione.CodiceScaglionePagamento'),
        ),
        migrations.AlterField(
            model_name='scadenzapagamento',
            name='tipo_pagamento',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fatturazione.TipoPagamento'),
        ),
        migrations.AlterField(
            model_name='scaglionepagamentotemplate',
            name='codice_scaglione_pagamento',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fatturazione.CodiceScaglionePagamento'),
        ),
        migrations.AlterField(
            model_name='scaglionepagamentotemplate',
            name='modalita_pagamento',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fatturazione.TipoPagamentoGamma'),
        ),
        migrations.AlterField(
            model_name='scaglionepagamentotemplate',
            name='tipo_pagamento',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fatturazione.TipoPagamento'),
        ),
    ]
