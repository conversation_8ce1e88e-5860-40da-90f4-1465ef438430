# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-08 07:39
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0013_merge_20191008_0939'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fattura',
            name='cambio_divisa',
            field=models.DecimalField(blank=True, decimal_places=6, help_text='Valore in Euro della divisa selezionata al cambio attuale: es. CHF 0.84 -> 1 CHF = 0.84 Euro', max_digits=9, null=True, verbose_name='Cambio Attuale Divisa (EUR)'),
        ),
        migrations.AlterField(
            model_name='fattura',
            name='divisa',
            field=models.CharField(choices=[('EUR', 'EUR - Euro'), ('CHF', 'CHF - Franco Svizzero')], default='EUR', max_length=200),
        ),
    ]
