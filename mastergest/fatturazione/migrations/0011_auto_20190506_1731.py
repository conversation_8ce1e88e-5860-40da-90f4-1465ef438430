# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-05-06 15:31


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0010_esenzioneiva_natura_esenzione_sdi'),
    ]

    operations = [
        migrations.AlterField(
            model_name='esenzioneiva',
            name='natura_esenzione_sdi',
            field=models.CharField(choices=[(b'N1', b'N1 escluse ex art. 15'), (b'N2', b'N2 non soggette'), (b'N3', b'N3 non imponibili'), (b'N4', b'N4 esenti'), (b'N5', b'N5 regime del margine / IVA non esposta in fattura'), (b'N6', b'N6 inversione contabile (per le operazioni in reverse charge ovvero nei casi di autofatturazione per acquisti extra UE di servizi ovvero per importazioni di beni nei soli casi previsti)'), (b'N7', b'N7 IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies lett. f, g, art. 74-sexies DPR 633/72)')], default=b'N4', max_length=50),
        ),
    ]
