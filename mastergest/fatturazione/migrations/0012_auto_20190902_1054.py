# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-09-02 08:54
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0011_auto_20190506_1731'),
    ]

    operations = [
        migrations.AddField(
            model_name='fattura',
            name='cambio_divisa',
            field=models.DecimalField(blank=True, decimal_places=6, help_text=b'Valore in Euro della divisa selezionata al cambio attuale: es. CHF 0.84 -> 1 CHF = 0.84 Euro', max_digits=9, null=True, verbose_name=b'Cambio Attuale Divisa (EUR)'),
        ),
        migrations.AddField(
            model_name='fattura',
            name='divisa',
            field=models.CharField(choices=[(b'EUR', b'EUR - Euro'), (b'CHF', b'CHF - Franco S<PERSON>zzero')], default=b'EUR', max_length=200),
        ),
    ]
