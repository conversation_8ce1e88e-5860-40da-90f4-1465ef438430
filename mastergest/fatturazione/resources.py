from import_export import resources

from mastergest.fatturazione.models import Fattura, RigaFattura


class FatturaResource(resources.ModelResource):

    class Meta:
        model = Fattura
        fields = (
            'numero', 'data', 'ragione_sociale', 'partita_iva', 'cig',
            'imponibile', 'iva', 'totale',
        )


class RigaFatturaResource(resources.ModelResource):

    class Meta:
        model = RigaFattura
        fields = (
            'fattura', 'seriale', 'descrizione', 'quantita', 'costo_unitario',
            'percentuale_iva', 'contabile', 'piano_conti_gamma', 'sede_cliente'
        )
