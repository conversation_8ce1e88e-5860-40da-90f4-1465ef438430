from mastergest.utils.widgets import LargeAutocompleteWidget
from mastergest.fatturazione.models import PianoContiGamma, PianoContiAttivo
from mastergest.fatturazione.models import PianoContiCosti, PianoContiRicavi


class PianoContiGammaWidget(LargeAutocompleteWidget):
    model = PianoContiGamma


class PianoContiAttivoWidget(LargeAutocompleteWidget):
    model = PianoContiAttivo


class PianoContiCostiWidget(LargeAutocompleteWidget):
    model = PianoContiCosti


class PianoContiRicaviWidget(LargeAutocompleteWidget):
    model = PianoContiRicavi
