[{"pk": 4, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 0, "anni": 0, "nome": "pagamento immediato"}}, {"pk": 10, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 0, "anni": 0, "nome": "pagamento immediato fine mese"}}, {"pk": 1, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 1, "anni": 0, "nome": "30ggfmdf"}}, {"pk": 6, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 1, "anni": 0, "nome": "30gg"}}, {"pk": 2, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 2, "anni": 0, "nome": "60ggfmdf"}}, {"pk": 7, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 2, "anni": 0, "nome": "60gg"}}, {"pk": 9, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 3, "anni": 0, "nome": "90gg"}}, {"pk": 3, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 3, "anni": 0, "nome": "90ggfmdf"}}, {"pk": 8, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 4, "anni": 0, "nome": "120gg"}}, {"pk": 5, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 4, "anni": 0, "nome": "120ggfmdf"}}, {"pk": 3, "model": "fatturazione.bancaappoggio", "fields": {"codice": "CAVOLA", "nome": "Banca di Cavola e Sassuolo - Ag. di Reggio Emilia (RE)", "abi": "", "conto": "", "iban": "***************************", "cab": ""}}, {"pk": 1, "model": "fatturazione.bancaappoggio", "fields": {"codice": "BPER", "nome": "Banca Popolare dell'Emilia Romagna - Ag. Di Correggio (RE)", "abi": "", "conto": "", "iban": "***************************", "cab": ""}}, {"pk": 2, "model": "fatturazione.bancaappoggio", "fields": {"codice": "BPV", "nome": "Banca Popolare Di Verona - Ag.di Castelnovo Ne Monti (RE)", "abi": "", "conto": "", "iban": "***************************", "cab": ""}}, {"pk": 4, "model": "fatturazione.bancaappoggio", "fields": {"codice": "UNICREDIT", "nome": "Banca UNICREDIT - Ag.di Castelnovo Ne Monti (RE)", "abi": "", "conto": "", "iban": "***************************", "cab": ""}}, {"pk": 5, "model": "fatturazione.bancaappoggio", "fields": {"codice": "CA.RI.CE", "nome": "CA.RI.CE - Ag. di Reggio Emilia (RE)", "abi": "", "conto": "", "iban": "***************************", "cab": ""}}, {"pk": 1, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "IVA ESENTE ai sensi dell'art.10 del D.P.R. 26/10/1972, n°633", "nome": "Esente IVA Art.10"}}, {"pk": 3, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "OPERAZIONE 'non imponibile' ai sensi dell'art.41 DL 331/93 (con il recepimento nell'ordinamento nazionale delle integrazioni e modifiche alla Direttiva IVA 2006/112/CE )", "nome": "Esente IVA Art.41c"}}, {"pk": 2, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "IVA ESENTE ai sensi dell'art.8/1c del D.P.R. 26/10/1972, n°633", "nome": "Esente IVA Art.8/1c"}}, {"pk": 5, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "Esente IVA art. 8 del D.P.R. 633/72. Rif. ns. Prot. n. 1/2011 - Rif. Vs. Prot. n. 417/2011", "nome": "Esente IVA Cadoro"}}, {"pk": 7, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "Operazione non imponibile Iva ai sensi dell'art. 8, 2° comma del D.P.R. 633/72 come da Vs. Dichiarazione D'Intento n. 194/ANNO 2011", "nome": "Esente IVA Permasteelisa - 194/2011"}}, {"pk": 8, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "Operazione non imponibile Iva ai sensi dell'art. 8, 2° comma del D.P.R. 633/72 come da Vs. Dichiarazione D'Intento n. 206/ANNO 2010", "nome": "Esente IVA Permasteelisa - 206/2010"}}, {"pk": 6, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "Operazione non imponibile ai sensi dell'Art. 8 lettera A DPR 633/72 e successive modificazioni ed integrazioni", "nome": "Esente IVA Permasteelisa GMEL"}}, {"pk": 4, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "IVA NON IMPONIBILE", "nome": "IVA NON IMPONIBILE"}}, {"pk": 1, "model": "fatturazione.tipopagamento", "fields": {"nome": "RIBA"}}, {"pk": 2, "model": "fatturazione.tipopagamento", "fields": {"nome": "RID"}}, {"pk": 4, "model": "fatturazione.tipopagamento", "fields": {"nome": "RIMESSA DIRETTA"}}, {"pk": 3, "model": "fatturazione.tipopagamento", "fields": {"nome": "BONIFICO"}}, {"pk": 5, "model": "fatturazione.tipopagamento", "fields": {"nome": "VEDI NOTE"}}]