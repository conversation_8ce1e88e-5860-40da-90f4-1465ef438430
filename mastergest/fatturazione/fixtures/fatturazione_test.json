[{"pk": 4, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 0, "anni": 0, "nome": "<PERSON><PERSON>a diretta"}}, {"pk": 1, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 1, "anni": 0, "nome": "30ggfmdf"}}, {"pk": 6, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 1, "anni": 0, "nome": "30gg"}}, {"pk": 2, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 2, "anni": 0, "nome": "60ggfmdf"}}, {"pk": 7, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": false, "giorni": 0, "mesi": 2, "anni": 0, "nome": "60gg"}}, {"pk": 3, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 3, "anni": 0, "nome": "90ggfmdf"}}, {"pk": 5, "model": "fatturazione.codicescaglionepagamento", "fields": {"fine_mese": true, "giorni": 0, "mesi": 3, "anni": 0, "nome": "120ggfmdf"}}, {"pk": 1, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "IVA ESENTE ai sensi dell'art.10 del D.P.R. 26/10/1972, n°633", "nome": "Esente IVA Art.10"}}, {"pk": 3, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "OPERAZIONE 'non imponibile' ai sensi dell'art.41 DL 331/93 (con il recepimento nell'ordinamento nazionale delle integrazioni e modifiche alla Direttiva IVA 2006/112/CE )", "nome": "Esente IVA Art.41c"}}, {"pk": 2, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "IVA ESENTE ai sensi dell'art.8/1c del D.P.R. 26/10/1972, n°633", "nome": "Esente IVA Art.8/1c"}}, {"pk": 4, "model": "fatturazione.esenzioneiva", "fields": {"descrizione": "IVA NON IMPONIBILE", "nome": "IVA NON IMPONIBILE"}}, {"pk": 1, "model": "fatturazione.tipopagamento", "fields": {"nome": "RIBA"}}, {"pk": 2, "model": "fatturazione.tipopagamento", "fields": {"nome": "RID"}}, {"pk": 3, "model": "fatturazione.tipopagamento", "fields": {"nome": "Bonifico Bancario"}}, {"pk": 4, "model": "fatturazione.tipopagamento", "fields": {"nome": "<PERSON><PERSON><PERSON>"}}, {"pk": 1, "model": "fatturazione.bancaappoggio", "fields": {"codice": "BPER", "nome": "Banca Popolare dell'Emilia Romagna", "abi": "12340", "conto": "0000123456", "iban": "12345675685685698569", "cab": "32456"}}, {"pk": 2, "model": "fatturazione.bancaappoggio", "fields": {"codice": "BPV", "nome": "Banca Popolare Di Verona", "abi": "12340", "conto": "0000123456", "iban": "236666666666666666666636", "cab": "32456"}}, {"pk": 1, "model": "fatturazione.fattura", "fields": {"iva": "313.01", "codice_fiscale": "", "imponibile": "1490.54", "citta": "<PERSON><PERSON><PERSON><PERSON>", "omaggio": false, "indirizzo": "<PERSON>, 90", "provincia": "SS", "note": "", "ragione_sociale": "ALBERGHIERO (SS)", "banca_appoggio": null, "esenzione_iva": null, "totale": "1803.55", "telefono": "0789 82179-82139", "numero": "1", "note_pagamento": "", "iban": "", "partita_iva": "", "data": "2011-07-22", "anno": 2011, "cap": "07021", "banca_cliente": "", "cliente": 4}}, {"pk": 1, "model": "fatturazione.rigafattura", "fields": {"fattura": 1, "seriale": "", "quantita": "1.00", "percentuale_iva": 21, "descrizione": "Canone di assistenza dal 15/01/2010", "contabile": true, "costo_unitario": "1390.00", "order": 1}}, {"pk": 2, "model": "fatturazione.rigafattura", "fields": {"fattura": 1, "seriale": "", "quantita": "1.00", "percentuale_iva": 21, "descrizione": "Snom 300", "contabile": true, "costo_unitario": "100.54", "order": 2}}, {"pk": 1, "model": "fatturazione.scadenzapagamento", "fields": {"codice_scaglione_pagamento": 4, "tipo_pagamento": 4, "fattura": 1, "importo": "1703.55", "data": "2011-09-27"}}, {"pk": 2, "model": "fatturazione.scadenzapagamento", "fields": {"codice_scaglione_pagamento": 6, "tipo_pagamento": 2, "fattura": 1, "importo": "100.00", "data": "2011-10-27"}}]