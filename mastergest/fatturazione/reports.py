import html
import os
from io import BytesIO
from django.conf import settings

from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.platypus import Table, TableStyle, Frame
from reportlab.platypus import LongTable, Image
from reportlab.platypus.flowables import PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm as centimetri
from reportlab.lib import colors
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate, NextPageTemplate

from mastergest import web
from mastergest.utils.currency import get_stringa_importo
from mastergest.utils.dates import calcola_data_scadenza
from mastergest.fatturazione.constants import IVA_DEFAULT
from mastergest.anagrafe.models import DATI_MASTERCOM, DATI_MASTERTRAINING


SIMBOLO_EURO = '\u20ac'

DIMENSIONE_LINEE = 0.8

HEADER_STYLE = [
    ('FONT', (1, 0), (1, 0), 'Times-Roman', 7),
    ('FONT', (-1, 0), (-1, 0), 'Times-Bold', 10),
    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ('ALIGN', (0, 0), (0, 0), 'CENTER'),
    ('ALIGN', (-1, -1), (-1, -1), 'RIGHT'),
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
]

INTESTAZIONE_STYLE = [
    ('FONT', (0, 0), (-1, -1), 'Times-Roman', 10),
    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
    ('GRID', (0, 0), (-1, -1), DIMENSIONE_LINEE, colors.black),
]

TOTALI_STYLE = [
    ('FONT', (0, 0), (0, -1), 'Times-Bold', 10),
    ('FONT', (1, 0), (1, -1), 'Times-Roman', 9),
    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ('ALIGN', (0, 0), (0, -1), 'CENTER'),
    ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
    ('GRID', (0, 0), (-1, -1), DIMENSIONE_LINEE, colors.black),
]

TOTALI_STYLE_2 = [
    ('FONT', (0, 0), (0, -1), 'Times-Roman', 8),
    ('FONT', (1, 0), (1, -1), 'Times-Roman', 7),
    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ('ALIGN', (0, 0), (0, -1), 'CENTER'),
    ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
    ('GRID', (0, 0), (-1, -1), DIMENSIONE_LINEE, colors.black),
]

FOOTER_STYLE = [
    ('FONT', (0, 0), (-1, -1), 'Times-Roman', 9),
    ('VALIGN', (0, 0), (0, -1), 'TOP'),
    ('VALIGN', (1, 0), (1, -1), 'MIDDLE'),
    ('LEFTPADDING', (1, 0), (1, -1), 0),
    ('RIGHTPADDING', (1, 0), (1, -1), 0),
    ('TOPPADDING', (1, 0), (1, -1), 0),
    ('BOTTOMPADDING', (1, 0), (1, -1), 0),
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
    ('GRID', (0, 0), (-1, -1), DIMENSIONE_LINEE, colors.black),
]

MORA_CONAI_STYLE = [
    ('FONT', (0, 0), (-1, -1), 'Times-Roman', 7),
    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
    ('GRID', (0, 0), (-1, -1), DIMENSIONE_LINEE, colors.black),
]

RIGHE_STYLE = [
    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
    ('FONT', (0, 1), (-1, -1), 'Times-Roman', 8),
    ('FONT', (0, 0), (-1, 0), 'Times-Bold', 10),
    ('ALIGN', (1, 0), (1, -1), 'CENTER'),
    ('ALIGN', (2, 0), (2, -1), 'RIGHT'),
    ('ALIGN', (-2, 0), (-2, -1), 'CENTER'),
    ('ALIGN', (-1, 0), (-1, -1), 'RIGHT'),
    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
]

# DIMENSIONI PAGINA
ALTEZZA_PAGINA = A4[1]
LARGHEZZA_PAGINA = A4[0]
BORDO_SINISTRO = 0.5 * centimetri
BORDO_DESTRO = 0.5 * centimetri
BORDO_SUPERIORE = 7.0 * centimetri
# BORDO_INFERIORE = 8.0 * centimetri
BORDO_INFERIORE = 5.0 * centimetri

# DIMENSIONI TESTATA
LARGHEZZA_LOGO = (LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO)) * 0.4
ALTEZZA_LOGO = 3.0 * centimetri
LARGHEZZA_DATI_AZIENDA = (LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO)) * 0.3
LARGHEZZA_DATI_DOCUMENTO = (LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO)) * 0.3
ALTEZZA_TESTATA = 8.0 * centimetri
INIZIO_BORDO_TESTATA = ALTEZZA_TESTATA + (1.0 * centimetri)

# DIMENSIONI COLONNE RIGHE
LARGHEZZA_QUANTITA = 1.0 * centimetri
LARGHEZZA_COSTO_UNITARIO = 2.0 * centimetri
LARGHEZZA_IVA = 1.0 * centimetri
LARGHEZZA_TOTALE = 2.5 * centimetri
LARGHEZZA_COLONNA_DESCRIZIONE = LARGHEZZA_PAGINA - \
                                (1.0 * centimetri) - \
                                (LARGHEZZA_QUANTITA + LARGHEZZA_COSTO_UNITARIO + LARGHEZZA_IVA + LARGHEZZA_TOTALE)
# DIMENSIONI FOOTER
LARGHEZZA_PAGAMENTI = 14.0 * centimetri
LARGHEZZA_TOTALI = LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO) - LARGHEZZA_PAGAMENTI

# DATI AZIENDA
def get_dati_testata(documento):
    if settings.AZIENDA == 'mastercom':
        nome_azienda = DATI_MASTERCOM['ragione_sociale']
        sede_operativa = 'Sede Operativa: Via San Martino, 11 Correggio'
        dati_telefonici = 'Telefono %s Fax. 0522/331673' % DATI_MASTERCOM['telefono']
        email = 'email: %s' % DATI_MASTERCOM['email_amministrazione']
        sede_legale = 'Sede Legale %s Correggio (RE)' % DATI_MASTERCOM['indirizzo_sede_legale']
        dati_registro_imprese = 'Reg.Imp. di RE, P.I.e C/F %s' % DATI_MASTERCOM['partita_iva']
        dati_capitale_sociale = 'Capitale Sociale %s 20.000 i.v. - REA di RE:%s' % (SIMBOLO_EURO, DATI_MASTERCOM['numero_iscrizione_rea'])
    else:
        if hasattr(documento, 'gestione_azienda') and documento.gestione_azienda:
            nome_azienda = documento.gestione_azienda_ragione_sociale
            sede_operativa = 'Sede Operativa: Via San Martino, 11 Correggio'
            dati_telefonici = 'Telefono %s Fax. 0522/331673' % (documento.gestione_azienda_telefono)
            email = 'email: %s' % (documento.gestione_azienda_email_info)
            sede_legale = 'Sede Legale %s Correggio (RE)' % (documento.gestione_azienda_indirizzo_sede_legale)
            dati_registro_imprese = 'Reg.Imp. di RE, P.I.e C/F %s' % (documento.gestione_azienda_partita_iva)
            dati_capitale_sociale = 'Capitale Sociale %s 20.000 i.v. - REA di RE:%s' % (SIMBOLO_EURO, documento.gestione_azienda_numero_iscrizione_rea)
        else:
            nome_azienda = DATI_MASTERTRAINING['ragione_sociale']
            sede_operativa = 'Sede Operativa: Via San Martino, 11 Correggio'
            dati_telefonici = 'Telefono %s Fax. 0522/331673' % DATI_MASTERTRAINING['telefono']
            email = 'email: %s' % DATI_MASTERTRAINING['email_info']
            sede_legale = 'Sede Legale %s Correggio (RE)' % DATI_MASTERTRAINING['indirizzo_sede_legale']
            dati_registro_imprese = 'Reg.Imp. di RE, P.I.e C/F %s' % DATI_MASTERTRAINING['partita_iva']
            dati_capitale_sociale = 'Capitale Sociale %s 20.000 i.v. - REA di RE:%s' % (SIMBOLO_EURO, DATI_MASTERTRAINING['numero_iscrizione_rea'])
    dati_testata = '%s\n%s\n%s\n%s\n%s\n%s\n%s' % (
        nome_azienda,
        sede_operativa,
        dati_telefonici,
        email,
        sede_legale,
        dati_registro_imprese,
        dati_capitale_sociale
    )
    return dati_testata

# FILE LOGO
path = os.path.dirname(web.__file__)
FILE_LOGO_MASTERTRAINING = os.path.join(path, 'static', 'img', 'logo_master.png')
FILE_LOGO_MAGISTER = os.path.join(path, 'static', 'img', 'logo_magister.png')
FILE_LOGO_MASTERCOM = os.path.join(path, 'static', 'img', 'logo_mastercom.png')

def get_file_logo(documento):
    if settings.AZIENDA == 'mastercom':
        return FILE_LOGO_MASTERCOM
    else:
        if hasattr(documento, 'gestione_azienda') and documento.gestione_azienda:
            # Se il documento ha un attributo gestione_azienda, usiamo quello
            if documento.gestione_azienda == 'magister':
                return FILE_LOGO_MAGISTER
        return FILE_LOGO_MASTERTRAINING
        
# DATI FOOTER
TESTO_INTERESSI_MORA = 'Note: Gli interessi di mora decorreranno automaticamente dal giorno successivo la scadenza del termine per il pagamento Ex Art.4, Comma 1, D.Lgs231/2002'
TESTO_CONAI = 'Contributo CONAI Assolto ove dovuto'
LARGHEZZA_CONAI = 5.5 * centimetri
LARGHEZZA_INTERESSI_MORA = LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO) - LARGHEZZA_CONAI
TESTO_OMAGGIO = 'Cessione di campione gratuito, campioni di modico valore appositamente contrassegnati.Esclusi iva art 2, comma 3, lettera d, del dpr 633/72'
TESTO_PRIVACY_BOLD = 'Informativa Privacy (art.13 D.Lgs.196/2003), semplificata ai sensi della Prescrizione del Garante del 19.06.2008:'
TESTO_PRIVACY_CORPO = "I dati acquisiti per la predisposizione del presente documento sono utilizzati per l'assolvimento degli obblighi contrattuali o di legge, esclusivamente per finalita' di ordine contabile (DPR n.600/73). Il trattamento verra' effettuato secondo le norme del D.Lgs. n.196/03, con l'ausilio di sistemi informatici e su supporto cartaceo. Tali dati potranno essere comunicati a terzi, nell'ambito delle specifiche attivita' istituzionali e professionali e non saranno comunque oggetto di diffusione. L'informativa redatta con modalita' piu' articolate e' disponibile presso la sede amministrativa della societa'. L'interessato potra' avvalersi dei diritti previsti dagli artt. 7-8-9-10 del medesimo decreto (diritto di accesso ai dati personali, esercizio dei diritti, modalita' di esercizio, riscontro dell'interessato), indirizzando richiesta scritta alla societa' scrivente, Titolare del trattamento, presso la sede amministrativa indicata."
TESTO_BOLLO_VIRTUALE = "* Imposta di bollo assolta in modo virtuale ai sensi dell’articolo 15 del d.p.r. 642/1972 e del DM 17/06/2014"

TESTO_FATTURE_FONIA = "Ai sensi del Regolamento in materia di qualità e carte dei servizi di comunicazioni da postazione fissa (Allegato A alla Delibera n. 156/23/CONS), Master Training S.r.l. pubblica sul proprio sito al seguente link: https://www.mastervoice.it/trasparenza-e-qualita/indicatori-di-qualita/, gli obiettivi prefissati annualmente per gli indicatori generali e specifici di qualità dei servizi di comunicazioni elettroniche da postazione fissa accessibili al pubblico e i risultati raggiunti."

DICITURA_TRAFFICO_1 = "Dal 1^ maggio 2023 gli utenti mantengono il diritto di trasferire il numero di telefono relativo ad un contratto cessato verso un altro fornitore per 60 (sessanta) giorni dalla data di risoluzione."

DICITURA_TRAFFICO_2 = "L'art. 4, Mantenimento del numero cessato, della Delibera 103/21/CIR e successiva modifica con la Delibera 8/22/CIR, prescrive che qualora l'utente risolva un contratto, lo stesso mantiene il diritto di trasferire il numero di telefono relativo al contratto cessato verso un altro fornitore per 60 (sessanta) giorni dalla data della risoluzione, a meno che non vi rinunci."

styles = getSampleStyleSheet()

DEBUG_MODE = False


class MyDocTemplate(SimpleDocTemplate):
    secondo_passaggio = False

    def _allSatisfied(self):
        if self.secondo_passaggio:
            return SimpleDocTemplate._allSatisfied(self)
        else:
            self.secondo_passaggio = True
            return 0


class DocumentoPDF(object):

    def disegna_paragrafo(self, testo, style=None):
        lines = [line.strip() for line in testo.split('\n') if line.strip()]
        if not style:
            style = ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=8,)
        return [
            Paragraph(
                html.escape(line),
                style)
            for line in lines
        ]


class FatturaPdf(DocumentoPDF):

    fattura = None
    scaglione_fisso = None
    detailStyle = getSampleStyleSheet()['Normal']
    total_pages = 0
    fatture_pagine = None
    pagine_totali = dict()

    def genera_pdf(self, fattura):
        self.fattura = fattura
        buffer = BytesIO()
        doc = MyDocTemplate(
            buffer,
            leftMargin=BORDO_SINISTRO,
            rightMargin=BORDO_DESTRO,
            bottomMargin=BORDO_INFERIORE,
            topMargin=BORDO_SUPERIORE,
            showBoundary=DEBUG_MODE,
        )
        Story = []
        t = self.disegna_righe(
            self.fattura.get_righe_fattura_raggruppate(), self.fattura.omaggio,
            self.fattura.divisa, self.fattura.cambio_divisa,
            self.fattura.cliente.mastervoice, self.fattura.traffico_voip
        )
        Story.append(t)
        doc.multiBuild(Story, onFirstPage=self.disegna_header_footer, onLaterPages=self.disegna_header_footer,)
        pdf = buffer.getvalue()
        buffer.close()
        return pdf
  
    def disegna_tabella_codici_migrazione(self, fattura):
        from mastergest.traffico.models import Account
        data = [('Sede Cliente', 'Numero Telefono', 'Codice Portabilità')]
        if fattura.sede_cliente:
            elenco_sedi = [fattura.sede_cliente, ]
        else:
            elenco_sedi = fattura.cliente.sede_set.all()
        elenco_account = Account.objects.filter(sede__in=elenco_sedi).order_by('sede', 'codice')
        for account in elenco_account:
            if account.codice_migrazione:
                data.append((account.sede, account.numeri_associati, account.codice_migrazione))
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ])
        colwidths = [150, 150, 150]
        t = LongTable(data, colWidths=colwidths)
        t.setStyle(style)
        return t

    def genera_pdf_massivo(self, elenco_fatture, scaglione_fisso=None):
        self.scaglione_fisso = scaglione_fisso
        if elenco_fatture:
            TotalStory = []
            self.fatture_pagine = dict()
            indice_pagine = 0
            for fatt in elenco_fatture:
                buffer = BytesIO()
                doc = MyDocTemplate(
                    buffer,
                    leftMargin=BORDO_SINISTRO,
                    rightMargin=BORDO_DESTRO,
                    bottomMargin=BORDO_INFERIORE,
                    topMargin=BORDO_SUPERIORE,
                    showBoundary=DEBUG_MODE,
                )
                Story = []
                self.fattura = fatt
                t = self.disegna_righe(
                    fatt.get_righe_fattura_raggruppate(), fatt.omaggio,
                    fatt.divisa, fatt.cambio_divisa, fatt.cliente.mastervoice,
                    fatt.traffico_voip,
                )
                Story.append(t)
                doc.build(Story, onFirstPage=self.aggiorna_numero_pagine, onLaterPages=self.aggiorna_numero_pagine,)
                TotalStory.append(t)
                TotalStory.append(PageBreak())
                for indice in range(0, self.total_pages):
                    indice_pagine = indice_pagine + 1
                    self.fatture_pagine[indice_pagine] = fatt
                    self.pagine_totali[indice_pagine] = dict(pagina_corrente=indice + 1, max_pagine=self.total_pages)
                self.total_pages = 0
            buffer = BytesIO()
            doc = MyDocTemplate(
                buffer,
                leftMargin=BORDO_SINISTRO,
                rightMargin=BORDO_DESTRO,
                bottomMargin=BORDO_INFERIORE,
                topMargin=BORDO_SUPERIORE,
                showBoundary=DEBUG_MODE,
            )
            doc.build(TotalStory, onFirstPage=self.disegna_header_footer, onLaterPages=self.disegna_header_footer,)
            pdf = buffer.getvalue()
            buffer.close()
            return pdf
        else:
            return None

    def disegna_righe(self, righe, omaggio, divisa, cambio, cliente_mastervoice, fattura_traffico):
        data = [('Descrizione', 'Q.ta', 'Costo Unit.', 'Iva', 'Totale'), ]
        for riga in righe:
            if not riga['contabile']:
                percentuale_iva = ''
                totale_riga = ''
            else:
                percentuale_iva = '%s%s' % (riga['percentuale_iva'], r'%')
                totale_riga = get_stringa_importo(riga['totale_riga'], divisa, cambio)
            costo_unitario = get_stringa_importo(riga['costo_unitario'], divisa, cambio)
            if riga['seriale']:
                descrizione = '%s<br/><font size=6>SERIAL NUM.: %s<br/>----------------------------</font>' % (
                    riga['descrizione'],
                    ' - '.join(riga['seriale'])
                )
            else:
                descrizione = riga['descrizione']
            data.append((
                Paragraph(descrizione, ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=8)),
                riga['quantita'],
                costo_unitario,
                percentuale_iva,
                totale_riga,
            ))
        if omaggio:
            data.append((
                Paragraph(TESTO_OMAGGIO, ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=8)),
                '',
                '',
                '',
                '',
            ))
        if fattura_traffico:
            data.append((
                Paragraph(DICITURA_TRAFFICO_1, ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=6)),
                '',
                '',
                '',
                '',
            ))
            data.append((
                Paragraph(DICITURA_TRAFFICO_2, ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=6)),
                '',
                '',
                '',
                '',
            ))
        if cliente_mastervoice:
            data.append((
                Paragraph(TESTO_FATTURE_FONIA, ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=6)),
                '',
                '',
                '',
                '',
            ))
        style = TableStyle(RIGHE_STYLE)
        colwidths = (LARGHEZZA_COLONNA_DESCRIZIONE, LARGHEZZA_QUANTITA, LARGHEZZA_COSTO_UNITARIO, LARGHEZZA_IVA, LARGHEZZA_TOTALE)
        t = LongTable(data=data, colWidths=colwidths, repeatRows=1)
        t.setStyle(style)
        return t

    def aggiorna_numero_pagine(self, canvas, document):
        if self.total_pages < document.page:
            self.total_pages = document.page

    def disegna_header_footer(self, canvas, document):
        """Produce the first page of the invoice"""
        canvas.saveState()
        self.aggiorna_numero_pagine(canvas, document)
        if self.fatture_pagine:
            fattura = self.fatture_pagine[document.page]
        else:
            fattura = self.fattura
        try:
            self.disegna_header(canvas, fattura, document)
            self.disegna_footer(canvas, fattura)
        finally:
            canvas.restoreState()

    def disegna_riquadro_superiore_testata(self, canvas, frame, fatt, doc):
        if self.pagine_totali:
            pagina_corrente = 0
            pagine_massime = 0
            if doc.page:
                if doc.page in self.pagine_totali:
                    if 'pagina_corrente' in self.pagine_totali[doc.page]:
                        pagina_corrente = self.pagine_totali[doc.page]['pagina_corrente']
                    if 'max_pagine' in self.pagine_totali[doc.page]:
                        pagine_massime = self.pagine_totali[doc.page]['max_pagine']
        else:
            pagine_massime = self.total_pages
            pagina_corrente = canvas.getPageNumber()
        if fatt.totale < 0:
            tipo_documento = 'NOTA DI CREDITO'
        else:
            tipo_documento = 'FATTURA'
        dati_fattura = 'Pagina %s di %s\n%s\nNUMERO %s del %s' % (
            pagina_corrente,
            pagine_massime,
            tipo_documento,
            fatt.get_numero(),
            fatt.data.strftime('%d/%m/%Y')
        )
        file_logo = get_file_logo(fatt)
        image_fd = open(file_logo, 'rb')
        logo = Image(image_fd)
        logo.drawWidth = LARGHEZZA_LOGO
        logo.drawHeight = ALTEZZA_LOGO
        dati_testata = get_dati_testata(fatt)
        data = [(logo, dati_testata, dati_fattura, ), ]
        indirizzo_cliente = []
        cliente = fatt.cliente
        indirizzo_cliente.append('%s' % cliente)
        indirizzo_cliente.append(cliente.indirizzo or '')
        indirizzo_cliente.append('%s %s (%s)' % (cliente.cap, cliente.citta, cliente.get_provincia()))
        stile_header = TableStyle(HEADER_STYLE)
        colwidths = (LARGHEZZA_LOGO, LARGHEZZA_DATI_AZIENDA, LARGHEZZA_DATI_DOCUMENTO,)
        t1 = Table(data, colWidths=colwidths)
        t1.setStyle(stile_header)
        frame.addFromList([t1], canvas)
        image_fd.close()

    def disegna_riquadro_destinatario_testata(self, canvas, frame, fatt):
        # Dati intestazione fattura
        pi_cf = ''
        if fatt.partita_iva:
            pi_cf = 'P.Iva: %s' % fatt.partita_iva
        if fatt.codice_fiscale:
            cod_fisc = 'C.F. %s' % fatt.codice_fiscale
            if pi_cf:
                pi_cf = '%s - %s' % (pi_cf, cod_fisc)
            else:
                pi_cf = cod_fisc
        testo_intestazione = '<b>Intestazione Fattura:</b><br/>%s<br/>%s' % (fatt.get_indirizzo().replace('\n', '<br/>'), pi_cf)
        intestazione_style = ParagraphStyle(name='intestazione', fontName='Times-Roman', fontSize=10, alignment=TA_LEFT)
        intestazione_fattura = Paragraph(testo_intestazione, intestazione_style)
        destinazione_fattura = fatt.get_destinazione_documento().replace("&", "&amp;").replace('\n', '<br/>'),
        destinatario = Paragraph('<b>Destinazione Documento:</b><br/>%s' % destinazione_fattura, intestazione_style)
        data = [(intestazione_fattura, destinatario), ]
        colwidths = ((LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO)) / 2, (LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO)) / 2)
        t2 = Table(data, colWidths=colwidths)
        t2.setStyle(TableStyle(INTESTAZIONE_STYLE))
        frame.addFromList([t2], canvas)

    def get_riquadro_totali_footer(self, fatt):
        label_style = ParagraphStyle(name='label_totali', fontName='Times-Bold', fontSize=9, alignment=TA_CENTER)
        valore_iva = ''
        if fatt.esenzione_iva:
            valore_iva = '*'
        else:
            valore_iva = get_stringa_importo(fatt.iva, fatt.divisa, fatt.cambio_divisa)
        totale_non_imponibile = fatt.get_totale_non_imponibile()
        if totale_non_imponibile:
            totale_imponibile = fatt.imponibile - totale_non_imponibile
            label_style_2 = ParagraphStyle(name='label_totali', fontName='Times-Bold', fontSize=7, alignment=TA_CENTER)
            data = [
                (
                    Paragraph('TOTALE<br/>ESENTE IVA', label_style_2), get_stringa_importo(totale_non_imponibile, fatt.divisa, fatt.cambio_divisa)
                ),
                (
                    Paragraph('TOTALE IMPONIBILE (%s%%)' % IVA_DEFAULT, label_style_2), get_stringa_importo(
                        totale_imponibile, fatt.divisa, fatt.cambio_divisa
                    )
                ),
                (
                    Paragraph('IVA', label_style_2), valore_iva
                ),
                (
                    Paragraph('TOTALE FATTURA', label_style_2), get_stringa_importo(fatt.totale, fatt.divisa, fatt.cambio_divisa)
                ),
            ]
            stile_header = TableStyle(TOTALI_STYLE_2)
        else:
            data = [
                (
                    Paragraph('TOTALE IMPONIBILE', label_style), get_stringa_importo(fatt.imponibile, fatt.divisa, fatt.cambio_divisa)
                ),
                (
                    Paragraph('IVA', label_style), valore_iva
                ),
                (
                    Paragraph('TOTALE FATTURA', label_style), get_stringa_importo(fatt.totale, fatt.divisa, fatt.cambio_divisa)
                ),
            ]
            stile_header = TableStyle(TOTALI_STYLE)
        colwidths = (LARGHEZZA_TOTALI / 2, LARGHEZZA_TOTALI / 2)
        tabella_totali = Table(data, colWidths=colwidths)
        tabella_totali.setStyle(stile_header)
        return tabella_totali

    def disegna_pagamento_totali(self, canvas, frame_footer, fatt):
        tabella_totali = self.get_riquadro_totali_footer(fatt)
        if self.scaglione_fisso:
            sc = self.scaglione_fisso
            data_scadenza = calcola_data_scadenza(
                fatt.data,
                giorni_scadenza=sc.giorni,
                mesi_scadenza=sc.mesi,
                anni_scadenza=sc.anni,
                fine_mese=sc.fine_mese,
                giorni_oltre_fine_mese=sc.giorni_oltre_fine_mese
            ).strftime('%d/%m/%Y')
            dati_pagamento = 'Pagamento:\n%s %s %s al %s' % (
                'BONIFICO', self.scaglione_fisso,
                get_stringa_importo(fatt.totale, fatt.divisa, fatt.cambio_divisa),
                data_scadenza
            )
            if fatt.banca_appoggio:
                dati_banca = '\nBanca appoggio:\n%s\n%s' % (fatt.banca_appoggio.nome, fatt.banca_appoggio.iban)
                dati_pagamento = dati_pagamento + dati_banca
        else:
            dati_pagamento = 'Pagamento:'
            ha_riba = False
            if fatt.tipo_pagamento_gamma and fatt.tipo_pagamento_gamma.nome == 'VEDI NOTE':
                if fatt.note_pagamento:
                    dati_pagamento = 'Pagamento:\n' + fatt.note_pagamento
            else:
                for pagamento in fatt.get_scadenze_pagamenti():
                    nome_pagamento = ''
                    if pagamento.codice_scaglione_pagamento:
                        nome_pagamento = pagamento.codice_scaglione_pagamento.nome
                    if pagamento.tipo_pagamento.nome == 'RIBA':
                        banca_cliente = fatt.banca_cliente or 'banca cliente'
                        if fatt.iban:
                            stringa_pagamento = '%s su %s con IBAN:%s - %s %s al %s' % (
                                pagamento.tipo_pagamento,
                                banca_cliente,
                                fatt.iban,
                                nome_pagamento,
                                get_stringa_importo(pagamento.importo, fatt.divisa, fatt.cambio_divisa),
                                pagamento.data.strftime('%d/%m/%Y'),
                            )
                        else:
                            stringa_pagamento = '%s su %s con ABI:%s CAB:%s - %s %s al %s' % (
                                pagamento.tipo_pagamento,
                                banca_cliente,
                                fatt.abi,
                                fatt.cab,
                                nome_pagamento,
                                get_stringa_importo(pagamento.importo, fatt.divisa, fatt.cambio_divisa),
                                pagamento.data.strftime('%d/%m/%Y'),
                            )
                        dati_pagamento = dati_pagamento + '\n' + stringa_pagamento
                        ha_riba = True
                    elif pagamento.tipo_pagamento.nome == 'RID':
                        banca_cliente = fatt.banca_cliente_rid or 'banca cliente'
                        if fatt.iban_rid:
                            stringa_pagamento = '%s su %s con IBAN:%s - %s %s al %s' % (
                                pagamento.tipo_pagamento,
                                banca_cliente,
                                fatt.iban_rid,
                                nome_pagamento,
                                get_stringa_importo(pagamento.importo, fatt.divisa, fatt.cambio_divisa),
                                pagamento.data.strftime('%d/%m/%Y'),
                            )
                        else:
                            stringa_pagamento = '%s su %s con ABI:%s CAB:%s - %s %s al %s' % (
                                pagamento.tipo_pagamento,
                                banca_cliente,
                                fatt.abi_rid,
                                fatt.cab_rid,
                                nome_pagamento,
                                get_stringa_importo(pagamento.importo, fatt.divisa, fatt.cambio_divisa),
                                pagamento.data.strftime('%d/%m/%Y'),
                            )
                        dati_pagamento = dati_pagamento + '\n' + stringa_pagamento
                        ha_riba = True
                    else:
                        stringa_pagamento = '%s %s %s al %s' % (
                            pagamento.tipo_pagamento,
                            nome_pagamento,
                            get_stringa_importo(pagamento.importo, fatt.divisa, fatt.cambio_divisa),
                            pagamento.data.strftime('%d/%m/%Y')
                        )
                        dati_pagamento = dati_pagamento + '\n' + stringa_pagamento
                if fatt.banca_appoggio and not ha_riba:
                    dati_banca = '\nBanca appoggio:\n%s\n%s' % (fatt.banca_appoggio.nome, fatt.banca_appoggio.iban)
                    dati_pagamento = dati_pagamento + dati_banca
            if fatt.note_fattura:
                dati_pagamento = dati_pagamento + '\n' + fatt.note_fattura
            if fatt.bollo_virtuale:
                dati_pagamento = dati_pagamento + '\n' + TESTO_BOLLO_VIRTUALE
        style = ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=7)
        riquadro_pagamento = self.disegna_paragrafo(dati_pagamento, style)
        data = [(riquadro_pagamento, tabella_totali, ), ]
        stile_footer = TableStyle(FOOTER_STYLE)
        colwidths = (LARGHEZZA_PAGAMENTI, LARGHEZZA_TOTALI)
        tabella_footer = Table(data, colWidths=colwidths)
        tabella_footer.setStyle(stile_footer)
        frame_footer.addFromList([tabella_footer], canvas)

    def disegna_riga_mora_conai(self, canvas, frame_footer):
        style = ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=7, alignment=TA_RIGHT)
        data = [(self.disegna_paragrafo(TESTO_INTERESSI_MORA), self.disegna_paragrafo(TESTO_CONAI, style),), ]
        stile_footer = TableStyle(MORA_CONAI_STYLE)
        colwidths = (LARGHEZZA_INTERESSI_MORA, LARGHEZZA_CONAI)
        tabella_conai = Table(data, colWidths=colwidths)
        tabella_conai.setStyle(stile_footer)
        frame_footer.addFromList([tabella_conai], canvas)

    def disegna_privacy(self, canvas, frame_footer):
        style_titolo = ParagraphStyle(name='descrizione', fontName='Times-Bold', fontSize=7, alignment=TA_LEFT)
        style_corpo = ParagraphStyle(name='descrizione', fontName='Times-Roman', fontSize=6, alignment=TA_LEFT)
        data = [
            self.disegna_paragrafo(TESTO_PRIVACY_BOLD, style_titolo),
            self.disegna_paragrafo(TESTO_PRIVACY_CORPO, style_corpo),
        ]
        tabella_privacy = Table(data)
        # frame_footer.addFromList([tabella_privacy], canvas)

    def disegna_esenzione_iva(self, canvas, frame_footer, fatt):
        data = [(self.disegna_paragrafo('* ' + fatt.esenzione_iva.descrizione)), ]
        tabella_esenzione = Table(data)
        frame_footer.addFromList([tabella_esenzione], canvas)

    def disegna_footer(self, canvas, fattura):
        frame_footer = Frame(
            x1=BORDO_SINISTRO,
            y1=0.5 * centimetri,
            width=LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO),
            height=BORDO_INFERIORE - (0.5 * centimetri),
            showBoundary=DEBUG_MODE,
            leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0,
        )
        self.disegna_pagamento_totali(canvas, frame_footer, fattura)
        self.disegna_riga_mora_conai(canvas, frame_footer)
        self.disegna_privacy(canvas, frame_footer)
        if fattura.esenzione_iva:
            self.disegna_esenzione_iva(canvas, frame_footer, fattura)

    def disegna_header(self, canvas, fattura, doc):
        frame_header = Frame(
            x1=BORDO_SINISTRO,
            y1=ALTEZZA_PAGINA - INIZIO_BORDO_TESTATA,
            width=LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO),
            height=ALTEZZA_TESTATA,
            showBoundary=DEBUG_MODE,
            leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0,
        )
        self.disegna_riquadro_superiore_testata(canvas, frame_header, fattura, doc)
        self.disegna_riquadro_destinatario_testata(canvas, frame_header, fattura)
        canvas.setLineWidth(DIMENSIONE_LINEE)  # small lines
        canvas.rect(
            x=BORDO_SINISTRO,
            y=BORDO_INFERIORE,
            width=LARGHEZZA_PAGINA - (BORDO_SINISTRO + BORDO_DESTRO),
            height=ALTEZZA_PAGINA - (BORDO_SUPERIORE + BORDO_INFERIORE)
        )
