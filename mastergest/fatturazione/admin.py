import csv
from datetime import datetime
from decimal import Decimal, getcontext, ROUND_HALF_UP
import zipfile
from wsgiref.util import FileWrapper

from django.conf.urls import url
from django.contrib import admin
from django.contrib import messages

from django.core.files.temp import tempfile
from django.db import models
from django.db.models import Sum, Count
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from django.utils.translation import ugettext as _
from django.contrib.admin import SimpleListFilter
from django.utils import timezone
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.conf import settings
from django.template import Context, loader
from suit.admin import SortableTabularInline, SortableStackedInline

from import_export.formats import base_formats
from import_export.admin import ExportActionMixin
from webodt.shortcuts import render_to_response as webodt_render_to_response

from mastergest.utils.forms import LimitModelFormset
from mastergest.fatturazione import forms
from mastergest.fatturazione.models import BancaAppoggio
from mastergest.fatturazione.models import RigaFattura, Fattura
from mastergest.fatturazione.models import ScadenzaPagamento
from mastergest.fatturazione.models import ScaglionePagamentoTemplate
from mastergest.fatturazione.models import TipoPagamentoGamma
from mastergest.fatturazione.utils import send_fattura, calcola_hash_file
from mastergest.fatturazione.utils import crea_riepilogo_centri_costo
from mastergest.fatturazione.reports import FatturaPdf
from mastergest.incassi.models import Incasso, Abbinamento
from mastergest.sdi.admin import TrasmissioneFatturaInline
from mastergest.sdi.tasks import trasmetti_fattura_sdi_task
# from mastergest.sdi.tasks import trasmetti_fattura_bluenext_task
from mastergest.utils.signing import vuota_cartella_temp
from mastergest.utils.admin import MastergestAdmin
from mastergest.fatturazione.utils import get_xml_fattura
from mastergest.fatturazione.resources import FatturaResource, RigaFatturaResource
from mastergest.fatturazione.utils import aggiungi_scaglioni_pagamento_fattura

getcontext().rounding = ROUND_HALF_UP


class RigaFatturaAdmin(ExportActionMixin, admin.ModelAdmin):
    list_display = (
        'id', 'get_anno_numero', 'data_fattura', 'descrizione', 'quantita',
        'costo_unitario', 'piano_conti_gamma', 'sede_cliente'
    )
    search_fields = (
        'descrizione', 'fattura__ragione_sociale',
        'fattura__data', 'fattura__anno_numero'
    )
    resource_class = RigaFatturaResource
    formats = (
        base_formats.CSV,
        base_formats.XLS,
        base_formats.XLSX,
        base_formats.ODS,
    )
    date_hierarchy = 'fattura__data'
    autocomplete_fields = ('piano_conti_gamma', 'fattura', 'sede_cliente')
    list_filter = ('piano_conti_gamma', 'contabile', 'quantita')


class RigaFatturaInline(SortableTabularInline):
    model = RigaFattura
    form = forms.RigaFatturaForm
    formfield_overrides = {
        models.DecimalField: {'localize': True},
    }
    autocomplete_fields = ('piano_conti_gamma', )
    extra = 0
    fields = (
        'seriale', 'descrizione', 'quantita', 'costo_unitario',
        'percentuale_iva', 'contabile', 'piano_conti_gamma'
    )
    suit_classes = 'suit-tab suit-tab-corpo'
    sortable = 'order'


class RigaFatturaCentriCostoInline(SortableStackedInline):
    model = RigaFattura
    form = forms.RigaFatturaForm
    formfield_overrides = {
        models.DecimalField: {'localize': True},
    }
    extra = 0
    suit_classes = 'suit-tab suit-tab-corpo'
    readonly_fields = ('get_centro_costo_display', )
    autocomplete_fields = ('piano_conti_gamma', 'sede_cliente')
    sortable = 'order'
    fieldsets = (
        (
            None, dict(
                fields=[
                    ('descrizione', 'quantita', 'costo_unitario', 'percentuale_iva', 'contabile'),
                    ('seriale', 'piano_conti_gamma', 'sede_cliente', 'get_centro_costo_display'),
                ]
            )
        ),
    )


class ScadenzaPagamentoInline(admin.TabularInline):
    model = ScadenzaPagamento
    form = forms.ScadenzaPagamentoForm
    formfield_overrides = {
        models.DecimalField: {'localize': True},
    }
    extra = 0
    suit_classes = 'suit-tab suit-tab-pagamenti'


class FatturaReadonlyInline(admin.TabularInline):
    model = Fattura
    form = forms.FatturaForm
    fields = ('get_link', 'data', 'get_importo_pagato_display', 'get_totale_display', 'banca_appoggio')
    extra = 0
    verbose_name_plural = 'Elenco Ultime 30 fatture'
    formset = LimitModelFormset
    suit_classes = 'suit-tab suit-tab-fatture'

    def get_readonly_fields(self, request, obj=None):
        return [n.name for n in self.opts.fields] + ['get_link', 'get_importo_pagato_display', 'get_totale_display']

    def has_add_permission(self, request, obj):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class FatturaScuoleFilter(SimpleListFilter):
    title = 'Tipo Fattura'
    parameter_name = 'cliente'

    def lookups(self, request, model_admin):
        return (
            ('scuola', 'Scuola'),
            ('split_payment', 'Split Payment'),
            ('azienda', 'Azienda'),
            ('privato', 'Privato'),
            ('traffico', 'Traffico'),
            ('flat', 'Flat'),
            ('nota_credito', 'Nota di Credito'),
            ('scuole_inviate', 'Inviate PA'),
            ('scuole_non_inviate', 'Da inviare PA'),
        )

    def queryset(self, request, queryset):
        if self.value() is not None:
            if self.value() == 'scuola':
                return queryset.filter(cliente__scuola=True)
            if self.value() == 'split_payment':
                return queryset.filter(split_payment=True)
            if self.value() == 'azienda':
                return queryset.filter(cliente__azienda=True)
            if self.value() == 'privato':
                return queryset.filter(cliente__privato=True)
            if self.value() == 'traffico':
                return queryset.filter(traffico_voip=True)
            if self.value() == 'flat':
                return queryset.filter(flat=True)
            if self.value() == 'nota_credito':
                return queryset.filter(imponibile__lt=0)
            if self.value() == 'scuole_inviate':
                return queryset.filter(inviata_pa=True)
            if self.value() == 'scuole_non_inviate':
                return queryset.filter(cliente__scuola=True, inviata_pa=False)
        else:
            return queryset


class FatturaAdmin(ExportActionMixin, MastergestAdmin):
    list_display = (
        'get_numero', 'data', 'get_ragione_sociale_link',
        'get_totale_display', 'get_importo_pagato_display',
        'get_data_ultimo_pagamento', 'banca_appoggio', 'data_anticipo',
        'stato_sdi_display', 'gestione_azienda'
    )
    list_editable = ('data_anticipo', )
    autocomplete_fields = (
        'cliente', 'esenzione_iva', 'tipo_pagamento_gamma', 'sede_cliente'
    )
    readonly_fields = (
        'invio_elettronico', 'imponibile', 'iva', 'totale', 'codice_invio_xml',
        'stato_invio_sdi', 'inviata_pa',
    )
    search_fields = (
        'cliente__ragione_sociale', 'cliente__alias',
        'ragione_sociale', 'numero', 'data', 'postfisso', 'totale', 'cig',
        'cup', 'numero_documento_ordine'
    )
    list_filter = (
        'pagata', 'banca_appoggio', 'gestione_azienda', 'inviata_pa', 'stato_invio_sdi',
    )
    date_hierarchy = 'data'
    inlines = [
        ScadenzaPagamentoInline, RigaFatturaInline, TrasmissioneFatturaInline
    ]
    actions = [
        'mass_pdf', 'invio_fatture_email', 'elenco_fatture', 'export_to_csv',
        'export_riepilogo_pagamenti', 'export_riepilogo_mensile',
        'trasmetti_fatture_sdi', 'export_admin_action', 'mass_xml'
    ]
    form = forms.FatturaForm
    list_max_show_all = 10000
    list_per_page = 50
    resource_class = FatturaResource
    formats = (
        base_formats.CSV,
        base_formats.XLS,
        base_formats.XLSX,
        base_formats.ODS,
    )
    fieldsets = (
        (None, {
            'classes': ('suit-tab suit-tab-testata',),
            'fields': (
                ('data', 'numero', 'postfisso'),
                'cliente',
                'gestione_azienda',
            ),
        }),
        (_('Dati Cliente'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                ('ragione_sociale'),
                ('indirizzo', 'telefono'),
                ('citta', 'cap', 'provincia'),
                'stato',
                ('partita_iva', 'codice_fiscale'),
                'sede_cliente',
                'destinazione_documento',
            ),
        )),
        (_('Dati Banca Cliente (RIBA)'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=('banca_cliente', ('iban', 'abi', 'cab')),
        )),
        (_('Dati Banca Cliente (RID)'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=('banca_cliente_rid', ('iban_rid', 'abi_rid', 'cab_rid')),
        )),
        (_('Invio per Email'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                (
                    ('invio_elettronico', 'inviata',),
                )
            ),
        )),
        (_('Totali'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                ('divisa', 'cambio_divisa'),
                ('imponibile', 'omaggio', 'traffico_voip', 'flat'),
                ('iva', 'esenzione_iva', 'iva_esigibilita_differita', 'split_payment'),
                'totale',
            ),
        )),
        (_('Dati Pagamento'), dict(
            classes=('suit-tab suit-tab-pagamenti',),
            fields=(
                'tipo_pagamento_gamma', 'banca_appoggio', 'data_anticipo',
                'scaricata'
            ),
        )),
        (_('Note'), dict(
            classes=('suit-tab suit-tab-note',),
            fields=(
                'note',
                'note_pagamento',
                'note_fattura',
            ),
        )),
        (_('Corpo'), dict(
            classes=('suit-tab suit-tab-corpo',),
            fields=(('cig', 'cup', 'numero_documento_ordine'),),
        )),
        (_('Dati Accompagnatoria'), dict(
            classes=('suit-tab suit-tab-dati_accompagnatoria',),
            fields=(
                'accompagnatoria', 'vettore', 'aspetto_esteriore',
                'numero_colli', 'peso'
            ),
        )),
        (_('Dati Invio SDI'), dict(
            classes=('suit-tab suit-tab-trasmissioni_sdi',),
            fields=(
                (
                    ('bollo_virtuale', 'importo_bollo'),
                    'riferimento_amministrazione',
                    'inviata_pa',
                    'stato_invio_sdi',
                )
            ),
        )),
    )
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('corpo', 'Corpo'),
        ('pagamenti', 'Pagamenti'),
        ('note', 'Note'),
        ('dati_accompagnatoria', 'Dati Accompagnatoria'),
        ('trasmissioni_sdi', 'Trasmissioni SDI'),
    )

    def suit_cell_attributes(self, obj, column):
        if column in [
            'banca_appoggio', 'inviata_pa',
        ]:
            return {'class': 'text-center'}

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(r'^(\d+)/pdf/$',
                self.admin_site.admin_view(self.pdf),
                name='%s_%s_pdf' % info
                ),
            url(r'^odt/(\w+)/$',
                self.admin_site.admin_view(self.general_odt),
                name='%s_%s_general_odt' % info
                ),
            url(r'^(\d+)/riepilogo_centri_costo/$',
                self.admin_site.admin_view(self.export_riepilogo_centri_costo),
                name='%s_%s_riepilogo_centri_costo' % info
                ),
        ]
        url_patterns += super(FatturaAdmin, self).get_urls()
        return url_patterns

    def mass_pdf(self, request, queryset):
        fattura_pdf = FatturaPdf()
        pdf = fattura_pdf.genera_pdf_massivo(queryset)
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=elenco_fatture.pdf'
        return response
    mass_pdf.short_description = 'Download pdf'

    def trasmetti_fatture_sdi(self, request, queryset):
        form = None
        elenco_fatture_ordinate = queryset.order_by('anno_numero')
        if 'trasmetti' in request.POST:
            form = forms.TrasmettiFattureForm(request.POST)
            if form.is_valid():
                vuota_cartella_temp()
                alias = form.cleaned_data['alias']
                pin = form.cleaned_data['pin']
                for fattura in elenco_fatture_ordinate:
                    if fattura.cliente.codici_pa_sedi:
                        if not fattura.sede_cliente:
                            msg = 'ERRORE Il cliente della fatt. %s ha abilitato il codice SDI nelle sedi ma manca la sede nella fattura.' % fattura
                            messages.error(request, msg)
                            continue
                    if not fattura.get_codice_pa():
                        if not fattura.pec_destinatario():
                            msg = 'ERRORE Il cliente della fatt. %s non ha il codice destinatario e/o la PEC valorizzate.' % fattura
                            messages.error(request, msg)
                            continue
                    if not fattura.cap:
                        msg = 'ERRORE Il cliente della fatt. %s non ha il cap valorizzato.' % fattura
                        messages.error(request, msg)
                        continue
                    trasmetti_fattura_sdi_task.apply_async(queue='sdi', args=(fattura.id, alias, pin))
                return HttpResponseRedirect(request.get_full_path())
        if not form:
            form = forms.TrasmettiFattureForm(initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)})
        opts = self.model._meta
        app_label = opts.app_label
        return render(
            request, 'admin/fatturazione/trasmetti_fatture.html',
            {
                'elenco_fatture': elenco_fatture_ordinate,
                'trasmetti_fatture_form': form, "opts": opts,
                "app_label": app_label
            },
        )
    trasmetti_fatture_sdi.short_description = _('Trasmetti Fatture al Sistema di Interscambio')

    # def trasmetti_fatture_bluenext(self, request, queryset):
    #     elenco_fatture_ordinate = queryset.order_by('anno_numero')
    #     for fattura in elenco_fatture_ordinate:
    #         if fattura.cliente.codici_pa_sedi:
    #             if not fattura.sede_cliente:
    #                 msg = 'ERRORE Il cliente della fatt. %s ha abilitato il codice SDI nelle sedi ma manca la sede nella fattura.' % fattura
    #                 messages.error(request, msg)
    #                 continue
    #         if not fattura.get_codice_pa():
    #             if not fattura.pec_destinatario():
    #                 msg = 'ERRORE Il cliente della fatt. %s non ha il codice destinatario e/o la PEC valorizzate.' % fattura
    #                 messages.error(request, msg)
    #                 continue
    #         if not fattura.cap:
    #             msg = 'ERRORE Il cliente della fatt. %s non ha il cap valorizzato.' % fattura
    #             messages.error(request, msg)
    #             continue
    #         # trasmetti_fattura_sdi_task.apply_async(queue='sdi', args=(fattura.id, alias, pin))
    #         trasmetti_fattura_bluenext_task(fattura.id)
    #     return HttpResponseRedirect(request.get_full_path())
    # trasmetti_fatture_bluenext.short_description = _('Trasmetti Fatture SDI (Bluenext)')

    def invio_fatture_email(self, request, queryset):
        no_invio_elettronico = queryset.filter(invio_elettronico=False)
        if no_invio_elettronico.count() > 0:
            msg = "Sono presenti fatture non abilitate all'invio elettronico."
            messages.error(request, msg)
            return
        inviate = queryset.filter(inviata=True)
        if inviate.count() > 0:
            msg = "Sono presenti fatture gia' inviate."
            messages.error(request, msg)
            return
        # Invio
        for fattura in queryset:
            send_fattura(fattura)
            fattura.inviata = True
            fattura.save()
        msg = "Inviate %d fatture." % queryset.count()
        messages.success(request, msg)
    invio_fatture_email.short_description = 'Invio fatture email'

    def elenco_fatture(self, request, queryset):
        fatture = queryset.order_by('-data', '-numero')
        totalnum = fatture.count()
        totale = fatture.aggregate(Sum('totale'))['totale__sum']
        totale_iva = fatture.aggregate(Sum('iva'))['iva__sum']
        totale_imponibile = fatture.aggregate(Sum('imponibile'))['imponibile__sum']
        template = 'fatture/fatture_elenco.odt'
        context = Context(
            dict(
                fatture=fatture, totalnum=totalnum, totale=totale,
                totale_imponibile=totale_imponibile, totale_iva=totale_iva
            )
        )
        messages.success(request, 'Fatture stampate: %d' % fatture.count())
        filename = 'elenco_fatture.odt'  # TO DO: add here some filters(data start, data end, payed...)
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    elenco_fatture.short_description = 'Elenco fatture'

    def general_odt(self, request, type):
        list_display = list(self.list_display)
        ChangeList = self.get_changelist(request)
        cl = ChangeList(
            request, self.model, list_display,
            self.list_display_links, self.list_filter, self.date_hierarchy,
            self.search_fields, self.list_select_related,
            self.list_per_page, self.list_max_show_all, self.list_editable,
            self, self.sortable_by
        )
        fatture = cl.get_queryset(request)
        banche = BancaAppoggio.objects.all()
        totalsum, totalnum = Decimal(0), 0
        fatture = fatture.order_by('ragione_sociale')
        for i, banca in zip(list(range(len(banche))), banche):
            banche[i].scadenze = []
            fatture_banca = fatture.filter(banca_appoggio=banche[i])
            for fattura in fatture_banca:
                if type == 'riba' or type == 'rid':
                    scadenze = fattura.get_scadenze_pagamenti().filter(tipo_pagamento__nome=type.upper())
                for scadenza in scadenze:
                    banche[i].scadenze.append(scadenza)
                    totalnum += 1
                    totalsum += scadenza.importo
        if type == 'riba' or type == 'rid':
            template = 'fatture/fatture_%s.odt' % type
        context = Context(dict(banche=banche, totalnum=totalnum, totalsum=totalsum))
        filename = '%s_fatture.odt' % (type, )
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )

    def pdf(self, request, object_id):
        fattura = get_object_or_404(Fattura, pk=object_id)
        pdf = fattura.get_pdf()
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=%s' % fattura.get_nome_pdf()
        return response

    def export_to_csv(self, request, queryset):
        from mastergest.canoni.models import ScadenzaCanone
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_fatture.csv"'
        writer = csv.writer(response, delimiter=';')
        fattura_precedente = None
        for fattura in queryset.order_by('data', 'numero', 'postfisso'):
            if fattura_precedente:
                distanza_numero = fattura.numero - fattura_precedente.numero
                if not distanza_numero == 1:
                    if not fattura.postfisso and not fattura_precedente.postfisso:
                        msg = "La fattura %s %s ha problemi con data e numero sovrapposti" % (fattura.numero, fattura.ragione_sociale)
                        messages.error(request, msg)
                        return HttpResponseRedirect('/fatturazione/fattura/')
            fattura_precedente = fattura
        # Riga vuota per importazione gamma (testata)
        writer.writerow('')
        for fattura in queryset.order_by('numero', 'postfisso'):
            progressivo_riga = 0
            if fattura.totale < 0:
                nota_di_credito = True
            else:
                nota_di_credito = False
            banca_appoggio = ''
            if fattura.banca_appoggio:
                if fattura.banca_appoggio.codice_gamma:
                    banca_appoggio = fattura.banca_appoggio.codice_gamma
            if fattura.omaggio:
                omaggio = 1
            else:
                omaggio = 0
            codice_pagamento_gamma = ''
            if fattura.tipo_pagamento_gamma:
                codice_pagamento_gamma = fattura.tipo_pagamento_gamma.codice
            codice_esenzione = ''
            descrizione_esenzione = ''
            if fattura.split_payment:
                codice_esenzione = '22SP'
                descrizione_esenzione = 'Split Payment'
            if fattura.esenzione_iva:
                if fattura.esenzione_iva.codice_gamma:
                    codice_esenzione = fattura.esenzione_iva.codice_gamma.strip()
                    descrizione_esenzione = fattura.esenzione_iva.descrizione
            if fattura.iva_esigibilita_differita:
                codice_esenzione = '21EP'
                descrizione_esenzione = 'Iva Esigibilita differita'
            numero_fattura = '%s' % fattura.numero
            if fattura.postfisso:
                numero_fattura += fattura.postfisso
            writer.writerow(
                [
                    't',
                    fattura.id,
                    fattura.cliente.id_gamma,
                    numero_fattura,
                    fattura.data.strftime("%d/%m/%Y"),
                    banca_appoggio,
                    fattura.cliente.ragione_sociale.strip(),
                    fattura.indirizzo,
                    fattura.cap,
                    fattura.citta,
                    fattura.provincia,
                    fattura.telefono,
                    fattura.partita_iva.strip(),
                    fattura.codice_fiscale.strip(),
                    fattura.banca_cliente,
                    fattura.abi,
                    fattura.cab,
                    fattura.iban,
                    codice_pagamento_gamma,
                    fattura.imponibile,
                    fattura.iva,
                    fattura.totale,
                    '', '', '', '',
                    fattura.note,
                    omaggio,
                    codice_esenzione,
                    descrizione_esenzione
                ]
            )
            data_inizio_competenza = ''
            data_fine_competenza = ''
            for riga in fattura.get_righe_fattura():
                if riga.contabile:
                    contabile = 1
                else:
                    contabile = 0
                piano_conti_gamma = ''
                if riga.piano_conti_gamma:
                    piano_conti_gamma = riga.piano_conti_gamma.codice
                try:
                    scadenza_canone = ScadenzaCanone.objects.get(riga_fattura=riga)
                    data_inizio_competenza = scadenza_canone.data_inizio.strftime("%d/%m/%Y")
                    data_fine_competenza = scadenza_canone.data_fine.strftime("%d/%m/%Y")
                except ScadenzaCanone.DoesNotExist:
                    data_inizio_competenza = ''
                    data_fine_competenza = ''
                progressivo_riga += 1
                costo_unitario = riga.costo_unitario
                if nota_di_credito:
                    if costo_unitario:
                        costo_unitario = -riga.costo_unitario
                writer.writerow([
                    'r',
                    progressivo_riga,
                    riga.descrizione.replace('\n', '').replace('\r', ''),
                    riga.quantita,
                    costo_unitario,
                    riga.percentuale_iva,
                    contabile,
                    riga.seriale,
                    piano_conti_gamma,
                    data_inizio_competenza,
                    data_fine_competenza
                ])
        return response
    export_to_csv.short_description = 'Esportazione Fatture Teamsystem'

    def export_piano_conti_aggregato_csv(self, request, queryset):
        """Esporta i totali delle righe delle fatture aggregate per piano dei conti gamma"""
        from collections import defaultdict
        from decimal import Decimal

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_piano_conti_aggregato.csv"'
        writer = csv.writer(response, delimiter=';')

        # Intestazioni del CSV
        writer.writerow([
            'Piano dei Conti',
            'Codice Piano Conti',
            'Totale'
        ])

        # Dizionario per aggregare i totali per piano dei conti
        totali_piano_conti = defaultdict(Decimal)
        piani_conti_info = {}  # Per memorizzare nome e codice

        # Itera attraverso tutte le fatture selezionate
        for fattura in queryset:
            # Itera attraverso tutte le righe di ogni fattura
            for riga in fattura.rigafattura_set.all():
                if riga.piano_conti_gamma and riga.contabile:
                    piano_conti = riga.piano_conti_gamma
                    # Aggrega il totale per questo piano dei conti
                    totali_piano_conti[piano_conti.id] += riga.get_totale_riga()
                    # Memorizza le informazioni del piano dei conti
                    piani_conti_info[piano_conti.id] = {
                        'nome': piano_conti.nome,
                        'codice': piano_conti.codice
                    }

        # Ordina per nome del piano dei conti e scrive le righe
        for piano_conti_id in sorted(piani_conti_info.keys(),
                                   key=lambda x: piani_conti_info[x]['nome']):
            info = piani_conti_info[piano_conti_id]
            totale = totali_piano_conti[piano_conti_id]

            writer.writerow([
                info['nome'],
                info['codice'],
                str(totale).replace('.', ',')
            ])

        return response
    export_piano_conti_aggregato_csv.short_description = 'Esportazione Piano Conti Aggregato'

    def export_riepilogo_pagamenti(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_fatture.csv"'
        writer = csv.writer(response, delimiter=';')
        elenco_fatture = queryset.order_by('data', 'numero', 'postfisso')
        elenco_totali_pagamenti = elenco_fatture.filter(numero__gte=0).values('tipo_pagamento_gamma').annotate(
            numero_fatture=Count('id'),
            totale_imponibile=Sum('imponibile'),
            totale_iva=Sum('iva'),
            totale_fatture=Sum('totale')
        ).order_by('tipo_pagamento_gamma')
        writer.writerow([
            'tipo pagamento',
            'codice gamma',
            'numero fatture',
            'totale imponibile',
            'totale iva',
            'totale fatture',
        ])
        for totale_pagamento in elenco_totali_pagamenti:
            pagamento_gamma = TipoPagamentoGamma.objects.get(id=totale_pagamento['tipo_pagamento_gamma'])
            writer.writerow([
                pagamento_gamma.nome,
                pagamento_gamma.codice,
                totale_pagamento['numero_fatture'],
                str(totale_pagamento['totale_imponibile']).replace('.', ','),
                str(totale_pagamento['totale_iva']).replace('.', ','),
                str(totale_pagamento['totale_fatture']).replace('.', ','),
            ])
        return response
    export_riepilogo_pagamenti.short_description = 'Esportazione Riepilogo Pagamenti'

    def export_riepilogo_centri_costo(self, request, object_id):
        fattura = get_object_or_404(Fattura, pk=object_id)
        if fattura.cliente.gestione_centri_costo:
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = 'attachment;filename="riepilogo_centri_costo.xls"'
            foglio_excel = crea_riepilogo_centri_costo(fattura)
            foglio_excel.save(response)
        return response
    export_riepilogo_centri_costo.short_description = 'Esportazione Riepilogo Centri di costo'

    def export_riepilogo_mensile(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_fatture.csv"'
        writer = csv.writer(response, delimiter=';')
        elenco_fatture = queryset.order_by('data', 'numero', 'postfisso')

        from django.db import connection
        truncate_month = connection.ops.date_trunc_sql('month', 'data')
        elenco_fatture_mensili = elenco_fatture.extra({'mese': truncate_month}).values('anno', 'mese').annotate(
            numero_fatture=Count('id'),
            totale_imponibile=Sum('imponibile'),
            totale_fatture=Sum('totale')
        ).order_by('anno', 'mese')
        writer.writerow([
            'anno',
            'mese',
            'numero fatture',
            'imponibile MC',
            'imponibile MV',
            'imponibile Altro',
            'totale imponibile',
        ])
        for totale_fattura in elenco_fatture_mensili:
            anno = totale_fattura['anno']
            mese = totale_fattura['mese'].month
            query_imponibile_mc = elenco_fatture.filter(
                data__month=mese, data__year=anno, cliente__mastercom=True,
                cliente__mastervoice=False
            ).aggregate(totale_imponibile=Sum('imponibile'))
            imponibile_mc = query_imponibile_mc['totale_imponibile']
            if not imponibile_mc:
                imponibile_mc = 0
            query_imponibile_mv = elenco_fatture.filter(
                data__month=mese, data__year=anno, cliente__mastercom=False,
                cliente__mastervoice=True
            ).aggregate(totale_imponibile=Sum('imponibile'))
            imponibile_mv = query_imponibile_mv['totale_imponibile']
            if not imponibile_mv:
                imponibile_mv = 0
            query_imponibile_altro = elenco_fatture.filter(
                data__month=mese, data__year=anno).exclude(
                cliente__mastercom=True, cliente__mastervoice=False
            ).exclude(
                    cliente__mastercom=False, cliente__mastervoice=True
            ).aggregate(totale_imponibile=Sum('imponibile'))
            imponibile_altro = query_imponibile_altro['totale_imponibile']
            if not imponibile_altro:
                imponibile_altro = 0
            writer.writerow([
                anno,
                mese,
                totale_fattura['numero_fatture'],
                str(imponibile_mc).replace('.', ','),
                str(imponibile_mv).replace('.', ','),
                str(imponibile_altro).replace('.', ','),
                str(totale_fattura['totale_imponibile']).replace('.', ','),
            ])
        return response
    export_riepilogo_mensile.short_description = 'Esportazione Riepilogo Mensile'

    def mass_xml(self, request, queryset):
        elenco_fatture = []
        cartella_origine = tempfile.mkdtemp(dir=settings.SIGNING_TEMPDIR)
        nome_zip = '%s/elenco_fatture_xml.zip' % settings.SIGNING_TEMPDIR
        archivio_zip = zipfile.ZipFile(nome_zip, "w", zipfile.ZIP_DEFLATED)
        for indice_fatture in range(0, len(queryset)):
            messaggio, file_fattura = get_xml_fattura(queryset[indice_fatture], '00000')
            if file_fattura:
                # aggiungo a elenco fatture
                elenco_fatture.append(queryset[indice_fatture])
                nome_file_xml = 'fattura_%s.xml' % queryset[indice_fatture].id
                # scrivo fisicamente il file su una directory temporanea (per calcolare hash)
                path_file_origine = '%s/%s' % (cartella_origine, nome_file_xml)
                file_origine = open(path_file_origine, "w")
                file_origine.write(file_fattura)
                file_origine.close()
                # trovo hash 256 del file
                stringa_hash = calcola_hash_file(path_file_origine)
                # allego hash ai dati della fattura
                queryset[indice_fatture].stringa_hash = stringa_hash.decode('utf-8')
                queryset[indice_fatture].nome_file_xml = nome_file_xml
                # scrivo il file dentro archivio zip
                archivio_zip.write(path_file_origine, nome_file_xml)
            else:
                messages.error(request, messaggio)
        # aggiungo indice di versamento
        ora_corrente = timezone.now()
        codice_indice = ora_corrente.strftime('%Y%m%d-%H%M')
        template_vars = dict()
        template_vars['elenco_fatture'] = elenco_fatture
        template_vars['codice_indice'] = codice_indice
        template_idv = loader.get_template('fatturazione/IPDV-template-cdd_2__fatturaPA.xml')
        indice_di_versamento_xml = template_idv.render(template_vars)
        archivio_zip.writestr('IPDV-%s.xml' % codice_indice, indice_di_versamento_xml)
        # CHIUDO ARCHIVIO ZIP
        archivio_zip.close()
        response = HttpResponse(FileWrapper(open(nome_zip, 'rb')), content_type="application/zip")
        response["Content-Disposition"] = "attachment; filename=fatture_xml_%s.zip" % codice_indice
        return response
    mass_xml.short_description = 'Download files XML'

    def save_related(self, request, form, formsets, change):
        super(FatturaAdmin, self).save_related(request, form, formsets, change)
        aggiungi_scaglioni_pagamento_fattura(form.instance)

    def get_inline_instances(self, request, obj=None):
        if obj:
            if obj.cliente:
                if obj.cliente.gestione_centri_costo:
                    self.inlines = [
                        ScadenzaPagamentoInline, RigaFatturaCentriCostoInline,
                        TrasmissioneFatturaInline
                    ]
                else:
                    self.inlines = [
                        ScadenzaPagamentoInline, RigaFatturaInline,
                        TrasmissioneFatturaInline
                    ]
        return super(FatturaAdmin, self).get_inline_instances(request, obj)


class FatturaMagisterAdmin(FatturaAdmin):
    list_display = (
        'get_numero', 'data', 'get_ragione_sociale_link',
        'get_totale_display', 'get_importo_pagato_display',
        'get_data_ultimo_pagamento', 'banca_appoggio', 'data_anticipo',
        'stato_sdi_display',
    )
    fieldsets = (
        (None, {
            'classes': ('suit-tab suit-tab-testata',),
            'fields': (('data', 'numero', 'postfisso'),
                       'cliente',
                       ),
        }),
        (_('Dati Cliente'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                ('ragione_sociale'),
                ('indirizzo', 'telefono'),
                ('citta', 'cap', 'provincia'),
                'stato',
                ('partita_iva', 'codice_fiscale'),
                'sede_cliente',
                'destinazione_documento',
            ),
        )),
        (_('Dati Banca Cliente (RIBA)'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=('banca_cliente', ('iban', 'abi', 'cab')),
        )),
        (_('Dati Banca Cliente (RID)'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=('banca_cliente_rid', ('iban_rid', 'abi_rid', 'cab_rid')),
        )),
        (_('Invio per Email'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                (
                    ('invio_elettronico', 'inviata',),
                )
            ),
        )),
        (_('Totali'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                ('divisa', 'cambio_divisa'),
                ('imponibile', 'omaggio', 'traffico_voip'),
                ('iva', 'esenzione_iva', 'iva_esigibilita_differita', 'split_payment'),
                'totale',
            ),
        )),
        (_('Dati Pagamento'), dict(
            classes=('suit-tab suit-tab-pagamenti',),
            fields=(
                'tipo_pagamento_gamma', 'banca_appoggio', 'data_anticipo',
                'scaricata'
            ),
        )),
        (_('Note'), dict(
            classes=('suit-tab suit-tab-note',),
            fields=(
                'note',
                'note_pagamento',
                'note_fattura',
            ),
        )),
        (_('Corpo'), dict(
            classes=('suit-tab suit-tab-corpo',),
            fields=(('cig', 'cup', 'numero_documento_ordine'),),
        )),
        (_('Dati Accompagnatoria'), dict(
            classes=('suit-tab suit-tab-dati_accompagnatoria',),
            fields=(
                'accompagnatoria', 'vettore', 'aspetto_esteriore',
                'numero_colli', 'peso'
            ),
        )),
        (_('Dati Invio SDI'), dict(
            classes=('suit-tab suit-tab-trasmissioni_sdi',),
            fields=(
                (
                    ('bollo_virtuale', 'importo_bollo'),
                    'riferimento_amministrazione',
                    'inviata_pa',
                    'stato_invio_sdi',
                )
            ),
        )),
    )
    list_filter = (
        'pagata', 'scadenze_complete', 'esenzione_iva', 'banca_appoggio',
        'inviata_pa', FatturaScuoleFilter, 'stato_invio_sdi',
    )


class FatturaMastertrainingAdmin(FatturaAdmin):
    list_display = (
        'get_numero', 'data', 'get_ragione_sociale_link',
        'get_totale_display', 'get_importo_pagato_display',
        'get_data_ultimo_pagamento', 'banca_appoggio', 'data_anticipo',
        'stato_sdi_display',
    )
    fieldsets = (
        (None, {
            'classes': ('suit-tab suit-tab-testata',),
            'fields': (('data', 'numero', 'postfisso'),
                       'cliente',
                       ),
        }),
        (_('Dati Cliente'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                ('ragione_sociale'),
                ('indirizzo', 'telefono'),
                ('citta', 'cap', 'provincia'),
                'stato',
                ('partita_iva', 'codice_fiscale'),
                'sede_cliente',
                'destinazione_documento',
            ),
        )),
        (_('Dati Banca Cliente (RIBA)'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=('banca_cliente', ('iban', 'abi', 'cab')),
        )),
        (_('Dati Banca Cliente (RID)'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=('banca_cliente_rid', ('iban_rid', 'abi_rid', 'cab_rid')),
        )),
        (_('Invio per Email'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                (
                    ('invio_elettronico', 'inviata',),
                )
            ),
        )),
        (_('Totali'), dict(
            classes=('suit-tab suit-tab-testata',),
            fields=(
                ('divisa', 'cambio_divisa'),
                ('imponibile', 'omaggio', 'traffico_voip', 'flat'),
                ('iva', 'esenzione_iva', 'iva_esigibilita_differita', 'split_payment'),
                'totale',
            ),
        )),
        (_('Dati Pagamento'), dict(
            classes=('suit-tab suit-tab-pagamenti',),
            fields=(
                'tipo_pagamento_gamma', 'banca_appoggio', 'data_anticipo',
                'scaricata'
            ),
        )),
        (_('Note'), dict(
            classes=('suit-tab suit-tab-note',),
            fields=(
                'note',
                'note_pagamento',
                'note_fattura',
            ),
        )),
        (_('Corpo'), dict(
            classes=('suit-tab suit-tab-corpo',),
            fields=(('cig', 'cup', 'numero_documento_ordine'),),
        )),
        (_('Dati Accompagnatoria'), dict(
            classes=('suit-tab suit-tab-dati_accompagnatoria',),
            fields=(
                'accompagnatoria', 'vettore', 'aspetto_esteriore',
                'numero_colli', 'peso'
            ),
        )),
        (_('Dati Invio SDI'), dict(
            classes=('suit-tab suit-tab-trasmissioni_sdi',),
            fields=(
                (
                    ('bollo_virtuale', 'importo_bollo'),
                    'riferimento_amministrazione',
                    'inviata_pa',
                    'stato_invio_sdi',
                )
            ),
        )),
    )
    list_filter = (
        'pagata', 'scadenze_complete', 'esenzione_iva', 'banca_appoggio',
        'inviata_pa', FatturaScuoleFilter, 'stato_invio_sdi',
    )


class TipoPagamentoAdmin(admin.ModelAdmin):
    list_display = ('nome', 'codice_pagamento_sdi')
    search_fields = ('nome',)


class ScaglionePagamentoTemplateInline(admin.TabularInline):
    model = ScaglionePagamentoTemplate


class TipoPagamentoGammaAdmin(admin.ModelAdmin):
    list_display = ('nome', 'codice')
    search_fields = ('codice', 'nome')
    inlines = [ScaglionePagamentoTemplateInline]


class PianoContiGammaAdmin(admin.ModelAdmin):
    list_display = ('codice', 'nome', 'attivo', 'natura_conto')
    list_filter = ('attivo', 'natura_conto')
    search_fields = ('codice', 'nome')


class PianoContiCostiAdmin(admin.ModelAdmin):
    list_display = ('codice', 'nome', 'attivo', 'natura_conto')
    list_filter = ('attivo', 'natura_conto')
    search_fields = ('codice', 'nome')


class PianoContiRicaviAdmin(admin.ModelAdmin):
    list_display = ('codice', 'nome', 'attivo', 'natura_conto')
    list_filter = ('attivo', 'natura_conto')
    search_fields = ('codice', 'nome')


class EsenzioneIvaAdmin(admin.ModelAdmin):
    list_display = ('nome', 'descrizione', 'codice_gamma', 'get_natura_display', 'dichiarazione_intento')
    search_fields = ('nome', 'descrizione', 'codice_gamma')
    list_filter = ('natura_esenzione_sdi', 'codice_gamma', 'dichiarazione_intento')
    form = forms.EsenzioneIvaForm


class BancaAppoggioAdmin(admin.ModelAdmin):
    list_display = ('codice', 'nome', 'iban', 'codice_gamma')
    search_fields = ('codice', 'nome', 'iban')


class CodiceScaglionePagamentoAdmin(admin.ModelAdmin):
    list_display = ('nome', 'giorni', 'mesi', 'anni', 'fine_mese', 'giorni_oltre_fine_mese')
    search_fields = ('nome',)
    list_filter = ('mesi', 'fine_mese')


class ScadenzaPagamentoAdmin(MastergestAdmin):
    list_display = (
        'data', 'get_anno_numero', 'get_data_fattura', 'get_ragione_sociale',
        'tipo_pagamento', 'importo', 'pagata', 'gestione_azienda'
    )
    search_fields = ('fattura__cliente__ragione_sociale', 'fattura__cliente__alias', 'fattura__numero', )
    list_filter = ('pagata', 'tipo_pagamento', 'data', 'fattura__gestione_azienda')
    date_hierarchy = 'data'
    actions = ['paga_scadenze', 'export_to_csv', 'export_incassi_csv', 'export_saldo_csv']
    readonly_fields = (
        'fattura', 'codice_scaglione_pagamento', 'data', 'get_anno_numero',
        'get_data_fattura', 'get_ragione_sociale', 'tipo_pagamento',
        'importo', 'pagata', 'storno'
    )
    list_max_show_all = 10000
    list_per_page = 50
    form = forms.ScadenzaPagamentoForm

    def gestione_azienda(self, obj):
        return obj.fattura.gestione_azienda
    gestione_azienda.short_description = 'Azienda'

    def paga_scadenze(self, request, queryset):
        for scadenza in queryset:
            oggi = timezone.now().date()
            nuovo_incasso = Incasso.objects.create(
                data=oggi,
                importo=scadenza.importo,
                banca=scadenza.fattura.banca_appoggio,
                anagrafica=scadenza.fattura.cliente,
                causale='Pagamento scadenza %s' % scadenza
            )
            nuovo_incasso.save()
            nuovo_abbinamento = Abbinamento.objects.create(
                incasso=nuovo_incasso,
                scadenza=scadenza,
                importo=scadenza.importo
            )
            nuovo_abbinamento.save()
            msg = "Pagata scadenza %s" % scadenza
            messages.success(request, msg)
    paga_scadenze.short_description = 'Paga Scadenze Selezionate'

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_scadenze.csv"'
        writer = csv.writer(response, delimiter=';')
        writer.writerow([
            'ID',
            'Data Scadenza',
            'Anno/Numero Fattura',
            'Data Fattura',
            'Ragione Sociale',
            'Tipo Pagamento',
            'Importo',
            'Pagata'
        ])
        for scadenza in queryset:
            writer.writerow([
                scadenza.id,
                scadenza.data,
                scadenza.get_anno_numero(),
                scadenza.get_data_fattura(),
                scadenza.get_ragione_sociale(),
                scadenza.tipo_pagamento,
                str(scadenza.importo).replace('.', ','),
                scadenza.pagata
            ])
        return response
    export_to_csv.short_description = 'Esportazione Scadenze Pagamenti'

    def export_incassi_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_scadenze_incassi.csv"'
        writer = csv.writer(response, delimiter=';')
        writer.writerow([
            'ID',
            'Data Scadenza',
            'Anno/Numero Fattura',
            'Data Fattura',
            'Ragione Sociale',
            'Tipo Pagamento',
            'Importo',
            'Pagata',
            'incassato',
            'causale incasso',
            'data incasso',
            'banca incasso'
        ])
        for scadenza in queryset:
            abbinamenti = Abbinamento.objects.filter(scadenza=scadenza)
            if abbinamenti:
                incasso = abbinamenti[0].incasso
                writer.writerow([
                    scadenza.id,
                    scadenza.data,
                    scadenza.get_anno_numero(),
                    scadenza.get_data_fattura(),
                    scadenza.get_ragione_sociale(),
                    scadenza.tipo_pagamento,
                    str(scadenza.importo).replace('.', ','),
                    scadenza.pagata,
                    'si',
                    incasso.data,
                    incasso.banca
                ])
            else:
                writer.writerow([
                    scadenza.id,
                    scadenza.data,
                    scadenza.get_anno_numero(),
                    scadenza.get_data_fattura(),
                    scadenza.get_ragione_sociale(),
                    scadenza.tipo_pagamento,
                    str(scadenza.importo).replace('.', ','),
                    scadenza.pagata,
                    '',
                    '',
                    '',
                    ''
                ])
        return response
    export_incassi_csv.short_description = 'Esportazione Scadenze Pagamenti (con incassi)'

    def export_saldo_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_saldo.csv"'
        elenco_scadenze = ScadenzaPagamento.objects.filter(fattura__data__lte=datetime.date(2013, 12, 31))
        writer = csv.writer(response, delimiter=';')
        writer.writerow([
            'ID',
            'Data Scadenza',
            'Anno/Numero Fattura',
            'Data Fattura',
            'Ragione Sociale',
            'Tipo Pagamento',
            'Importo',
        ])
        for scadenza in elenco_scadenze:
            abbinamenti = Abbinamento.objects.filter(scadenza=scadenza)
            if abbinamenti:
                incasso = abbinamenti[0].incasso
                if incasso.data > datetime.date(2013, 12, 31):
                    writer.writerow([
                        scadenza.id,
                        scadenza.data,
                        scadenza.get_anno_numero(),
                        scadenza.get_data_fattura(),
                        scadenza.get_ragione_sociale(),
                        scadenza.tipo_pagamento,
                        str(scadenza.importo).replace('.', ','),
                    ])
            else:
                writer.writerow([
                    scadenza.id,
                    scadenza.data,
                    scadenza.get_anno_numero(),
                    scadenza.get_data_fattura(),
                    scadenza.get_ragione_sociale(),
                    scadenza.tipo_pagamento,
                    str(scadenza.importo).replace('.', ','),
                ])
        return response
    export_saldo_csv.short_description = 'Esportazione Saldo (al 31/12/2013)'


class FatturaScadenzeAdmin(FatturaAdmin):
    list_display = (
        'get_numero', 'data', 'get_ragione_sociale_link',
        'get_totale_display', 'banca_appoggio', 'tipo_pagamento_gamma',
        'get_pagamenti_display'
    )
    list_editable = ('tipo_pagamento_gamma',)
    list_filter = ('tipo_pagamento_gamma',)
    date_hierarchy = 'data'


class RigaPianoContiGammaAdmin(admin.ModelAdmin):
    list_display = (
        'get_anno_numero', 'data_fattura', 'descrizione', 'quantita',
        'costo_unitario', 'piano_conti_gamma'
    )
    list_editable = ('piano_conti_gamma',)
    list_filter = ('piano_conti_gamma', 'fattura__data', 'fattura__gestione_azienda')
    search_fields = (
        'piano_conti_gamma__nome', 'descrizione', 'fattura__ragione_sociale',
        'fattura__data', 'fattura__anno_numero'
    )
    actions = ['export_to_csv']

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_fatture.csv"'
        writer = csv.writer(response, delimiter=';')
        writer.writerow([
            'ID Fattura',
            'Numero Fattura',
            'Cliente',
            'Data Fattura',
            'Descrizione Riga',
            'quantita',
            'Costo unitario',
            'Percentuale IVA',
            'totale riga',
            'seriale',
            'piano dei conti'
        ])
        for riga in queryset:
            writer.writerow([
                riga.fattura.id,
                riga.fattura.get_anno_numero(),
                riga.fattura.ragione_sociale,
                riga.fattura.data.strftime("%d/%m/%Y"),
                riga.descrizione.replace('\n', '').replace('\r', ''),
                str(riga.quantita).replace('.', ','),
                str(riga.costo_unitario).replace('.', ','),
                riga.percentuale_iva,
                str(riga.get_totale_riga()).replace('.', ','),
                riga.seriale,
                riga.piano_conti_gamma,
            ])
        return response
    export_to_csv.short_description = 'Esportazione Righe Fatture'
