from optparse import make_option

from django.core.management.base import BaseCommand
from django.utils import timezone

from mastergest.fatturazione.models import Fattura
from mastergest.fatturazione.utils import write_fattura_pdf


class Command(BaseCommand):
    option_list = BaseCommand.option_list + (
        make_option(
            '-s', '--start', action='store', dest='start',
            default=None, help='Chiave di partenza'
        ),
        make_option(
            '-t', '--stop', action='store', dest='stop',
            default=None, help='Chiave di arrivo'
        ),
    )
    help = 'Rigenerazione pdf fatture.'
    requires_system_checks = False

    def handle(self, *app_labels, **options):
        verbosity = int(options.get('verbosity', 1))
        fatture = Fattura.objects.all().order_by('-pk')
        if options['start']:
            fatture = fatture.filter(pk__gte=int(options['start']))
        if options['stop']:
            fatture = fatture.filter(pk__lte=int(options['stop']))
        for i, fattura in enumerate(fatture.order_by('-pk')):
            if verbosity > 0:
                print('%5d - %s - pk: %s - fattura %s del %s' % (
                    i, timezone.now().time(), fattura.pk,
                    fattura.get_anno_numero(), fattura.data
                ))
            try:
                write_fattura_pdf(fattura)
            except:
                print('Rigenerazione interrotta alla fattura', fattura.pk, fattura)
                print('Ripartire con il parametro --start=%d' % fattura.id)
                raise
