import vobject
from datetime import date, datetime
from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.core.exceptions import ValidationError
from django.utils.html import format_html

from mastergest.anagrafe.models import Anagrafica, Sede, Stato
from mastergest.fatturazione import validators
from mastergest.fatturazione.constants import IVA_DEFAULT
from mastergest.fatturazione.reports import FatturaPdf
from mastergest.fatturazione.utils import format_riga_fattura_quantita
from mastergest.fatturazione.utils import smart_truncate
from mastergest.utils.dates import calcola_data_scadenza
from mastergest.utils.currency import get_stringa_importo
from mastergest.utils.xml import pulisci_stringa_xml
from mastergest.sdi.constants import NATURA_CASSA_PREVIDENZIALE, STATO_TRASMISSIONE
from mastergest.utils.web import internal_link
from mastergest.anagrafe.models import TIPO_DIVISA, GestioneAziendaAware

getcontext().rounding = ROUND_HALF_UP

SIMBOLO_EURO = '\u20ac'
DECIMAL_ZERO = Decimal('0.00')
TIPI_PAGAMENTO_FATTURA = (
    ('0', 'No'),
    ('1', 'Si'),
    ('2', 'Parz.'),
)

NATURA_CONTO = (
    ('patrimoniale', 'PATRIMONIALE'),
    ('liquidita', 'LIQUIDITA\''),
    ('economico', 'ECONOMICO'),
    ('costi', 'COSTO'),
    ('ricavi', 'RICAVO'),
    ('ordine', 'D\'ORDINE'),
)


class CodiceScaglionePagamento(models.Model):
    nome = models.CharField(max_length=50)
    giorni = models.PositiveIntegerField(
        'giorni dilazione', blank=True, default=0)
    mesi = models.PositiveIntegerField('mesi dilazione', blank=True, default=0)
    anni = models.PositiveIntegerField('anni dilazione', blank=True, default=0)
    fine_mese = models.BooleanField('fine mese', default=True)
    giorni_oltre_fine_mese = models.PositiveIntegerField(
        'giorni oltre fine mese', blank=True, default=0)

    class Meta:
        verbose_name_plural = 'codici scaglioni pagamenti'
        ordering = ('mesi', 'anni')

    def __str__(self):
        return str(self.nome)


class EsenzioneIva(models.Model):
    nome = models.CharField(max_length=50)
    descrizione = models.CharField(max_length=200)
    codice_gamma = models.CharField(max_length=50, null=True, blank=True)
    natura_esenzione_sdi = models.CharField(
        max_length=50, default='N4', choices=NATURA_CASSA_PREVIDENZIALE
    )
    dichiarazione_intento = models.BooleanField(default=False)
    numero_protocollo = models.CharField(max_length=60, null=True, blank=True)
    data_protocollo = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name_plural = 'esenzioni iva'
        ordering = ('nome',)

    def __str__(self):
        return str(self.nome)

    def get_natura_display(self):
        return self.natura_esenzione_sdi
    get_natura_display.short_description = 'Natura es. SDI'
    get_natura_display.admin_order_field = 'natura_esenzione_sdi'

    def get_riferimento_esenzione_xml(self):
        descrizione_natura = ''
        if self.descrizione:
            descrizione_natura = self.descrizione
            if len(self.descrizione) > 100:
                descrizione_natura = self.descrizione[0:98] + '..'
        return descrizione_natura


class TipoPagamento(models.Model):
    nome = models.CharField(max_length=50)
    codice_pagamento_sdi = models.CharField(max_length=50, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'Tipi Pagamento'

    def __str__(self):
        return str(self.nome)


class TipoPagamentoGamma(models.Model):
    nome = models.CharField(max_length=255)
    codice = models.CharField('codice gamma', max_length=50)

    class Meta:
        verbose_name_plural = 'Modalita\' di pagamento'
        ordering = ('nome',)

    def __str__(self):
        return str(self.nome)


class ScaglionePagamentoTemplate(models.Model):
    modalita_pagamento = models.ForeignKey(
        TipoPagamentoGamma, on_delete=models.PROTECT
    )
    tipo_pagamento = models.ForeignKey(
        TipoPagamento, on_delete=models.PROTECT
    )
    codice_scaglione_pagamento = models.ForeignKey(
        CodiceScaglionePagamento, on_delete=models.PROTECT
    )
    percentuale_importo = models.PositiveIntegerField(default=100)

    class Meta:
        verbose_name_plural = 'Template scaglioni pagamento'
        ordering = ('tipo_pagamento', 'codice_scaglione_pagamento',)

    def __str__(self):
        return '%s - %s %s' % (self.percentuale_importo, self.tipo_pagamento, self.codice_scaglione_pagamento,)


class PianoContiGamma(models.Model):
    nome = models.CharField(max_length=255)
    codice = models.CharField(max_length=50)
    attivo = models.BooleanField(default=True)
    natura_conto = models.CharField(
        max_length=200, null=True, blank=True, choices=NATURA_CONTO
    )

    class Meta:
        verbose_name_plural = 'Piano dei conti'
        ordering = ('nome',)

    def __str__(self):
        descrizione = '%s (%s)' % (self.nome, self.codice)
        if not self.attivo:
            return '%s NON ATTIVO' % descrizione
        return descrizione


class PianoContiAttivoManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoContiAttivoManager, self).get_queryset()
        return qs.filter(attivo=True)


class PianoContiAttivo(PianoContiGamma):
    objects = PianoContiAttivoManager()

    class Meta:
        proxy = True


class PianoContiCostiManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoContiCostiManager, self).get_queryset()
        return qs.filter(attivo=True, natura_conto='costi')


class PianoContiCosti(PianoContiGamma):
    objects = PianoContiCostiManager()

    class Meta:
        proxy = True


class PianoContiRicaviManager(models.Manager):

    def get_queryset(self):
        qs = super(PianoContiRicaviManager, self).get_queryset()
        return qs.filter(attivo=True, natura_conto='ricavi')


class PianoContiRicavi(PianoContiGamma):
    objects = PianoContiRicaviManager()

    class Meta:
        proxy = True


class BancaAppoggio(models.Model):
    codice = models.CharField(max_length=50)
    nome = models.CharField(max_length=200)
    abi = models.CharField(max_length=5, null=True, blank=True)
    cab = models.CharField(max_length=5, null=True, blank=True)
    conto = models.CharField(max_length=50, null=True, blank=True,)
    iban = models.CharField(max_length=50)
    codice_gamma = models.PositiveIntegerField('codice gamma', null=True, blank=True)
    
    class Meta:
        verbose_name_plural = 'banche appoggio'
        ordering = ('codice',)

    def __str__(self):
        return str(self.codice)


class Fattura(GestioneAziendaAware):
    data = models.DateField(default=date.today)
    numero = models.PositiveIntegerField(null=True, blank=True, db_index=True)
    postfisso = models.CharField(
        'postfisso (bis)', max_length=50, null=True, blank=True
    )
    cliente = models.ForeignKey(
        Anagrafica, null=True, blank=True, on_delete=models.SET_NULL,
        db_index=True,
        limit_choices_to=dict(
            cliente=True, attivo=True, tipo_cliente='cliente'
        )
    )
    ragione_sociale = models.CharField(
        max_length=200, null=True, blank=True, db_index=True
    )
    partita_iva = models.CharField(max_length=200, blank=True)
    codice_fiscale = models.CharField(max_length=50, blank=True)
    indirizzo = models.CharField(max_length=200, blank=True)
    citta = models.CharField(max_length=200, blank=True)
    provincia = models.CharField(max_length=50, blank=True)
    cap = models.CharField(max_length=50, blank=True)
    stato = models.ForeignKey(
        Stato, default=1, on_delete=models.PROTECT
    )
    destinazione_documento = models.TextField(blank=True)
    ritenuta_dacconto = models.DecimalField(
        "ritenuta d'acconto (%s)" % SIMBOLO_EURO, max_digits=9,
        decimal_places=2, null=True, blank=True
    )
    esenzione_iva = models.ForeignKey(
        EsenzioneIva, null=True, blank=True, on_delete=models.PROTECT
    )
    omaggio = models.BooleanField(default=False)
    iva_esigibilita_differita = models.BooleanField(
        "IVA ad esigibilita' differita", default=False
    )
    banca_appoggio = models.ForeignKey(
        BancaAppoggio, null=True, blank=True,
        related_name='banca_appoggio_pk', on_delete=models.PROTECT
    )
    note = models.TextField(blank=True)
    # Invio fattura
    invio_elettronico = models.BooleanField(default=False)
    inviata = models.BooleanField(default=False)
    inviata_pa = models.BooleanField('inviata SDI', default=False)
    # Anticipi
    data_anticipo = models.DateField('data anticipo', null=True, blank=True)
    scaricata = models.BooleanField(default=False)
    # Dati aggiuntivi fattura accompagnatoria
    accompagnatoria = models.BooleanField(default=False)
    vettore = models.CharField(max_length=50, blank=True)
    aspetto_esteriore = models.CharField(
        'aspetto esteriore', max_length=50, blank=True
    )
    numero_colli = models.CharField('numero colli', max_length=50, blank=True)
    peso = models.CharField(max_length=50, blank=True)
    telefono = models.CharField(max_length=200, blank=True)
    # Campi calcolati
    anno = models.PositiveIntegerField(db_index=True, editable=False)
    anno_numero = models.CharField(
        max_length=50, db_index=True, editable=False
    )
    imponibile = models.DecimalField(
        'imponibile (%s)' % SIMBOLO_EURO, max_digits=9, decimal_places=2,
        default=0
    )
    iva = models.DecimalField(
        'totale iva (%s)' % SIMBOLO_EURO, max_digits=9, decimal_places=2,
        default=0
    )
    totale = models.DecimalField(
        'totale fattura (%s)' % SIMBOLO_EURO, max_digits=9, decimal_places=2,
        default=0
    )
    scadenze_complete = models.BooleanField(
        default=False, editable=False, db_index=True
    )
    pagata = models.BooleanField(default=False, editable=False, db_index=True)
    # Pagamento
    storno = models.DecimalField(max_digits=9, decimal_places=2, null=True,
                                 blank=True, editable=False)
    iban = models.CharField(max_length=50, blank=True)
    abi = models.CharField(max_length=5, blank=True)
    cab = models.CharField(max_length=5, blank=True)
    banca_cliente = models.CharField(max_length=200, blank=True)
    iban_rid = models.CharField('Iban', max_length=50, blank=True)
    abi_rid = models.CharField('Abi', max_length=5, blank=True)
    cab_rid = models.CharField('Cab', max_length=5, blank=True)
    banca_cliente_rid = models.CharField(
        'Banca cliente', max_length=200, blank=True
    )
    tipo_pagamento_gamma = models.ForeignKey(
        TipoPagamentoGamma, null=True, blank=True, on_delete=models.PROTECT
    )
    # Appoggio
    note_pagamento = models.TextField('note sul pagamento', blank=True)
    note_fattura = models.TextField('note in fattura', blank=True)
    traffico_voip = models.BooleanField(default=False)
    flat = models.BooleanField(default=False)
    codice_invio_xml = models.CharField(max_length=10, blank=True, null=True)
    cig = models.CharField(max_length=15, blank=True, null=True)
    cup = models.CharField(max_length=15, blank=True, null=True)
    numero_documento_ordine = models.CharField(
        'Num. buono d\'ordine', max_length=20, blank=True, null=True
    )
    split_payment = models.BooleanField(default=False)
    bollo_virtuale = models.BooleanField(default=False)
    importo_bollo = models.DecimalField(
        'importo bollo (%s)' % SIMBOLO_EURO, max_digits=9, decimal_places=2,
        default=0
    )
    stato_invio_sdi = models.CharField(
        max_length=200, choices=STATO_TRASMISSIONE, blank=True, null=True
    )
    divisa = models.CharField(
        max_length=200, choices=TIPO_DIVISA, default='EUR'
    )
    cambio_divisa = models.DecimalField(
        max_digits=9, decimal_places=6, blank=True, null=True,
        verbose_name='Cambio Attuale Divisa (EUR)',
        help_text='Valore in Euro della divisa selezionata al cambio attuale: es. CHF 0.84 -> 1 CHF = 0.84 Euro'
    )
    riferimento_amministrazione = models.CharField(max_length=20, blank=True, null=True)
    sede_cliente = models.ForeignKey(
        Sede, null=True, blank=True, on_delete=models.PROTECT
    )

    class Meta:
        unique_together = (('anno', 'numero', 'postfisso', 'gestione_azienda'),)
        verbose_name_plural = 'fatture'
        ordering = ('-anno_numero',)

    def __str__(self):
        if self.cliente:
            return 'num. %s del %s - %s' % (
                self.get_numero(), self.data, self.cliente.ragione_sociale
            )
        else:
            return ''

    def save(self, *args, **kwargs):
        self.anno = self.data.year
        if not self.ragione_sociale and self.cliente:
            if self.cliente.titolo:
                self.ragione_sociale = '%s %s' % (
                    self.cliente.titolo, self.cliente.ragione_sociale.strip())
            else:
                self.ragione_sociale = self.cliente.ragione_sociale.strip()
            self.indirizzo = self.cliente.indirizzo
            self.citta = self.cliente.citta
            if self.cliente.cap:
                self.cap = self.cliente.cap.strip()
            self.provincia = self.cliente.get_provincia()
            self.telefono = self.cliente.telefono
            if self.sede_cliente:
                self.iban = self.sede_cliente.iban
                self.banca_cliente = self.sede_cliente.banca
                self.iban_rid = self.sede_cliente.iban_rid
                self.banca_cliente_rid = self.sede_cliente.banca_rid
            else:
                self.iban = self.cliente.iban
                self.banca_cliente = self.cliente.banca
                self.iban_rid = self.cliente.iban_rid
                self.banca_cliente_rid = self.cliente.banca_rid
            self.stato = self.cliente.stato
            self.divisa = self.cliente.divisa_fatturazione
            if not self.cambio_divisa:
                self.cambio_divisa = self.cliente.cambio_divisa
            if self.cliente.partita_iva:
                self.partita_iva = self.cliente.partita_iva.strip()
            if self.cliente.codice_fiscale:
                self.codice_fiscale = self.cliente.codice_fiscale.strip()
            if self.sede_cliente:
                self.destinazione_documento = self.sede_cliente.get_indirizzo()
            else:
                self.destinazione_documento = self.cliente.get_indirizzo_destinazione_documenti()
            self.split_payment = self.cliente.split_payment
            if self.gestione_azienda == 'mastertraining':
                self.esenzione_iva = self.cliente.esenzione_iva_default
            else:
                self.esenzione_iva = self.cliente.esenzione_iva_default_magister
            self.note_fattura = self.cliente.note_default_fattura
        if not self.riferimento_amministrazione:
            if self.sede_cliente:
                if self.sede_cliente.riferimento_amministrazione:
                    self.riferimento_amministrazione = self.sede_cliente.riferimento_amministrazione
            if not self.riferimento_amministrazione:
                if self.cliente:
                    if self.cliente.riferimento_amministrazione:
                        self.riferimento_amministrazione = self.cliente.riferimento_amministrazione
        if not self.destinazione_documento:
            if self.sede_cliente:
                self.destinazione_documento = self.sede_cliente.get_indirizzo()
            else:
                self.destinazione_documento = self.cliente.get_indirizzo_destinazione_documenti()
        if not self.numero:
            self.numero = self.get_nuovo_numero()
        self.anno_numero = '%d_%08d' % (self.data.year, self.numero)
        if self.postfisso:
            self.anno_numero += '_%s' % self.postfisso
        self.aggiorna_totali()
        self.aggiorna_da_scadenze()
        if self.esenzione_iva:
            if self.esenzione_iva.natura_esenzione_sdi == 'N3.5':
                if self.totale > Decimal('77.47'):
                    if not self.bollo_virtuale:
                        self.bollo_virtuale = True
                    if not self.importo_bollo:
                        self.importo_bollo = Decimal('2.00')
                else:
                    if self.bollo_virtuale and self.importo_bollo == Decimal('2.00'):
                        self.bollo_virtuale = False
                        self.importo_bollo = Decimal('0.00')
        if not self.abi and not self.cab:
            self.aggiorna_abicab()
        if not self.abi_rid and not self.cab_rid:
            self.aggiorna_abicab_rid()
        # Invio elettronico
        self.invio_elettronico = False
        if self.cliente:
            if self.cliente.email_fatturazione or self.cliente.email_fatturazione_cc:
                self.invio_elettronico = True
        # Pagata
        scadenze_non_pagate = self.scadenzapagamento_set.filter(pagata=False)
        self.pagata = not bool(scadenze_non_pagate)
        # Banca Appoggio e Scaglione Pagamento Default
        if self.cliente and not self.banca_appoggio:
            if self.traffico_voip:
                if self.cliente.banca_appoggio_traffico:
                    self.banca_appoggio = self.cliente.banca_appoggio_traffico
            else:
                banca_appoggio = None
                if self.sede_cliente:
                    if self.gestione_azienda == 'mastertraining':
                        if self.sede_cliente.banca_appoggio:
                            banca_appoggio = self.sede_cliente.banca_appoggio
                    else:
                        if self.sede_cliente.banca_appoggio_magister:
                            banca_appoggio = self.sede_cliente.banca_appoggio_magister
                else:
                    if self.gestione_azienda == 'mastertraining':
                        if self.cliente.banca_appoggio:
                            banca_appoggio = self.cliente.banca_appoggio
                    else:
                        if self.cliente.banca_appoggio_magister:
                            banca_appoggio = self.cliente.banca_appoggio_magister
                self.banca_appoggio = banca_appoggio
        if self.cliente and not self.tipo_pagamento_gamma:
            if self.traffico_voip:
                if self.cliente.pagamento_default_traffico_voip:
                    self.tipo_pagamento_gamma = self.cliente.pagamento_default_traffico_voip
            else:
                pagamento_default_fatture = None
                if self.sede_cliente:
                    if self.gestione_azienda == 'mastertraining':
                        if self.sede_cliente.pagamento_default_fatture:
                            pagamento_default_fatture = self.sede_cliente.pagamento_default_fatture
                    else:
                        if self.sede_cliente.pagamento_default_fatture_magister:
                            pagamento_default_fatture = self.sede_cliente.pagamento_default_fatture_magister
                else:
                    if self.gestione_azienda == 'mastertraining':
                        if self.cliente.pagamento_default_fatture:
                            pagamento_default_fatture = self.cliente.pagamento_default_fatture
                    else:
                        if self.cliente.pagamento_default_fatture_magister:
                            pagamento_default_fatture = self.cliente.pagamento_default_fatture_magister
                self.tipo_pagamento_gamma = pagamento_default_fatture
        # Save
        obj = super(Fattura, self).save(*args, **kwargs)
        # Pdf
        return obj

    def get_url(self):
        if self.gestione_azienda:
            url = reverse('admin:fatturazione_fattura%s_change' % self.gestione_azienda, args=(self.id,))
        else:
            url = reverse('admin:fatturazione_fattura_change', args=(self.id,))
        return url

    def get_link(self, dettaglio_azienda=False):
        if dettaglio_azienda:
            label = '%s (%s)' % (self.get_anno_numero(), self.gestione_azienda)
        else:
            label = self.get_anno_numero()
        return internal_link(
            url=self.get_url(), title=self.get_excerpt(), label=label
        )
    get_link.short_description = 'Fattura'
    get_link.admin_order_field = 'numero'

    def get_link_azienda(self):
        return self.get_link(dettaglio_azienda=True)
    get_link_azienda.short_description = 'Fattura'
    get_link_azienda.admin_order_field = 'numero'

    @property
    def pubblica_amministrazione(self):
        if self.cliente:
            if self.cliente.pubblica_amministrazione:
                return True
        return False

    @property
    def pec_destinatario(self):
        if self.cliente:
            codice_destinatario = self.get_codice_pa()
            if codice_destinatario == '0000000':
                if self.cliente.codici_pa_sedi:
                    if self.sede_cliente:
                        if self.sede_cliente.pec:
                            return self.sede_cliente.pec
                else:
                    if self.cliente.pec:
                        return self.cliente.pec

    def get_ragione_sociale_link(self):
        if self.cliente:
            url = '?cliente__id__exact=%s' % self.cliente.pk
            return internal_link(url=url, label=self.cliente)
        return self.ragione_sociale
    get_ragione_sociale_link.short_description = 'Cliente'
    get_ragione_sociale_link.admin_order_field = 'cliente'

    def get_ragione_sociale_xml(self):
        return pulisci_stringa_xml(self.ragione_sociale)

    def get_indirizzo_xml(self):
        return pulisci_stringa_xml(self.indirizzo)

    def get_citta_xml(self):
        return pulisci_stringa_xml(self.citta)

    def get_partita_iva_xml(self):
        if self.stato_id == 1:
            return self.partita_iva
        else:
            if self.stato.area == 11:
                return self.partita_iva
            else:
                return 'OO99999999999'

    def get_cap_xml(self):
        if self.stato_id == 1:
            return self.cap
        else:
            return '00000'

    def get_provincia_xml(self):
        if self.stato_id == 1:
            return self.provincia
        else:
            return ''

    def get_codice_fiscale_xml(self):
        if self.stato_id == 1:
            return self.codice_fiscale
        else:
            return None

    def get_imponibile_xml(self):
        return abs(self.imponibile)

    def get_iva_xml(self):
        return abs(self.iva)

    def get_totale_xml(self):
        return abs(self.totale)

    def get_aliquota_iva(self):
        if not self.esenzione_iva:
            aliquota_iva = None
            for riga in self.rigafattura_set.filter(contabile=True):
                iva_riga = riga.percentuale_iva
                if not aliquota_iva:
                    aliquota_iva = iva_riga
                else:
                    if not aliquota_iva == iva_riga:
                        raise ValidationError('Attenzione! Sono presenti due diverse percentuali iva sulla stessa fattura')
            if not aliquota_iva:
                aliquota_iva = '22.00'
            return '%s.00' % aliquota_iva

    def get_codice_pa(self):
        if self.cliente:
            if self.cliente.codici_pa_sedi:
                if self.sede_cliente:
                    if self.sede_cliente.codice_pa:
                        return self.sede_cliente.codice_pa
            if self.cliente.codice_pa:
                return self.cliente.codice_pa
            else:
                if not self.cliente.pubblica_amministrazione:
                    if self.cliente.codici_pa_sedi:
                        if self.sede_cliente:
                            if self.sede_cliente.pec:
                                return '0000000'
                    else:
                        if self.cliente.pec:
                            return '0000000'

    def aggiorna_stato_invio_sdi(self):
        if self.trasmissionefattura_set.all():
            ultima_trasmissione = self.trasmissionefattura_set.all().order_by('-data_trasmissione')[0]
            self.stato_invio_sdi = ultima_trasmissione.stato
            self.save()

    def stato_sdi_display(self):
        if self.stato_invio_sdi:
            if self.stato_invio_sdi in ['accettata', 'decorrenza_termini']:
                immagine_stato = 'pallina_verde.png'
            elif self.stato_invio_sdi in ['rifiutata', 'scartata', 'mancata_consegna']:
                immagine_stato = 'pallina_rossa.png'
            else:
                immagine_stato = 'pallina_gialla.png'
            if self.trasmissionefattura_set.all():
                ultima_trasmissione = self.trasmissionefattura_set.all().order_by('-data_trasmissione')[0]
                url = ultima_trasmissione.get_url()
                return format_html(
                    '<a href="{url}" title="{title}"><img src="/static/img/{image}" height="25" width="25"></a>',
                    url=url, title=self.get_stato_invio_sdi_display(),
                    image=immagine_stato
                )
        return format_html('<img src="/static/img/pallina_vuota.png" height="25" width="25" title="DA INVIARE"/>')
    stato_sdi_display.short_description = 'SDI'

    def get_excerpt(self):
        rows = [
            'numero: %s' % self.get_anno_numero(),
            'data: %s' % self.data.strftime("%d/%m/%Y"),
            'cliente: %s' % self.cliente,
            'imponibile: %s' % self.imponibile,
            'totale: %s' % self.totale,
        ]
        return '\r\n'.join(rows)

    def aggiorna_abicab(self):
        if self.cliente:
            self.abi = self.cliente.abi
            self.cab = self.cliente.cab

    def aggiorna_abicab_rid(self):
        if self.cliente:
            self.abi_rid = self.cliente.abi_rid
            self.cab_rid = self.cliente.cab_rid

    def aggiorna_totali(self):
        self.imponibile = DECIMAL_ZERO
        self.iva = DECIMAL_ZERO
        getcontext().rounding = ROUND_HALF_UP
        for riga in self.get_righe_fattura():
            if not self.omaggio:
                if riga.contabile and riga.costo_unitario:
                    totale_riga = (riga.costo_unitario * riga.quantita)
                    if self.data > date(month=6, year=2012, day=18):
                        self.imponibile += totale_riga.quantize(DECIMAL_ZERO)
                    else:
                        self.imponibile += totale_riga
            if not self.esenzione_iva and not self.omaggio:
                if riga.contabile and riga.costo_unitario:
                    if self.data > date(month=6, year=2012, day=18):
                        costo_totale = (
                            riga.costo_unitario * riga.quantita).quantize(DECIMAL_ZERO)
                    else:
                        costo_totale = (riga.costo_unitario * riga.quantita)
                    percentuale_iva = riga.percentuale_iva / Decimal('100.00')
                    totale_iva_riga = (costo_totale * percentuale_iva)
                    self.iva += totale_iva_riga
        getcontext().rounding = ROUND_HALF_UP
        self.imponibile = self.imponibile.quantize(DECIMAL_ZERO)
        self.iva = self.iva.quantize(DECIMAL_ZERO)
        self.totale = (self.imponibile + self.iva).quantize(DECIMAL_ZERO)

    def get_totale_non_imponibile(self):
        getcontext().rounding = ROUND_HALF_UP
        totale_non_imponibile = DECIMAL_ZERO
        for riga in self.get_righe_fattura():
            if riga.contabile and riga.costo_unitario and riga.percentuale_iva == 0:
                if self.data > date(month=6, year=2012, day=18):
                    totale_non_imponibile = (
                        riga.costo_unitario * riga.quantita).quantize(DECIMAL_ZERO)
                else:
                    totale_non_imponibile = (
                        riga.costo_unitario * riga.quantita)
        return totale_non_imponibile.quantize(DECIMAL_ZERO)

    def aggiorna_da_scadenze(self):
        scadenze = self.scadenzapagamento_set.all()
        aggregate = scadenze.aggregate(
            models.Sum('importo'), models.Sum('storno'))
        totale_scadenze = aggregate['importo__sum'] or Decimal('0.00')
        totale_scadenze = totale_scadenze.quantize(DECIMAL_ZERO)
        self.storno = aggregate['storno__sum'] or Decimal('0.00')
        self.scadenze_complete = True
        if self.totale >= Decimal('0.00'):
            if self.split_payment:
                self.scadenze_complete = bool(
                    self.imponibile == totale_scadenze)
            else:
                self.scadenze_complete = bool(self.totale == totale_scadenze)

    def clean(self):
        if self.cliente:
            if self.cliente.blocco_fatturazione:
                raise ValidationError(
                    'Attenzione! La fatturazione per il cliente %s e\' stata bloccata. Contattare l\'amministrazione.' % self.cliente
                )

    def get_numero(self):
        numero = str(self.numero)
        if self.anno >= 2013:
            numero = '%s/%s' % (str(self.numero), self.anno)
        if self.postfisso:
            return '%s/%s' % (numero, self.postfisso)
        return numero
    get_numero.short_description = 'numero'
    get_numero.admin_order_field = 'numero'

    def get_anno_numero(self):
        numero = str(self.numero)
        if self.anno:
            if self.postfisso:
                return '%d/%s/%s' % (self.anno, numero, self.postfisso)
            return '%d/%s' % (self.anno, numero)
    get_anno_numero.short_description = 'numero'
    get_anno_numero.admin_order_field = 'anno_numero'

    def get_nuovo_numero(self):
        qs = Fattura.objects.filter(data__year=self.data.year, gestione_azienda=self.gestione_azienda)
        if self.pk:
            qs = qs.exclude(pk=self.pk)
        aggregate = qs.aggregate(models.Max('numero'))
        max = int(aggregate.get('numero__max', 0) or 0)
        return max + 1

    def get_indirizzo(self):
        riga_1 = '%s' % self.ragione_sociale.replace("&", "&amp;")
        riga_1.strip()
        riga_2 = self.indirizzo or ''
        riga_2.strip()
        if self.provincia:
            riga_3 = '%s %s (%s)' % (self.cap, self.citta, self.provincia)
            riga_3.strip()
        else:
            riga_3 = '%s %s' % (self.cap, self.citta)
            riga_3.strip()
        indirizzo = '\n'.join([riga_1, riga_2, riga_3])
        return indirizzo

    def get_destinazione_documento(self):
        return self.destinazione_documento or self.get_indirizzo()

    def get_righe_fattura(self):
        return self.rigafattura_set.all()

    def get_righe_fattura_raggruppate(self):
        all_righe = self.rigafattura_set.all().order_by('order', 'id')
        righe_raggruppate = list()
        riga_raggruppata = dict()
        riga_raggruppata['quantita'] = Decimal('0.0')
        riga_raggruppata['seriale'] = list()
        riga_raggruppata['totale_riga'] = Decimal('0.0')
        for i, riga in zip(list(range(len(all_righe))), all_righe):
            riga_raggruppata['fattura_id'] = riga.fattura_id
            next_line = all_righe[
                (i + 1)] if (i + 1) < len(all_righe) else None
            last_line = (i + 1) == len(all_righe)
            if not last_line and\
                    riga.descrizione == next_line.descrizione and\
                    riga.percentuale_iva == next_line.percentuale_iva and\
                    riga.costo_unitario == next_line.costo_unitario:
                """ is a riga to group """
                if riga.seriale:
                    riga_raggruppata['seriale'].append(riga.seriale)
                if riga.contabile:
                    riga_raggruppata['totale_riga'] += riga.get_totale_riga()
                if riga.quantita:
                    riga_raggruppata['quantita'] += riga.quantita
                continue
            riga_raggruppata['descrizione'] = riga.descrizione
            riga_raggruppata['descrizione_xml'] = riga.descrizione[0:100]
            riga_raggruppata['contabile'] = riga.contabile
            riga_raggruppata['costo'] = riga.costo_unitario
            riga_raggruppata['percentuale_iva'] = riga.percentuale_iva
            if riga.seriale:
                riga_raggruppata['seriale'].append(riga.seriale)
            if riga.quantita:
                riga_raggruppata['quantita'] += riga.quantita
            riga_raggruppata['quantita'] = format_riga_fattura_quantita(
                riga_raggruppata['quantita'])
            if riga.get_totale_riga():
                riga_raggruppata['totale_riga'] += riga.get_totale_riga()
            riga_raggruppata['costo_unitario'] = riga_raggruppata['costo']
            riga_raggruppata['totale_pulito'] = str(
                riga_raggruppata['totale_riga']).replace(',', '.')
            righe_raggruppate.append(riga_raggruppata)
            if not last_line:
                """ we prepare riga_raggruppata for next loop """
                riga_raggruppata = dict()
                riga_raggruppata['seriale'] = list()
                riga_raggruppata['quantita'] = Decimal('0.0')
                riga_raggruppata['totale_riga'] = Decimal('0.0')
        return righe_raggruppate

    def get_scadenze_pagamenti(self):
        return self.scadenzapagamento_set.all()

    def get_pagamenti(self):
        pagamenti = []
        scadenze_pagamenti = self.get_scadenze_pagamenti()
        for pagamento in scadenze_pagamenti.values('tipo_pagamento__nome'):
            if pagamento['tipo_pagamento__nome']:
                if pagamento['tipo_pagamento__nome'] not in pagamenti:
                    pagamenti.append(pagamento['tipo_pagamento__nome'])
        if not pagamenti:
            return 'N/A'
        return ' - '.join(pagamenti)

    def get_pagamenti_display(self):
        pagamenti = []
        scadenze_pagamenti = self.get_scadenze_pagamenti()
        if scadenze_pagamenti:
            for pagamento in scadenze_pagamenti:
                pagamenti.append(pagamento.get_nome_completo())
        if not pagamenti:
            return 'N/A'
        return format_html('<BR>'.join(pagamenti))
    get_pagamenti_display.short_description = 'Elenco Scadenze'
    get_pagamenti_display.admin_order_field = 'totale'

    def get_pagata(self):
        if self.pagata:
            return 'si'
        if self.get_importo_pagato() > Decimal('0.00'):
            return 'parziale'
        return 'no'

    def get_totale_display(self):
        css_class = ''
        if self.scadenze_complete:
            css_class = ' class="totale_ok"'
        else:
            css_class = ' class="totale_ko"'
        if self.split_payment:
            return format_html('<div%s>%s (S)</span>' % (css_class, self.imponibile))
        else:
            return format_html('<div%s>%s</span>' % (css_class, self.totale))
    get_totale_display.short_description = 'Totale fattura'
    get_totale_display.admin_order_field = 'totale'

    def get_importo_pagato_display(self):
        css_class = 'pagata_%s' % self.get_pagata()
        return format_html('<div class="%s">%s</span>' % (css_class, self.get_importo_pagato()))
    get_importo_pagato_display.short_description = 'Importo pagato'

    def get_abbinamenti(self):
        from mastergest.incassi.models import Abbinamento
        return Abbinamento.objects.filter(scadenza__fattura=self).order_by('-incasso__data')

    def get_banche_incassi(self):
        from mastergest.incassi.models import Incasso
        banche = Incasso.objects.filter(
            scadenze__in=self.get_scadenze_pagamenti())
        banche = banche.exclude(banca=None)
        banche = banche.values_list('banca__codice', flat=True).distinct()
        return banche

    def get_data_ultimo_pagamento(self):
        if self.get_pagata() in ('si', 'parziale'):
            title = self.get_pagamenti_excerpt()
            abbinamenti = self.get_abbinamenti()
            if abbinamenti.count() > 0:
                title = self.get_pagamenti_excerpt()
                data = abbinamenti[0].incasso.data.strftime("%d/%m/%Y")
                return format_html('<span title="%s">%s</span>' % (title, data))
        return ''
    get_data_ultimo_pagamento.short_description = 'Data ultimo pagamento'

    def get_pagamenti_excerpt(self):
        rows = []
        for abbinamento in self.get_abbinamenti().order_by('incasso__data'):
            rows.append('%s - %.2f' % (
                abbinamento.incasso.data.strftime("%d/%m/%Y"),
                abbinamento.importo))
        return '\n'.join(rows)

    def get_importo_pagato(self):
        pagato = getattr(self, 'importo_pagato', None)
        if pagato is not None:
            return pagato
        pagato = Decimal('0.00')
        for scadenza in self.get_scadenze_pagamenti():
            pagato += scadenza.get_importo_pagato()
        return pagato

    def get_importo_da_pagare(self):
        return self.totale - self.get_importo_pagato()
    get_importo_da_pagare.short_description = 'Importo da pagare'

    def get_pdf(self):
        pdf = FatturaPdf()
        return pdf.genera_pdf(self)

    def get_nome_pdf(self):
        return 'fattura_%s.pdf' % self.get_anno_numero().replace('/', '_')

    def get_elenco_centri_costo(self):
        if self.cliente.gestione_centri_costo:
            elenco_centri_costo = dict()
            for riga in self.rigafattura_set.all():
                if riga.contabile:
                    centro_di_costo = riga.get_centro_costo()
                    if not centro_di_costo:
                        centro_di_costo = 'N/D'
                    if centro_di_costo not in elenco_centri_costo:
                        elenco_centri_costo[centro_di_costo] = DECIMAL_ZERO
                    elenco_centri_costo[centro_di_costo] += riga.get_totale_riga().quantize(DECIMAL_ZERO)
            return elenco_centri_costo


class FatturaScadenze(Fattura):

    class Meta:
        proxy = True
        verbose_name_plural = 'Associa Pagamenti GAMMA'
        ordering = ('-anno_numero',)


class FatturaMastertrainingManager(models.Manager):
    
        def get_queryset(self):
            qs = super(FatturaMastertrainingManager, self).get_queryset()
            return qs.filter(gestione_azienda='mastertraining')


class FatturaMastertraining(Fattura):
    objects = FatturaMastertrainingManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Fatture Mastertraining'
        
    def save(self, *args, **kwargs):
        self.gestione_azienda = 'mastertraining'
        super(FatturaMastertraining, self).save(*args, **kwargs)


class FatturaMagisterManager(models.Manager):
    
    def get_queryset(self):
        qs = super(FatturaMagisterManager, self).get_queryset()
        return qs.filter(gestione_azienda='magister')


class FatturaMagister(Fattura):
    objects = FatturaMagisterManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Fatture Magister'
        
    def save(self, *args, **kwargs):
        self.gestione_azienda = 'magister'
        super(FatturaMagister, self).save(*args, **kwargs)


class RigaFattura(models.Model):
    fattura = models.ForeignKey(
        Fattura, db_index=True, on_delete=models.CASCADE
    )
    seriale = models.CharField(max_length=50, blank=True)
    descrizione = models.TextField(blank=True)
    quantita = models.DecimalField(
        max_digits=9, decimal_places=2, null=True, blank=True, default=1
    )
    costo_unitario = models.DecimalField(
        'costo unitario (%s)' % SIMBOLO_EURO, max_digits=9, decimal_places=3,
        null=True, blank=True
    )
    percentuale_iva = models.PositiveIntegerField(
        'percentuale iva', default=IVA_DEFAULT, null=True, blank=True
    )
    contabile = models.BooleanField('contabile', default=True)
    piano_conti_gamma = models.ForeignKey(
        PianoContiGamma, null=True, blank=True, on_delete=models.PROTECT
    )
    order = models.PositiveIntegerField(default=0)
    sede_cliente = models.ForeignKey(
        Sede, null=True, blank=True, on_delete=models.PROTECT
    )

    class Meta:
        verbose_name_plural = 'righe fattura'
        ordering = ('fattura', 'order', 'id')

    def __str__(self):
        return 'N. %s' % str(self.order + 1)

    def save(self, *args, **kwargs):
        self.full_clean()
        super(RigaFattura, self).save(*args, **kwargs)
        self.fattura.save()

    def get_descrizione_xml(self):
        descrizione_pulita = pulisci_stringa_xml(self.descrizione)
        if len(descrizione_pulita) > 1000:
            descrizione_pulita = descrizione_pulita[0:997] + '...'
        return descrizione_pulita

    def get_estensione_descrizione_xml(self):
        if len(self.descrizione) > 100:
            estensione_descrizione_xml = list()
            descrizione_xml = self.get_descrizione_xml()
            rimanenza = self.descrizione[len(descrizione_xml) - 3:]
            while len(rimanenza) > 0:
                if len(rimanenza) > 60:
                    blocco = smart_truncate(rimanenza, 60, '...')
                    estensione_descrizione_xml.append(str(blocco))
                    rimanenza = rimanenza[len(blocco) - 3:]
                else:
                    estensione_descrizione_xml.append(rimanenza)
                    rimanenza = ''
            return estensione_descrizione_xml

    def clean(self):
        validators.clean_rigafattura_contabile(
            self.contabile, self.quantita, self.costo_unitario,
            self.percentuale_iva
        )

    def get_totale_riga(self):
        totale_riga = None
        if self.contabile:
            totale_riga = (self.costo_unitario * self.quantita)
            if totale_riga:
                totale_riga = totale_riga.quantize(DECIMAL_ZERO)
        return totale_riga

    def get_costo_stringa(self):
        return get_stringa_importo(
            importo=self.costo_unitario, con_valuta=True
        )

    def get_quantita_stringa(self):
        return format_riga_fattura_quantita(self.quantita)

    def get_anno_numero(self):
        return '%s - %s' % (self.fattura.get_anno_numero(), self.fattura.ragione_sociale)
    get_anno_numero.short_description = 'fattura'
    get_anno_numero.admin_order_field = 'fattura'

    def data_fattura(self):
        return '%s' % (self.fattura.data)
    data_fattura.short_description = 'data fattura'
    data_fattura.admin_order_field = 'fattura'

    def get_centro_costo(self):
        if self.sede_cliente:
            if self.sede_cliente.centro_costo:
                return '%s' % self.sede_cliente.centro_costo
        return 'N/D'

    def get_centro_costo_display(self):
        return self.get_centro_costo()
    get_centro_costo_display.short_description = 'Centro di Costo'

    def get_costo_unitario_xml(self):
        if self.fattura.totale > 0:
            return self.costo_unitario
        else:
            return abs(self.costo_unitario)

    def get_totale_riga_xml(self):
        totale_riga = self.get_totale_riga()
        if self.fattura.totale > 0:
            return totale_riga
        else:
            return abs(totale_riga)


class RigaPianoContiGammaManager(models.Manager):

    def get_queryset(self):
        qs = super(RigaPianoContiGammaManager, self).get_queryset()
        return qs.filter(contabile=True)


class RigaPianoContiGamma(RigaFattura):
    objects = RigaPianoContiGammaManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Associa Piano Conti Gamma'
        ordering = ('fattura', 'descrizione',)


class ScadenzaPagamento(models.Model):
    fattura = models.ForeignKey(Fattura, on_delete=models.CASCADE)
    tipo_pagamento = models.ForeignKey(
        TipoPagamento, null=True, blank=True, on_delete=models.PROTECT
    )
    codice_scaglione_pagamento = models.ForeignKey(
        CodiceScaglionePagamento, null=True, blank=True,
        on_delete=models.PROTECT
    )
    data = models.DateField(null=True, blank=True)
    importo = models.DecimalField(
        'importo (%s)' % SIMBOLO_EURO, max_digits=9, decimal_places=2,
        null=True, blank=True
    )
    storno = models.DecimalField(
        max_digits=9, decimal_places=2, null=True, blank=True
    )
    pagata = models.BooleanField(default=False, editable=False, db_index=True)

    class Meta:
        verbose_name_plural = 'scadenze pagamenti'
        ordering = ('-data', '-fattura')

    def get_nome_scadenza(self):
        data_stringa = '%s/%s/%s' % (self.data.day,
                                     self.data.month, self.data.year)
        return '%s al %s %s %s' % (
            self.tipo_pagamento, data_stringa, self.importo, SIMBOLO_EURO
        )

    def get_nome_completo(self):
        data_stringa = '%s/%s/%s' % (
            self.data.day, self.data.month, self.data.year
        )
        return '%s %s (%s - %s %s)' % (
            self.tipo_pagamento, self.codice_scaglione_pagamento, data_stringa,
            self.importo, SIMBOLO_EURO
        )

    def __str__(self):
        return '%s - %s' % (
            self.fattura.get_anno_numero(), self.get_nome_scadenza()
        )

    def save(self, **kwargs):
        if self.codice_scaglione_pagamento and not self.data:
            self.data = calcola_data_scadenza(data_iniziale=self.fattura.data,
                                              giorni_scadenza=self.codice_scaglione_pagamento.giorni,
                                              mesi_scadenza=self.codice_scaglione_pagamento.mesi,
                                              anni_scadenza=self.codice_scaglione_pagamento.anni,
                                              fine_mese=self.codice_scaglione_pagamento.fine_mese,
                                              giorni_oltre_fine_mese=self.codice_scaglione_pagamento.giorni_oltre_fine_mese)
        if not self.importo or self.importo == 0:
            if self.fattura.split_payment:
                self.importo = self.fattura.imponibile
            else:
                self.importo = self.fattura.totale
        self.aggiorna_campo_pagata()
        self.full_clean()
        instance = super(ScadenzaPagamento, self).save(**kwargs)
        self.fattura.save()
        return instance

    def clean(self):
        validators.clean_scadenzapagamento_importo(self.importo, self.fattura,
                                                   self.pk)
        validators.clean_scadenzapagamento_storno(
            self.storno, self.importo, self)

    def get_ragione_sociale(self):
        return self.fattura.ragione_sociale
    get_ragione_sociale.short_description = 'Cliente'

    def get_anno_numero(self):
        return self.fattura.get_anno_numero()
    get_anno_numero.short_description = 'Anno/Numero'
    get_anno_numero.admin_order_field = 'fattura__anno_numero'

    def get_data_fattura(self):
        return self.fattura.data
    get_data_fattura.short_description = 'Data Fattura'
    get_data_fattura.admin_order_field = 'fattura__data'

    def get_importo_xml(self):
        return abs(self.importo)

    def aggiorna_campo_pagata(self):
        abbinamenti = self.abbinamento_set.all()
        totale = abbinamenti.aggregate(models.Sum('importo'))['importo__sum'] \
            or Decimal('0.00')
        storno = self.storno or Decimal('0.00')
        self.pagata = bool(totale == (self.importo - storno))

    def get_importo_pagato(self):
        abbinamenti = self.abbinamento_set.all()
        pagato = abbinamenti.aggregate(models.Sum('importo'))['importo__sum'] \
            or Decimal('0.00')
        if self.storno:
            pagato += self.storno
        return pagato

    def get_importo_da_pagare(self):
        return self.importo - self.get_importo_pagato()

    def get_vcalendar(self):
        cal = vobject.iCalendar()
        cal.add('vevent')
        summary = 'Scadenza fattura Mastertraining'
        cal.vevent.add('summary').value = summary
        d = self.data
        start = datetime(d.year, d.month, d.day, 9)
        cal.vevent.add('dtstart').value = start
        description = 'Scadenza pagamento fattura Mastertraining numero %s del %s' \
            ' Importo: %.2f da pagare entro il %s' % (self.fattura.get_numero(),
                                                      self.fattura.data.strftime(
                                                          "%d/%m/%Y"), self.importo,
                                                      self.data.strftime("%d/%m/%Y"))
        cal.vevent.add('description').value = description
        return cal.serialize()
