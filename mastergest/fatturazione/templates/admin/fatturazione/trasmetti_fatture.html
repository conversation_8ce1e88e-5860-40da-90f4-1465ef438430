{% extends "admin/base_site.html" %}
{% load i18n l10n %}
{% load admin_urls %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
        &rsaquo; {% trans 'Trasmetti Fatture Selezionate' %}
    </div>
{% endblock %}

{% block content %}

<div>
    <h3>{% trans 'Inserire Alias e Pin per la firma delle fatture' %}:</h3>
</div>

<form action="" method="post">
    <div>
        {% csrf_token %}
        {{ trasmetti_fatture_form }}
    </div>
    <div>
        <fieldset class="module">
            <h2>Fatture da trasmettere:</h2>
            <table id="result_list" class="table table-striped table-bordered table-hover table-condensed">
                <thead>
                <tr>
                    <th width='10%' align='center'>Numero</th>
                    <th width='10%' align='center'>Data</th>
                    <th width='*' align='center'>Ragione Sociale</th>
                    <th width='5%' align='center'>Codice Dest.</th>
                    <th width='5%' align='center'>Pubblica Amm.</th>
                    <th width='10%' align='center'>Imponibile</th>
                    <th width='10%' align='center'>Totale</th>
                </tr>
                </thead>
            <tbody>
                {% for fattura in elenco_fatture %}
                <tr>
                    <td align='center'>
                        {{ fattura.get_numero }}
                    </td>
                    <td align='left'>
                        {{ fattura.data }}
                    </td>
                    <td align='center'>
                        {{ fattura.ragione_sociale }}
                    </td>
                    {% if fattura.get_codice_pa %}
                    <td align='left'>
                        {{ fattura.get_codice_pa }}
                    </td>
                    {% else %}
                    <td align='left'>
                        <font color='#ff0000'>N/D</font>
                    </td>
                    {% endif %}
                    <td align='center'>
                        {% if fattura.pubblica_amministrazione %}
                        <img src="/static/admin/img/icon-yes.svg" alt="True">
                        {% else %}
                        <img src="/static/admin/img/icon-no.svg" alt="False">
                        {% endif %}
                    </td>
                    <td align='left'>
                        {{ fattura.imponibile|safe }}
                    </td>
                    <td align='left'>
                        {{ fattura.totale|safe }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            </table>
        </fieldset>
    </div>

    <input type="hidden" name="action" value="trasmetti_fatture_sdi" />
    <input type="submit" name="trasmetti" value="{% trans 'Trasmetti Fatture' %}" />
</form>

{% endblock %}

