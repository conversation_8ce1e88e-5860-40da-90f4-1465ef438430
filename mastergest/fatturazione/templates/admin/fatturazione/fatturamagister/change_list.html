{% extends "admin/change_list.html" %}
{% load  i18n suit_tags %}

{% block extrastyle %}
{{ block.super }}
<style type="text/css">
	.pagata_si {text-align:right; background-color:lime; padding:0.3em 0.5em;}
	.pagata_parziale {text-align:right; background-color:yellow; padding:0.3em 0.5em;}
	.pagata_no {text-align:right; background-color:red; color:black; padding:0.3em 0.5em;}
    .totale_ok {text-align:right; padding:0.3em 0.5em;}
	.totale_ko {text-align:right; background-color:red; color:black; padding:0.3em 0.5em;}
</style>
<style>
    body {
        background-color: {{ 'MAGISTER_BACKGROUND_COLOR'|suit_conf }};
    }
</style>
{% endblock %}


{% block object-tools %}
{% if has_add_permission %}
<ul class="object-tools">
  <li><a href="{% url 'admin:fatturazione_fatturamagister_general_odt' 'rid' %}{{ cl.get_query_string }}">{% blocktrans %}Stampa RID{% endblocktrans %}</a></li>
  <li><a href="{% url 'admin:fatturazione_fatturamagister_general_odt' 'riba' %}{{ cl.get_query_string }}">{% blocktrans %}Stampa RIBA{% endblocktrans %}</a></li>
  <li><a href="add/{% if is_popup %}?_popup=1{% endif %}" class="addlink">{% blocktrans with cl.opts.verbose_name|escape as name %}Add {{ name }}{% endblocktrans %}</a></li>
</ul>
{% endif %}
{% endblock %}
{% block content %}
<form id="print-setup" method="post" action="">{% csrf_token %}
    <table id="result_list" cellspacing="0">
        <tbody><tr valign="middle">
            <td class="nowrap" style="vertical-align: middle;"><b>Configurazione stampa:</b></td>
            <td class="nowrap">{{ print_setup_form.data.errors }}{{ print_setup_form.data }}</td>
            <td class="nowrap">{{ print_setup_form.banca.errors }}{{ print_setup_form.banca }}</td>
            <td class="nowrap">{{ print_setup_form.scaglione.errors }}{{ print_setup_form.scaglione }}</td>
            <td class="nowrap" style="vertical-align: middle;"><b>Stampa:</b></td>
            <td class="nowrap"><input type="submit" value="anticipi" name="anticipi" class="btn btn-info"></td>
            <td class="nowrap"><input type="submit" value="anticipate" name="anticipate" class="btn btn-info"></td>
            <td class="nowrap"><input type="submit" value="anticipabili" name="anticipabili" class="btn btn-info"></td>
        </tr></tbody>
    </table>
</form>
{{ block.super }}
{% endblock content %}
