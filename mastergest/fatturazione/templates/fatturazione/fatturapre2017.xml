<?xml version="1.0" encoding="UTF-8"?>
<p:FatturaElettronica xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:p="http://www.fatturapa.gov.it/sdi/fatturapa/v1.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" versione="1.1">
<FatturaElettronicaHeader>
    <DatiTrasmissione>
        <IdTrasmittente>
            <IdPaese>IT</IdPaese>
            <IdCodice>01932770355</IdCodice>
        </IdTrasmittente>
        <ProgressivoInvio>{{ fattura.anno|safe }}{{ fattura.get_progressivo_invio }}</ProgressivoInvio>
        <FormatoTrasmissione>SDI11</FormatoTrasmissione>
        <CodiceDestinatario>{{ fattura.cliente.codice_pa }}</CodiceDestinatario>
        <ContattiTrasmittente>
            <Telefono>05221590100</Telefono>
            <Email><EMAIL></Email>
        </ContattiTrasmittente>
    </DatiTrasmissione>
    <CedentePrestatore>
        <DatiAnagrafici>
            <IdFiscaleIVA>
                <IdPaese>IT</IdPaese>
                <IdCodice>01932770355</IdCodice>
            </IdFiscaleIVA>
            <CodiceFiscale>01932770355</CodiceFiscale>
            <Anagrafica>
                <Denominazione>Master Training s.r.l.</Denominazione>
            </Anagrafica>
            <RegimeFiscale>RF01</RegimeFiscale>
        </DatiAnagrafici>
        <Sede>
            <Indirizzo>Via Timolini n.18</Indirizzo>
            <CAP>42015</CAP>
            <Comune>Correggio</Comune>
            <Provincia>RE</Provincia>
            <Nazione>IT</Nazione>
        </Sede>
        <StabileOrganizzazione>
            <Indirizzo>Via San Martino n.11</Indirizzo>
            <CAP>42015</CAP>
            <Comune>Correggio</Comune>
            <Provincia>RE</Provincia>
            <Nazione>IT</Nazione>
        </StabileOrganizzazione>
        <IscrizioneREA>
            <Ufficio>RE</Ufficio>
            <NumeroREA>236176</NumeroREA>
            <StatoLiquidazione>LN</StatoLiquidazione>
        </IscrizioneREA>
        <Contatti>
            <Telefono>05221590100</Telefono>
            <Fax>0522331673</Fax>
            <Email><EMAIL></Email>
        </Contatti>
        <RiferimentoAmministrazione>{{ fattura.id|safe }}</RiferimentoAmministrazione>
    </CedentePrestatore>
    <CessionarioCommittente>
        <DatiAnagrafici>
            {% if fattura.partita_iva %}
            <IdFiscaleIVA>
                <IdPaese>IT</IdPaese>
                <IdCodice>{{ fattura.partita_iva }}</IdCodice>
            </IdFiscaleIVA>
            {% endif %}
            {% if fattura.codice_fiscale %}
            <CodiceFiscale>{{ fattura.codice_fiscale }}</CodiceFiscale>
            {% endif %}
            <Anagrafica>
                <Denominazione>{{ fattura.ragione_sociale|safe }}</Denominazione>
            </Anagrafica>
        </DatiAnagrafici>
        <Sede>
            <Indirizzo>{{ fattura.indirizzo|safe }}</Indirizzo>
            <CAP>{{ fattura.cap }}</CAP>
            <Comune>{{ fattura.citta|safe }}</Comune>
            <Provincia>{{ fattura.provincia }}</Provincia>
            <Nazione>IT</Nazione>
        </Sede>
    </CessionarioCommittente>
</FatturaElettronicaHeader>
<FatturaElettronicaBody>
    <DatiGenerali>
        <DatiGeneraliDocumento>
            {% if fattura.imponibile < 0 %}
            <TipoDocumento>TD04</TipoDocumento>
            {% else %}
            <TipoDocumento>TD01</TipoDocumento>
            {% endif %}
            <Divisa>EUR</Divisa>
            <Data>{{ fattura.data|date:"Y-m-d" }}</Data>
            <Numero>{{ fattura.numero|safe }}</Numero>
            {% if fattura.bollo_virtuale %}
            <DatiBollo>
                <BolloVirtuale>SI</BolloVirtuale>
                <ImportoBollo>{{ fattura.importo_bollo|safe }}</ImportoBollo>
            </DatiBollo>
            {% endif %}
        </DatiGeneraliDocumento>
        {% if fattura.cig or fattura.cup or fattura.numero_documento_ordine %}
        <DatiOrdineAcquisto>
            {% if fattura.numero_documento_ordine %}
            <IdDocumento>{{ fattura.numero_documento_ordine|safe }}</IdDocumento>
            {% else %}
            <IdDocumento>{{ fattura.numero|safe }}</IdDocumento>
            {% endif %}
            {% if fattura.cup %}
            <CodiceCUP>{{ fattura.cup|safe }}</CodiceCUP>
            {% endif %}
            {% if fattura.cig %}
            <CodiceCIG>{{ fattura.cig|safe }}</CodiceCIG>
            {% endif %}
        </DatiOrdineAcquisto>
        {% endif %}
    </DatiGenerali>
    <DatiBeniServizi>
        {% for riga_fattura in fattura.get_righe_fattura %}
        <DettaglioLinee>
            <NumeroLinea>{{ forloop.counter }}</NumeroLinea>
            {% if riga_fattura.seriale %}
            <CodiceArticolo>
                <CodiceTipo>Numero Seriale</CodiceTipo>
                <CodiceValore>{{ riga_fattura.seriale }}</CodiceValore>
            </CodiceArticolo>
            {% endif %}
            <Descrizione>{{ riga_fattura.get_descrizione_xml }}</Descrizione>
            {% if riga_fattura.contabile %}
                <Quantita>{{ riga_fattura.quantita|safe }}</Quantita>
                <PrezzoUnitario>{{ riga_fattura.costo_unitario|safe }}</PrezzoUnitario>
                <PrezzoTotale>{{ riga_fattura.get_totale_riga|safe }}</PrezzoTotale>
            {% else %}
                <Quantita>1.00</Quantita>
                <PrezzoUnitario>0.00</PrezzoUnitario>
                <PrezzoTotale>0.00</PrezzoTotale>
            {% endif %}
            {% if fattura.esenzione_iva %}
                <AliquotaIVA>0.00</AliquotaIVA>
                <Natura>N4</Natura>
            {% else %}
                {% if riga_fattura.percentuale_iva %}
                    <AliquotaIVA>{{ riga_fattura.percentuale_iva }}.00</AliquotaIVA>
                {% else %}
                    <AliquotaIVA>22.00</AliquotaIVA>
                {% endif %}
            {% endif %}
            {% for estensione in riga_fattura.get_estensione_descrizione_xml %}
            <AltriDatiGestionali>
                <TipoDato>Descriz.</TipoDato>
                <RiferimentoTesto>{{ estensione }}</RiferimentoTesto>
            </AltriDatiGestionali>
            {% endfor %}
        </DettaglioLinee>
        {% endfor %}
        <DatiRiepilogo>
            {% if fattura.esenzione_iva %}
                <AliquotaIVA>0.00</AliquotaIVA>
                <Natura>N4</Natura>
            {% else %}
                <AliquotaIVA>22.00</AliquotaIVA>
            {% endif %}
            <ImponibileImporto>{{ fattura.imponibile|safe }}</ImponibileImporto>
            <Imposta>{{ fattura.iva|safe }}</Imposta>
            {% if fattura.iva_esigibilita_differita %}
            <EsigibilitaIVA>D</EsigibilitaIVA>
            {% elif fattura.split_payment %}
            <EsigibilitaIVA>S</EsigibilitaIVA>
            <RiferimentoNormativo>Scissione dei pagamenti ai sensi dell'art.17 TER D.P.R. 633/72</RiferimentoNormativo>
            {% endif %}
        </DatiRiepilogo>
    </DatiBeniServizi>
    <DatiPagamento>
        <CondizioniPagamento>TP02</CondizioniPagamento>
        {% for scadenza_pagamento in fattura.get_scadenze_pagamenti %}
        <DettaglioPagamento>
            {% if scadenza_pagamento.tipo_pagamento.nome == 'RIBA' %}
                <ModalitaPagamento>MP07</ModalitaPagamento>
            {% elif scadenza_pagamento.tipo_pagamento.nome == 'RID' %}
                <ModalitaPagamento>MP09</ModalitaPagamento>
            {% elif scadenza_pagamento.tipo_pagamento.nome == 'BONIFICO' %}
                <ModalitaPagamento>MP05</ModalitaPagamento>
            {% elif scadenza_pagamento.tipo_pagamento.nome == 'RIMESSA DIRETTA' %}
                <ModalitaPagamento>MP01</ModalitaPagamento>
            {% endif %}
            <DataScadenzaPagamento>{{ scadenza_pagamento.data|date:"Y-m-d" }}</DataScadenzaPagamento>
            <ImportoPagamento>{{ scadenza_pagamento.importo|safe }}</ImportoPagamento>
            {% if fattura.banca_appoggio %}
            <IstitutoFinanziario>{{ fattura.banca_appoggio.nome|safe }}</IstitutoFinanziario>
            <IBAN>{{ fattura.banca_appoggio.iban }}</IBAN>
            {% endif %}
        </DettaglioPagamento>
        {% endfor %}
    </DatiPagamento>
</FatturaElettronicaBody>
</p:FatturaElettronica>
