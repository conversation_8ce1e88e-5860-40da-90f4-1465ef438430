<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<PDV>
    <pdvid>{{ codice_indice }}</pdvid>
    <docClass namespace="conservazione.doc">2__fatturaPA</docClass>
    <files>
        {% for fattura in elenco_fatture %}
        <file>
            <docid>{{ fattura.id|safe }}</docid>
            <filename>{{ fattura.nome_file_xml }}</filename>
            <mimetype>text/xml</mimetype>
            <closingDate>{{ fattura.data|safe }}</closingDate>
            <hash algorithm="SHA-256">
                <value>{{ fattura.stringa_hash }}</value>
            </hash>
            <metadata>
                <mandatory>
                    <singlemetadata>
                        <namespace>conservazione.doc</namespace>
                        <name>oggettodocumento</name>
                        <value>Fattura n. {{ fattura.numero }}</value>
                    </singlemetadata>
                    <singlemetadata>
                        <namespace>conservazione.doc</namespace>
                        <name>dataDocumentoTributario</name>
                        <value>{{ fattura.data|safe }}</value>
                    </singlemetadata>
                    <singlemetadata>
                        <namespace>conservazione.doc</namespace>
                        <name>dataDocumento</name>
                        <value>{{ fattura.data|safe }}</value>
                    </singlemetadata>
                    <complexmetadata namespace="conservazione.doc" name="destinatario" namespaceNode="conservazione.soggetti" nodeName="soggetto">
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>codicefiscale</name>
                            {% if fattura.codice_fiscale %}
                                <value>{{ fattura.codice_fiscale }}</value>
                            {% else %}
                                <value>{{ fattura.partita_iva }}</value>
                            {% endif %}
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>cognome</name>
                            <value></value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>partitaiva</name>
                            {% if fattura.partita_iva %}
                            <value>{{ fattura.partita_iva }}</value>
                            {% else %}
                            <value>{{ fattura.codice_fiscale }}</value>
                            {% endif %}
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>nome</name>
                            <value></value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>denominazione</name>
                            <value>{{ fattura.ragione_sociale }}</value>
                        </singlemetadata>
                    </complexmetadata>
                    <complexmetadata namespace="conservazione.doc" name="soggettoproduttore" namespaceNode="conservazione.soggetti" nodeName="soggetto">
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>codicefiscale</name>
                            <value>01932770355</value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>cognome</name>
                            <value></value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>partitaiva</name>
                            <value>01932770355</value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>nome</name>
                            <value></value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>denominazione</name>
                            <value>Master Training s.r.l.</value>
                        </singlemetadata>
                    </complexmetadata>
                    <complexmetadata namespace="conservazione.doc" name="soggettotributario" namespaceNode="conservazione.soggetti" nodeName="soggetto">
                         <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>codicefiscale</name>
                            <value>01932770355</value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>cognome</name>
                            <value></value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>partitaiva</name>
                            <value>01932770355</value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>nome</name>
                            <value></value>
                        </singlemetadata>
                        <singlemetadata>
                            <namespace>conservazione.soggetti</namespace>
                            <name>denominazione</name>
                            <value>Master Training s.r.l.</value>
                        </singlemetadata>
                    </complexmetadata>
                </mandatory>
            </metadata>
            <riferimento>
                <pdvidref>{{ fattura.id|safe }}</pdvidref>
                <docidref>{{ fattura.id|safe }}</docidref>
            </riferimento>
        </file>
        {% endfor %}
    </files>
</PDV>
