<?xml version="1.0" encoding="UTF-8"?>
<p:FatturaElettronica xmlns:p="http://www.fatturapa.gov.it/sdi/fatturapa/v1.0" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" versione="1.0">
<FatturaElettronicaHeader>
    <DatiTrasmissione>
        <IdTrasmittente>
            <IdPaese>IT</IdPaese>
            <IdCodice>01932770355</IdCodice>
        </IdTrasmittente>
        <ProgressivoInvio>{{ fattura.get_progressivo_invio }}</ProgressivoInvio>
        <FormatoTrasmissione>SDI10</FormatoTrasmissione>
        <CodiceDestinatario>{{ fattura.cliente.codice_pa }}</CodiceDestinatario>
        <ContattiTrasmittente>
            <Telefono>05221590100</Telefono>
            <Email><EMAIL></Email>
        </ContattiTrasmittente>
    </DatiTrasmissione>
    <CedentePrestatore>
        <DatiAnagrafici>
            <IdFiscaleIVA>
                <IdPaese>IT</IdPaese>
                <IdCodice>01932770355</IdCodice>
            </IdFiscaleIVA>
            <Anagrafica>
                <Denominazione>Master Training s.r.l.</Denominazione>
            </Anagrafica>
            <RegimeFiscale>RF01</RegimeFiscale>
        </DatiAnagrafici>
        <Sede>
            <Indirizzo>Via Timolini n.18</Indirizzo>
            <CAP>42015</CAP>
            <Comune>Correggio</Comune>
            <Provincia>RE</Provincia>
            <Nazione>IT</Nazione>
        </Sede>
        <StabileOrganizzazione>
            <Indirizzo>Via Sani n.15</Indirizzo>
            <CAP>42100</CAP>
            <Comune>Reggio Emilia</Comune>
            <Provincia>RE</Provincia>
            <Nazione>IT</Nazione>
        </StabileOrganizzazione>
        <IscrizioneREA>
            <Ufficio>RE</Ufficio>
            <NumeroREA>236176</NumeroREA>
            <StatoLiquidazione>LN</StatoLiquidazione>
        </IscrizioneREA>
        <Contatti>
            <Telefono>05221590100</Telefono>
            <Fax>0522331673</Fax>
            <Email><EMAIL></Email>
        </Contatti>
        <RiferimentoAmministrazione>{{ fattura.id|safe }}</RiferimentoAmministrazione>
    </CedentePrestatore>
    <CessionarioCommittente>
        <DatiAnagrafici>
            {% if fattura.partita_iva %}
            <IdFiscaleIVA>
                <IdPaese>IT</IdPaese>
                <IdCodice>{{ fattura.partita_iva }}</IdCodice>
            </IdFiscaleIVA>
            {% endif %}
            <CodiceFiscale>{{ fattura.codice_fiscale }}</CodiceFiscale>
            <Anagrafica>
                <Denominazione>{{ fattura.ragione_sociale }}</Denominazione>
            </Anagrafica>
        </DatiAnagrafici>
        <Sede>
            <Indirizzo>{{ fattura.indirizzo }}</Indirizzo>
            <CAP>{{ fattura.cap }}</CAP>
            <Comune>{{ fattura.citta }}</Comune>
            <Provincia>{{ fattura.provincia }}</Provincia>
            <Nazione>IT</Nazione>
        </Sede>
    </CessionarioCommittente>
</FatturaElettronicaHeader>
<FatturaElettronicaBody>
    <DatiGenerali>
        <DatiGeneraliDocumento>
            {% if fattura.imponibile < 0 %}
            <TipoDocumento>TD01</TipoDocumento>
            {% else %}
            <TipoDocumento>TD04</TipoDocumento>
            {% endif %}
            <Divisa>EUR</Divisa>
            <Data>{{ fattura.data|date:"Y-m-d" }}</Data>
            <Numero>{{ fattura.numero|safe }}</Numero>
        </DatiGeneraliDocumento>
        {% if fattura.cig %}
        <DatiOrdineAcquisto>
            <RiferimentoNumeroLinea>1</RiferimentoNumeroLinea>
            <IdDocumento>123</IdDocumento>
            <CodiceCUP>123abc</CodiceCUP>
            <CodiceCIG>456def</CodiceCIG>
        </DatiOrdineAcquisto>
        <DatiContratto>
            <RiferimentoNumeroLinea>1</RiferimentoNumeroLinea>
            <IdDocumento>123</IdDocumento>
            <Data>2012-09-01</Data>
            <NumItem>5</NumItem>
            <CodiceCUP>123abc</CodiceCUP>
            <CodiceCIG>456def</CodiceCIG>
        </DatiContratto>
        <DatiConvenzione>
            <RiferimentoNumeroLinea>1</RiferimentoNumeroLinea>
            <IdDocumento>123</IdDocumento>
            <Data>2012-09-01</Data>
            <NumItem>5</NumItem>
            <CodiceCUP>123abc</CodiceCUP>
            <CodiceCIG>456def</CodiceCIG>
        </DatiConvenzione>
        <DatiRicezione>
            <RiferimentoNumeroLinea>1</RiferimentoNumeroLinea>
            <IdDocumento>123</IdDocumento>
            <Data>2012-09-01</Data>
            <NumItem>5</NumItem>
            <CodiceCUP>123abc</CodiceCUP>
            <CodiceCIG>456def</CodiceCIG>
        </DatiRicezione>
        {% endif %}
    </DatiGenerali>
    <DatiBeniServizi>
        {% for riga_fattura in fattura.get_righe_fattura_raggruppate %}
        <DettaglioLinee>
            <NumeroLinea>{{ forloop.counter }}</NumeroLinea>
            <Descrizione>{{ riga_fattura.descrizione_xml }}</Descrizione>
            <Quantita>{{ riga_fattura.quantita|safe }}.00</Quantita>
            <PrezzoUnitario>{{ riga_fattura.costo_unitario }}</PrezzoUnitario>
            <PrezzoTotale>{{ riga_fattura.totale_pulito }}</PrezzoTotale>
            {% if riga_fattura.percentuale_iva %}
            <AliquotaIVA>{{ riga_fattura.percentuale_iva }}.00</AliquotaIVA>
            {% else %}
            <AliquotaIVA>22.00</AliquotaIVA>
            {% endif %}
        </DettaglioLinee>
        {% endfor %}
        <DatiRiepilogo>
            <AliquotaIVA>22.00</AliquotaIVA>
            <ImponibileImporto>{{ fattura.imponibile|safe }}</ImponibileImporto>
            <Imposta>{{ fattura.iva|safe }}</Imposta>
            {% if fattura.iva_esigibilita_differita %}
            <EsigibilitaIVA>D</EsigibilitaIVA>
            {% endif %}
        </DatiRiepilogo>
    </DatiBeniServizi>
    <DatiPagamento>
        <CondizioniPagamento>TP01</CondizioniPagamento>
        {% for scadenza_pagamento in fattura.get_scadenze_pagamenti %}
        <DettaglioPagamento>
            {% if scadenza_pagamento.tipo_pagamento.codice == 'RIBA' %}
                <ModalitaPagamento>MP07</ModalitaPagamento>
            {% elif scadenza_pagamento.tipo_pagamento.codice == 'RID' %}
                <ModalitaPagamento>MP09</ModalitaPagamento>
            {% elif scadenza_pagamento.tipo_pagamento.codice == 'BONIFICO' %}
                <ModalitaPagamento>MP05</ModalitaPagamento>
            {% elif scadenza_pagamento.tipo_pagamento.codice == 'RIMESSA DIRETTA' %}
                <ModalitaPagamento>MP01</ModalitaPagamento>
            {% endif %}
            <DataScadenzaPagamento>{{ scadenza_pagamento.data|date:"Y-m-d" }}</DataScadenzaPagamento>
            <ImportoPagamento>{{ scadenza_pagamento.importo|safe }}</ImportoPagamento>
            {% if fattura.banca_appoggio %}
            <IstitutoFinanziario>{{ fattura.banca_appoggio.nome }}</IstitutoFinanziario>
            <IBAN>{{ fattura.banca_appoggio.iban }}</IBAN>
            {% endif %}
        </DettaglioPagamento>
        {% endfor %}
    </DatiPagamento>
</FatturaElettronicaBody>
</p:FatturaElettronica>
