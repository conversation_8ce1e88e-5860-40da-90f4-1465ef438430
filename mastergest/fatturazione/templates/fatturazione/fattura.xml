<?xml version="1.0" encoding="UTF-8"?>
{% if fattura.pubblica_amministrazione %}
<p:FatturaElettronica versione="FPA12" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2 fatturaordinaria_v1.2.xsd ">
{% else %}
<p:FatturaElettronica versione="FPR12" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2 fatturaordinaria_v1.2.xsd ">
{% endif %}
<FatturaElettronicaHeader>
    <DatiTrasmissione>
        <IdTrasmittente>
            <IdPaese>IT</IdPaese>
            <IdCodice>01932770355</IdCodice>
        </IdTrasmittente>
        {% if fattura.postfisso %}
            <ProgressivoInvio>{{ fattura.anno|safe }}{{ fattura.numero|safe }}{{ fattura.postfisso|safe }}</ProgressivoInvio>
        {% else %}
            <ProgressivoInvio>{{ fattura.anno|safe }}{{ fattura.numero|safe }}</ProgressivoInvio>
        {% endif %}
        {% if fattura.pubblica_amministrazione %}
            <FormatoTrasmissione>FPA12</FormatoTrasmissione>
        {% else %}
            <FormatoTrasmissione>FPR12</FormatoTrasmissione>
        {% endif %}
        <CodiceDestinatario>{{ fattura.get_codice_pa }}</CodiceDestinatario>
        <ContattiTrasmittente>
            <Telefono>05221590100</Telefono>
            <Email><EMAIL></Email>
        </ContattiTrasmittente>
        {% if fattura.pec_destinatario %}
        <PECDestinatario>{{ fattura.pec_destinatario }}</PECDestinatario>
        {% endif %}
    </DatiTrasmissione>
    <CedentePrestatore>
        <DatiAnagrafici>
            <IdFiscaleIVA>
                <IdPaese>IT</IdPaese>
                <IdCodice>{{ fattura.gestione_azienda_partita_iva }}</IdCodice>
            </IdFiscaleIVA>
            <CodiceFiscale>{{ fattura.gestione_azienda_partita_iva }}</CodiceFiscale>
            <Anagrafica>
                <Denominazione>{{ fattura.gestione_azienda_ragione_sociale }}</Denominazione>
            </Anagrafica>
            <RegimeFiscale>RF01</RegimeFiscale>
        </DatiAnagrafici>
        <Sede>
            <Indirizzo>{{ fattura.gestione_azienda_indirizzo_sede_legale }}</Indirizzo>
            <CAP>{{ fattura.gestione_azienda_cap_sede_legale }}</CAP>
            <Comune>{{ fattura.gestione_azienda_citta_sede_legale }}</Comune>
            <Provincia>RE</Provincia>
            <Nazione>IT</Nazione>
        </Sede>
        <StabileOrganizzazione>
            <Indirizzo>Via San Martino n.11</Indirizzo>
            <CAP>42015</CAP>
            <Comune>Correggio</Comune>
            <Provincia>RE</Provincia>
            <Nazione>IT</Nazione>
        </StabileOrganizzazione>
        <IscrizioneREA>
            <Ufficio>RE</Ufficio>
            <NumeroREA>{{ fattura.gestione_azienda_numero_iscrizione_rea }}</NumeroREA>
            <StatoLiquidazione>LN</StatoLiquidazione>
        </IscrizioneREA>
        <Contatti>
            <Telefono>{{ fattura.gestione_azienda_telefono }}</Telefono>
            <Fax>0522331673</Fax>
            <Email>{{ fattura.gestione_azienda_email_amministrazione }}</Email>
        </Contatti>
        {% if fattura.riferimento_amministrazione %}
        <RiferimentoAmministrazione>{{ fattura.riferimento_amministrazione }}</RiferimentoAmministrazione>
        {% else %}
        <RiferimentoAmministrazione>{{ fattura.id|safe }}</RiferimentoAmministrazione>
        {% endif %}
    </CedentePrestatore>
    <CessionarioCommittente>
        <DatiAnagrafici>
            {% if fattura.get_partita_iva_xml %}
            <IdFiscaleIVA>
                <IdPaese>{{ fattura.stato.codice }}</IdPaese>
                <IdCodice>{{ fattura.get_partita_iva_xml }}</IdCodice>
            </IdFiscaleIVA>
            {% endif %}
            {% if fattura.get_codice_fiscale_xml %}
            <CodiceFiscale>{{ fattura.get_codice_fiscale_xml }}</CodiceFiscale>
            {% endif %}
            <Anagrafica>
                <Denominazione>{{ fattura.get_ragione_sociale_xml }}</Denominazione>
            </Anagrafica>
        </DatiAnagrafici>
        <Sede>
            <Indirizzo>{{ fattura.get_indirizzo_xml }}</Indirizzo>
            <CAP>{{ fattura.get_cap_xml }}</CAP>
            <Comune>{{ fattura.get_citta_xml }}</Comune>
            {% if fattura.get_provincia_xml %}
            <Provincia>{{ fattura.get_provincia_xml }}</Provincia>
            {% endif %}
            <Nazione>{{ fattura.stato.codice }}</Nazione>
        </Sede>
    </CessionarioCommittente>
</FatturaElettronicaHeader>
<FatturaElettronicaBody>
    <DatiGenerali>
        <DatiGeneraliDocumento>
            {% if fattura.imponibile < 0 %}
            <TipoDocumento>TD04</TipoDocumento>
            {% else %}
            <TipoDocumento>TD01</TipoDocumento>
            {% endif %}
            <Divisa>EUR</Divisa>
            <Data>{{ fattura.data|date:"Y-m-d" }}</Data>
            <Numero>{{ fattura.get_numero|safe }}</Numero>
            {% if fattura.bollo_virtuale %}
            <DatiBollo>
                <BolloVirtuale>SI</BolloVirtuale>
                <ImportoBollo>{{ fattura.importo_bollo|safe }}</ImportoBollo>
            </DatiBollo>
            {% endif %}
            <ImportoTotaleDocumento>{{ fattura.get_totale_xml|safe }}</ImportoTotaleDocumento>
        </DatiGeneraliDocumento>
        {% if fattura.cig or fattura.cup or fattura.numero_documento_ordine %}
        <DatiOrdineAcquisto>
            {% if fattura.numero_documento_ordine %}
            <IdDocumento>{{ fattura.numero_documento_ordine|safe }}</IdDocumento>
            {% else %}
            <IdDocumento>{{ fattura.numero|safe }}</IdDocumento>
            {% endif %}
            {% if fattura.cup %}
            <CodiceCUP>{{ fattura.cup|safe }}</CodiceCUP>
            {% endif %}
            {% if fattura.cig %}
            <CodiceCIG>{{ fattura.cig|safe }}</CodiceCIG>
            {% endif %}
        </DatiOrdineAcquisto>
        {% endif %}
    </DatiGenerali>
    <DatiBeniServizi>
        {% for riga_fattura in fattura.get_righe_fattura %}
        <DettaglioLinee>
            <NumeroLinea>{{ forloop.counter }}</NumeroLinea>
            {% if riga_fattura.seriale %}
            <CodiceArticolo>
                <CodiceTipo>Numero Seriale</CodiceTipo>
                <CodiceValore>{{ riga_fattura.seriale }}</CodiceValore>
            </CodiceArticolo>
            {% endif %}
            <Descrizione>{{ riga_fattura.get_descrizione_xml }}</Descrizione>
            {% if riga_fattura.contabile %}
                <Quantita>{{ riga_fattura.quantita|safe }}</Quantita>
                <PrezzoUnitario>{{ riga_fattura.get_costo_unitario_xml|safe }}</PrezzoUnitario>
                <PrezzoTotale>{{ riga_fattura.get_totale_riga_xml|safe }}</PrezzoTotale>
            {% else %}
                <Quantita>1.00</Quantita>
                <PrezzoUnitario>0.00</PrezzoUnitario>
                <PrezzoTotale>0.00</PrezzoTotale>
            {% endif %}
            {% if fattura.esenzione_iva %}
                <AliquotaIVA>0.00</AliquotaIVA>
                <Natura>{{ fattura.esenzione_iva.natura_esenzione_sdi }}</Natura>
                {% if fattura.esenzione_iva.dichiarazione_intento %}
                <AltriDatiGestionali>
                    <TipoDato>INTENTO</TipoDato>
                    <RiferimentoTesto>{{ fattura.esenzione_iva.numero_protocollo|safe }}</RiferimentoTesto>
                    <RiferimentoData>{{ fattura.esenzione_iva.data_protocollo|date:"Y-m-d" }}</RiferimentoData>
                </AltriDatiGestionali>
                {% endif %}
            {% else %}
                {% if riga_fattura.percentuale_iva %}
                    <AliquotaIVA>{{ riga_fattura.percentuale_iva }}.00</AliquotaIVA>
                {% else %}
                    <AliquotaIVA>22.00</AliquotaIVA>
                {% endif %}
            {% endif %}
        </DettaglioLinee>
        {% endfor %}
        <DatiRiepilogo>
            {% if fattura.esenzione_iva %}
                <AliquotaIVA>0.00</AliquotaIVA>
                <Natura>{{ fattura.esenzione_iva.natura_esenzione_sdi }}</Natura>
            {% else %}
                <AliquotaIVA>{{ fattura.get_aliquota_iva }}</AliquotaIVA>
            {% endif %}
            <ImponibileImporto>{{ fattura.get_imponibile_xml|safe }}</ImponibileImporto>
            <Imposta>{{ fattura.get_iva_xml|safe }}</Imposta>
            {% if fattura.iva_esigibilita_differita %}
            <EsigibilitaIVA>D</EsigibilitaIVA>
            {% elif fattura.split_payment %}
            <EsigibilitaIVA>S</EsigibilitaIVA>
            <RiferimentoNormativo>Scissione dei pagamenti ai sensi dell'art.17 TER D.P.R. 633/72</RiferimentoNormativo>
            {% elif fattura.esenzione_iva %}
             <RiferimentoNormativo>{{ fattura.esenzione_iva.get_riferimento_esenzione_xml }}</RiferimentoNormativo>
            {% endif %}
        </DatiRiepilogo>
    </DatiBeniServizi>
    <DatiPagamento>
        <CondizioniPagamento>TP02</CondizioniPagamento>
        {% for scadenza_pagamento in fattura.get_scadenze_pagamenti %}
        <DettaglioPagamento>
            <ModalitaPagamento>{{ scadenza_pagamento.tipo_pagamento.codice_pagamento_sdi }}</ModalitaPagamento>
            <DataScadenzaPagamento>{{ scadenza_pagamento.data|date:"Y-m-d" }}</DataScadenzaPagamento>
            <ImportoPagamento>{{ scadenza_pagamento.get_importo_xml|safe }}</ImportoPagamento>
            {% if fattura.banca_appoggio %}
            <IstitutoFinanziario>{{ fattura.banca_appoggio.nome|safe }}</IstitutoFinanziario>
            <IBAN>{{ fattura.banca_appoggio.iban }}</IBAN>
            {% endif %}
        </DettaglioPagamento>
        {% endfor %}
    </DatiPagamento>
    <Allegati>
        <NomeAttachment>{{ nome_allegato }}</NomeAttachment>
        <DescrizioneAttachment>Fattura PDF allegata</DescrizioneAttachment>
        <Attachment>{{ stringa_allegato }}</Attachment>
    </Allegati>
</FatturaElettronicaBody>
</p:FatturaElettronica>
