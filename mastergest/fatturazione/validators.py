from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db.models import Sum


def clean_scadenzapagamento_importo(importo, fattura, scadenza_pk):
    if not importo:
        return importo
    scadenze = fattura.scadenzapagamento_set.all()
    if scadenza_pk:
        scadenze = scadenze.exclude(pk=scadenza_pk)
    totale = scadenze.aggregate(Sum('importo'))['importo__sum'] or Decimal('0.00')
    totale += importo
    if fattura.split_payment:
        totale_fattura = fattura.imponibile.quantize(Decimal('0.00'))
    else:
        totale_fattura = fattura.totale.quantize(Decimal('0.00'))
    if totale > totale_fattura:
        msg = "L'importo totale delle scadenze pagamenti (%s) supera il " \
            "totale fattura (%s)." % (totale, totale_fattura)
        raise ValidationError(msg)
    return importo


def clean_scadenzapagamento_storno(storno, importo, scadenza):
    if not storno or not importo:
        return storno
    if storno > importo:
        raise ValidationError("Lo storno non puo' superare l'importo.")
    # Gli abbinamenti esistenti non possono superare importo - storno
    if scadenza:
        abbinamenti = scadenza.abbinamento_set.all()
        totale_abbinato = abbinamenti.aggregate(Sum('importo'))['importo__sum'] or Decimal('0.00')
        if totale_abbinato > importo - storno:
            msg = "L'importo incassato supera l'importo decuratato dello storno."
            raise ValidationError(msg)
    return storno


def clean_rigafattura_contabile(contabile, quantita, costo_unitario, percentuale_iva):
    costo_ok = bool(costo_unitario)
    if costo_unitario == Decimal('0.00'):
        costo_ok = True
    iva_ok = bool(percentuale_iva)
    if percentuale_iva == 0:
        iva_ok = True
    if contabile:
        if not quantita or not costo_ok or not iva_ok:
            msg = "Inserire quantita', costo unitario e percentuale iva"
            raise ValidationError(msg)
    else:
        if costo_ok or iva_ok:
            msg = "Costo unitario e percentuale iva non sono ammessi nelle righe non contabili"
            raise ValidationError(msg)
    return contabile
