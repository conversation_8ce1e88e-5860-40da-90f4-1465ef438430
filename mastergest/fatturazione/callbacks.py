from mastergest.fatturazione.models import Fattura
from mastergest.fatturazione.tasks import aggiorna_fatturato_cliente_task


def aggiorna_totali_fattura(sender, **kwargs):
    try:
        fattura = kwargs['instance'].fattura
        fattura.save()
    except Fattura.DoesNotExist:
        pass


def aggiorna_pagata_fattura(sender, **kwargs):
    try:
        fattura = kwargs['instance'].fattura
        fattura.save()
    except Fattura.DoesNotExist:
        pass


def aggiorna_fatturato_cliente_callback(sender, **kwargs):
    try:
        fattura = kwargs['instance']
        if fattura.cliente:
            aggiorna_fatturato_cliente_task.apply_async(queue='fast', args=(fattura.cliente.id, ))
    except Fattura.DoesNotExist:
        pass


def aggiorna_stato_invio_fattura(sender, **kwargs):
    trasmissione_fattura = kwargs['instance']
    if trasmissione_fattura.fattura:
        trasmissione_fattura.fattura.aggiorna_stato_invio_sdi()
