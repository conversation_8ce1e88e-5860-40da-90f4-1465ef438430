from celery import shared_task

from mastergest.fatturazione.models import Fattura
from mastergest.anagrafe.models import Anagrafica
from mastergest.fatturazione.utils import send_fattura


@shared_task()
def invia_fattura_email_task(id_fattura):
    if id_fattura:
        try:
            fattura = Fattura.objects.get(id=id_fattura)
            send_fattura(fattura)
            fattura.inviata = True
            fattura.save()
        except Fattura.DoesNotExist:
            return 'ERRORE La fattura con id: %s non esiste!' % id_fattura
    return False


@shared_task()
def aggiorna_fatturato_cliente_task(id_cliente):
    if id_cliente:
        try:
            anagrafica = Anagrafica.objects.get(id=id_cliente)
            anagrafica.aggiorna_fatturato_recente()
        except Anagrafica.DoesNotExist:
            return 'ERRORE Il cliente con id: %s non esiste!' % id_cliente
    return False
