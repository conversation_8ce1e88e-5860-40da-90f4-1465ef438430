import datetime
from django import forms

from mastergest.anagrafe.widgets import AnagraficaWidget
from mastergest.anagrafe.models import Sede
from mastergest.fatturazione import models
from mastergest.fatturazione import validators
from mastergest.fatturazione.widgets import PianoContiRicaviWidget

from suit.widgets import SuitDateWidget
from suit.widgets import NumberInput
from suit.widgets import EnclosedInput

SIMBOLO_EURO = '\u20ac'


class FatturaForm(forms.ModelForm):
    get_link = forms.CharField(required=False)

    class Meta:
        model = models.Fattura
        fields = '__all__'
        widgets = dict(
            data=SuitDateWidget,
            data_anticipo=SuitDateWidget,
            numero=NumberInput(attrs={'class': 'input-mini'}),
            cliente=AnagraficaWidget(),
            ragione_sociale=forms.TextInput(attrs={'class': 'input-xlarge'}),
            provincia=forms.TextInput(attrs={'class': 'input-mini'}),
            cap=forms.TextInput(attrs={'class': 'input-mini'}),
            cig=forms.TextInput(attrs={'class': 'input-mini'}),
            cup=forms.TextInput(attrs={'class': 'input-mini'}),
            numero_documento_ordine=forms.TextInput(attrs={'class': 'input-mini'}),
            abi=forms.TextInput(attrs={'class': 'input-mini'}),
            cab=forms.TextInput(attrs={'class': 'input-mini'}),
            abi_rid=forms.TextInput(attrs={'class': 'input-mini'}),
            cab_rid=forms.TextInput(attrs={'class': 'input-mini'}),
            postfisso=forms.TextInput(attrs={'class': 'input-mini'}),
            destinazione_documento=forms.Textarea(attrs=dict(rows=3)),
            note=forms.Textarea(attrs={'rows': 5, 'class': 'input-xlarge'}),
            note_pagamento=forms.Textarea(attrs={'rows': 5, 'class': 'input-xlarge'}),
            note_fattura=forms.Textarea(attrs={'rows': 5, 'class': 'input-xlarge'}),
        )


class EsenzioneIvaForm(forms.ModelForm):

    class Meta:
        model = models.EsenzioneIva
        fields = '__all__'
        widgets = dict(
            nome=forms.TextInput(attrs={'class': 'input-xlarge'}),
            descrizione=forms.TextInput(attrs={'class': 'input-xxlarge'}),
            codice_gamma=forms.TextInput(attrs={'class': 'input-mini'}),
        )


class RigaFatturaForm(forms.ModelForm):
    get_centro_costo_display = forms.CharField(required=False)

    class Meta:
        model = models.RigaFattura
        fields = '__all__'
        widgets = dict(
            seriale=forms.TextInput(attrs={'class': 'input-small'}),
            descrizione=forms.Textarea(attrs={'style': 'width:350px', 'rows': 2}),
            quantita=forms.TextInput(attrs={'class': 'input-mini', 'style': 'text-align:center;'}),
            costo_unitario=EnclosedInput(
                append=SIMBOLO_EURO,
                attrs={'class': 'input-mini', 'style': 'text-align:right;'}
            ),
            piano_conti_gamma=PianoContiRicaviWidget(),
            percentuale_iva=EnclosedInput(append='%', attrs={'class': 'input-mini', 'style': 'text-align:center;'}),
        )

    def clean_contabile(self):
        if 'quantita' in self.cleaned_data:
            return validators.clean_rigafattura_contabile(
                self.cleaned_data['contabile'],
                self.cleaned_data['quantita'],
                self.cleaned_data['costo_unitario'],
                self.cleaned_data['percentuale_iva']
            )

    def __init__(self, *args, **kwargs):
        super(RigaFatturaForm, self).__init__(*args, **kwargs)
        if self.instance:
            if self.instance.id:
                cliente = self.instance.fattura.cliente
                self.fields['sede_cliente'] = forms.ModelChoiceField(
                    queryset=Sede.objects.filter(anagrafica=cliente),
                    required=False
                )


class ScadenzaPagamentoForm(forms.ModelForm):
    class Meta:
        model = models.ScadenzaPagamento
        fields = '__all__'
        widgets = dict(
            importo=EnclosedInput(append=SIMBOLO_EURO, attrs={'class': 'input-mini', 'style': 'text-align:right;'}),
            storno=EnclosedInput(append=SIMBOLO_EURO, attrs={'class': 'input-mini', 'style': 'text-align:right;'}),
            data=SuitDateWidget,
        )

    def clean_importo(self):
        return validators.clean_scadenzapagamento_importo(
            self.cleaned_data['importo'], self.cleaned_data['fattura'],
            self.instance.pk)

    def clean_storno(self):
        return validators.clean_scadenzapagamento_storno(
            self.cleaned_data['storno'], self.cleaned_data.get('importo'),
            self.instance)


class PrintSetupForm(forms.Form):
    data = forms.DateField(initial=datetime.date.today, widget=SuitDateWidget(), required=False)
    banca = forms.ModelChoiceField(queryset=models.BancaAppoggio.objects.all(), empty_label="(Seleziona Banca)", required=False)
    scaglione = forms.ModelChoiceField(queryset=models.CodiceScaglionePagamento.objects.all(), empty_label="(Scaglione)", required=False)


class TrasmettiFattureForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    alias = forms.CharField()
    pin = forms.CharField(widget=forms.PasswordInput())
