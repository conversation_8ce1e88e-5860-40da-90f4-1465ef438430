import re
import io
import xlwt
import base64
from decimal import Decimal
import hashlib

from django.http import HttpResponse
from django.template import Context, loader
from django.contrib.contenttypes.models import ContentType
from django.conf import settings

from webodt import ODFTemplate

from mastergest.fatturazione.reports import FatturaPdf
from mastergest.utils.mail import render_email_message
from mastergest.attachments.models import Attachment
from mastergest.utils.tasks import invia_email_task

MAIL_SENDER_MASTERTRAINING = '<EMAIL>'
MAIL_SENDER_MAGISTER = '<EMAIL>'
MAIL_SENDER_MASTERCOM = '<EMAIL>'
SIMBOLO_EURO = '\u20ac'
DECIMAL_ZERO = Decimal('0.00')


def aggiungi_scaglioni_pagamento_fattura(fattura):
    from mastergest.fatturazione.models import ScadenzaPagamento
    if fattura:
        if fattura.tipo_pagamento_gamma and not fattura.get_scadenze_pagamenti() and not fattura.totale == DECIMAL_ZERO:
            scaglioni_default = fattura.tipo_pagamento_gamma.scaglionepagamentotemplate_set.all()
            totale_scadenze = DECIMAL_ZERO
            totale_fattura = fattura.totale
            if fattura.split_payment:
                totale_fattura = fattura.imponibile
            if scaglioni_default:
                for scaglione_template in scaglioni_default:
                    importo_parziale = totale_fattura * (scaglione_template.percentuale_importo / Decimal('100.00'))
                    importo_parziale = importo_parziale.quantize(DECIMAL_ZERO)
                    totale_scadenze += importo_parziale
                    if totale_scadenze > totale_fattura:
                        importo_parziale = importo_parziale + (totale_fattura - totale_scadenze)
                    nuova_scadenza = ScadenzaPagamento.objects.create(
                        fattura=fattura,
                        tipo_pagamento=scaglione_template.tipo_pagamento,
                        codice_scaglione_pagamento=scaglione_template.codice_scaglione_pagamento,
                        importo=importo_parziale
                    )
                if totale_scadenze < totale_fattura:
                    nuova_scadenza.importo = nuova_scadenza.importo + (totale_fattura - totale_scadenze)
                    nuova_scadenza.save()


def genera_anticipi_fatture(fatture, scaglione):
    pdf = FatturaPdf()
    return pdf.genera_pdf_massivo(fatture, scaglione)


def crea_riepilogo_centri_costo(fattura):
    book = xlwt.Workbook(encoding='utf8')
    foglio = book.add_sheet('riepilogo CDC')
    foglio.col(0).width = 8000
    foglio.col(1).width = 6000
    # ------------ Bordo marcato
    borders = xlwt.Borders()  # Create Borders
    borders.left = xlwt.Borders.THIN
    borders.right = xlwt.Borders.THIN
    borders.top = xlwt.Borders.THIN
    borders.bottom = xlwt.Borders.THIN
    borders.left_colour = 0x40
    borders.right_colour = 0x40
    borders.top_colour = 0x40
    borders.bottom_colour = 0x40
    # ------------- Background Grigio
    grey_pattern = xlwt.Pattern()  # Create the Pattern
    grey_pattern.pattern = xlwt.Pattern.SOLID_PATTERN  # May be: NO_PATTERN, SOLID_PATTERN, or 0x00 through 0x12
    grey_pattern.pattern_fore_colour = xlwt.Style.colour_map['gray25']
    # ------------- Background Giallo
    yellow_pattern = xlwt.Pattern()  # Create the Pattern
    yellow_pattern.pattern = xlwt.Pattern.SOLID_PATTERN
    yellow_pattern.pattern_fore_colour = xlwt.Style.colour_map['light_yellow']
    # ALLINEAMENTO DESTRA
    aligned_right = xlwt.Alignment()
    aligned_right.horz = xlwt.Alignment.HORZ_RIGHT
    aligned_right.vert = xlwt.Alignment.VERT_CENTER
    # ALLINEAMENTO CENTRO
    aligned_center = xlwt.Alignment()
    aligned_center.horz = xlwt.Alignment.HORZ_CENTER
    aligned_center.vert = xlwt.Alignment.VERT_CENTER
    # ------------- Stile per Valuta
    # GIALLO
    valuta_style_giallo = xlwt.XFStyle()
    valuta_style_giallo.alignment = aligned_right
    valuta_style_giallo.borders = borders
    valuta_style_giallo.pattern = yellow_pattern
    valuta_style_giallo.num_format_str = '[$%s-410] #,##0.00;[RED]-[$%s-410] #,##0.00' % (SIMBOLO_EURO, SIMBOLO_EURO)
    # GRIGIO
    valuta_style_grigio = xlwt.XFStyle()
    valuta_style_grigio.alignment = aligned_right
    valuta_style_grigio.borders = borders
    valuta_style_grigio.pattern = grey_pattern
    valuta_style_grigio.num_format_str = '[$%s-410] #,##0.00;[RED]-[$%s-410] #,##0.00' % (SIMBOLO_EURO, SIMBOLO_EURO)
    # STILE DATI TESTATA
    testata_style = xlwt.XFStyle()
    testata_style.borders = borders
    testata_style.pattern = grey_pattern
    testata_style.alignment = aligned_center
    testata_style.font.bold = True
    # STILE DATI PROGETTO
    dati_style = xlwt.XFStyle()
    dati_style.borders = borders
    dati_style.alignment = aligned_center
    dati_style.pattern = yellow_pattern
    # DATI GENERALI
    foglio.write(0, 0, 'FATTURA N.', testata_style)
    foglio.write(0, 1, fattura.get_numero(), testata_style)
    foglio.write(1, 0, 'DATA:', testata_style)
    foglio.write(1, 1, '%s/%s/%s' % (fattura.data.day, fattura.data.month, fattura.data.year), testata_style)
    foglio.write(3, 0, 'CENTRO DI COSTO', testata_style)
    foglio.write(3, 1, 'Imponibile (%s)' % SIMBOLO_EURO, testata_style)
    indice_riga = 4
    elenco_centri_costo = fattura.get_elenco_centri_costo()
    if elenco_centri_costo:
        for centro in list(elenco_centri_costo.keys()):
            foglio.write(indice_riga, 0, centro, dati_style)
            foglio.write(indice_riga, 1, elenco_centri_costo[centro], valuta_style_giallo)
            indice_riga += 1
    foglio.write(indice_riga, 0, 'TOT. IMPONIBILE (%s)' % SIMBOLO_EURO, testata_style)
    foglio.write(indice_riga, 1, fattura.imponibile, valuta_style_grigio)
    return book


def genera_stampa_fatture(fatture, tipo_stampa):
    """
        <tipo_stampa>: ["anticipabili"|"anticipate"]
    """
    totale = Decimal('0.00')
    totale_imponibile = Decimal('0.00')
    for fatt in fatture:
        # print fatt.id, fatt, fatt.totale, fatt.imponibile
        totale += fatt.totale
        totale_imponibile += fatt.imponibile
    filename = 'fatture_%s.odt' % tipo_stampa
    template = ODFTemplate('fatture/%s' % filename)
    response = HttpResponse(
        template.render(
            Context(
                dict(
                    fatture=fatture, totale=totale, tot_imp=totale_imponibile
                )
            )
        ),
        content_type='application/vnd.oasis.opendocument.text'
    )
    response['Content-Disposition'] = 'attachment; filename=%s' % filename
    return response


def format_riga_fattura_quantita(quantita):
    quantita_stringa = ''
    if quantita:
        quantita_stringa = '%s' % quantita
        quantita_stringa = quantita_stringa.replace('.', ',')
        if quantita_stringa.endswith(',00') or quantita_stringa.endswith(',0'):
            elenco_quantita = quantita_stringa.split(',')
            quantita_stringa = '%s' % (elenco_quantita[0])
    return quantita_stringa


def get_fattura_email_message(fattura, sender=MAIL_SENDER_MASTERTRAINING, recipients=None):
    context = dict(fattura=fattura)
    if not recipients and fattura.cliente and fattura.cliente.email_fatturazione:
        recipients = [fattura.cliente.email_fatturazione]
    if fattura.cliente.email_fatturazione_cc:
        elenco_destinatari_cc = fattura.cliente.email_fatturazione_cc.split(';')
        if len(elenco_destinatari_cc) > 1:
            destinatari_cc = elenco_destinatari_cc
        else:
            destinatari_cc = [fattura.cliente.email_fatturazione_cc, ]
        if recipients:
            recipients = recipients + destinatari_cc
        else:
            recipients = destinatari_cc
    if not recipients:
        raise RuntimeError('Nessun destinatario')
    print('url mailer:', settings.MAILER_URL)
    if settings.AZIENDA == 'mastercom':
        template_email_fattura = 'fatturazione/fattura_mastercom.email'
        sender = MAIL_SENDER_MASTERCOM
    else:
        template_email_fattura = 'fatturazione/fattura_mastertraining.email'
    message = render_email_message(
        template_email_fattura, context, sender, recipients
    )
    message.bcc = [sender]
    message.attach(
        fattura.get_nome_pdf(), fattura.get_pdf()
    )
    if fattura.cliente.gestione_centri_costo:
        xls_file = io.BytesIO()
        foglio_excel = crea_riepilogo_centri_costo(fattura)
        foglio_excel.save(xls_file)
        message.attach(
            'riepilogo_centri_costo.xls', xls_file.getvalue(), 'application/vnd.ms-excel'
        )
    return message


def send_fattura(fattura, sender=MAIL_SENDER_MASTERTRAINING, recipients=None):
    if fattura.gestione_azienda == 'magister':
        sender = MAIL_SENDER_MAGISTER
    if settings.AZIENDA == 'mastercom':
        sender = MAIL_SENDER_MASTERCOM
    message = get_fattura_email_message(
        fattura, sender=sender, recipients=recipients
    )
    object_type = ContentType.objects.get_for_model(fattura)
    allegati = []
    for attachment in message.attachments:
        allegato = {
            'filename': attachment[0],
            'content': base64.b64encode(attachment[1]).decode('utf-8'),
            'mimetype': attachment[2]
        }
        allegati.append(allegato)
    alternatives = []
    for alt in message.alternatives:
        alternative = {
            'content': alt[0],
            'mimetype': alt[1]
        }
        alternatives.append(alternative)
    invia_email_task(
        object_id=fattura.id,
        content_type_id=object_type.id,
        oggetto=message.subject, messaggio=message.body,
        destinatari=message.recipients(), mittente=sender,
        allegati=allegati, alternatives=alternatives
    )


def smart_truncate(text, max_length=100, suffix='...'):
    """Returns a string of at most `max_length` characters, cutting
    only at word-boundaries. If the string was truncated, `suffix`
    will be appended.
    """
    if len(text) > max_length:
        pattern = r'^(.{0,%d}\S)\s.*' % (max_length - len(suffix) - 1)
        return re.sub(pattern, r'\1' + suffix, text)
    else:
        return text


def get_xml_fattura(fattura, codice_invio_xml):
    if fattura and codice_invio_xml:
        if fattura.get_codice_pa():
            if fattura.cap:
                if len(fattura.cap) < 5:
                    msg = 'ERRORE: la fattura n.%s ha problemi al CAP' % fattura.numero
                    return msg, None
            else:
                msg = 'ERRORE: la fattura n.%s non ha il CAP' % fattura.numero
                return msg, None
            template_vars = dict()
            template_vars['fattura'] = fattura
            template_vars['codice_invio_xml'] = codice_invio_xml
            template_vars['nome_allegato'] = fattura.get_nome_pdf()
            fattura_pdf = fattura.get_pdf()
            template_vars['stringa_allegato'] = base64.encodebytes(fattura_pdf).decode('utf-8')
            t = loader.get_template('fatturazione/fattura.xml')
            fattura_xml = t.render(template_vars)
            return '', fattura_xml
        else:
            msg = 'Manca Codice PA del Cliente %s' % fattura.cliente
            return msg, None
    msg = 'Mancano dati fondamentali per procedere'
    return msg, None


def calcola_hash_file(filename):
    with open(filename, "rb") as f:
        bytes = f.read()
        readable_hash = base64.b64encode(hashlib.sha256(bytes).digest())
        return readable_hash
