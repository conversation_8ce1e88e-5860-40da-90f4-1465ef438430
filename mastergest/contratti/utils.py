from mastergest.canoni.models import Canone


def crea_canone_contratto(contratto):
    if contratto:
        if contratto.data_inizio_rate:
            elenco_canoni = contratto.canone_set.all()
            if elenco_canoni:
                return 'ERRORE: Il contratto selezionato ha gia\' dei canoni presenti!'
            else:
                if contratto.categoria_canone and contratto.importo_rata_canone:
                    nuovo_canone = Canone()
                    nuovo_canone.contratto = contratto
                    nuovo_canone.data_inizio = contratto.data_inizio_rate
                    nuovo_canone.numero_rate = contratto.numero_rate_canone
                    nuovo_canone.tipo_rinnovo = contratto.tipo_rinnovo_canone
                    nuovo_canone.periodicita = contratto.periodicita_canone
                    nuovo_canone.tipologia = contratto.tipologia_canone
                    nuovo_canone.categoria_canone = contratto.categoria_canone
                    nuovo_canone.descrizione_aggiuntiva_fattura = contratto.descrizione_aggiuntiva_fattura_canone
                    nuovo_canone.anticipato = contratto.anticipato_canone
                    nuovo_canone.con_ratino = contratto.con_ratino_canone
                    nuovo_canone.importo_rata = contratto.importo_rata_canone
                    nuovo_canone.numero_rate_rinnovo = contratto.numero_rate_rinnovo_canone
                    nuovo_canone.save()
                    return 'Nuovo canone creato correttamente!'
                else:
                    return 'ERRORE: Il contratto selezionato non ha i dati minimi necessari per creare il canone.'
        else:
            return 'ERRORE: Il contratto selezionato non e\' ancora stato collaudato!'
