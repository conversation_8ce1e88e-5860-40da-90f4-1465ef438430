from django.db import models
from django.urls import reverse
from mastergest.contratti.models import Contratto


class ContrattoVodafoneManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoVodafoneManager, self).get_queryset()
        return qs.filter(fornitore='vodafone')


class ContrattoVodafone(Contratto):
    objects = ContrattoVodafoneManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto vodafone'
        verbose_name_plural = 'contratti vodafone'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'vodafone'
        if not self.tipologia:
            self.tipologia = 'mobile'
        return super(ContrattoVodafone, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattovodafone_change', args=(self.id,))
        return url
