from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.utils.html import format_html

from mastergest.contratti.models import Contratto, Preordine
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class PreordineIpkomManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineIpkomManager, self).get_queryset()
        return qs.filter(tipo='ipkom')


class PreordineIpkom(Preordine):
    objects = PreordineIpkomManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine ipkom'
        verbose_name_plural = 'preordini ipkom'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'ipkom'
        return super(PreordineIpkom, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_preordineipkom_change', args=(self.id,))
        return url


class ContrattoIpkomManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoIpkomManager, self).get_queryset()
        return qs.filter(fornitore='ipkom')


class ContrattoIpkom(Contratto):
    objects = ContrattoIpkomManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto ipkom'
        verbose_name_plural = 'contratti ipkom'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'ipkom'
        if not self.tipologia:
            self.tipologia = 'fisso'
        return super(ContrattoIpkom, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattoipkom_change', args=(self.id,))
        return url


class InstallazioneIpkomManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneIpkomManager, self).get_queryset()
        return qs.filter(fornitore='ipkom', categoria='installazione')


class InstallazioneIpkom(ContrattoIpkom):
    objects = InstallazioneIpkomManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione ipkom'
        verbose_name_plural = 'installazioni ipkom'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione IPKOM per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'ipkom'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneIpkom.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneIpkom, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'

    def get_link_riferimento(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html('<a href="%s" target="">Vedi</a>' % url_file)
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html('<a href="%s" target="">Vedi</a>' % url_file)
        return ''
    get_link_riferimento.short_description = 'Riferimento'

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.offerta))
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.preordine))
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
