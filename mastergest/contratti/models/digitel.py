from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse

from mastergest.utils.mail import render_email_message
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_NUOVO_CONTRATTO_DIGITEL_INSERITO = [
    'g.muzzin<PERSON>@mastertraining.it',
    'm.bizza<PERSON>@mastertraining.it',
    '<EMAIL>',
    '<EMAIL>',
]


class ContrattoDigitelManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoDigitelManager, self).get_queryset()
        return qs.filter(fornitore='digitel')


class ContrattoDigitel(Contratto):
    objects = ContrattoDigitelManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto Digitel'
        verbose_name_plural = 'contratti Digitel'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'digitel'
        if not self.tipologia:
            self.tipologia = 'fisso'
        nuovo_contratto = False
        if not self.id:
            nuovo_contratto = True
        super(ContrattoDigitel, self).save(*args, **kwargs)
        if nuovo_contratto:
            self.email_contratto_kpn_inserito()

    def get_url(self):
        url = reverse('admin:contratti_contrattodigitel_change', args=(self.id,))
        return url

    def email_contratto_kpn_inserito(self):
        sender = '<EMAIL>'
        # context = dict(
        #     contratto=self,
        #     dettaglio=self.dettagliocontratto_set.all(),
        #     materiale=self.materialecontratto_set.all()
        # )
        # message = render_email_message(
        #     'contratto/nuovo_contratto_inserito.email',
        #     context,
        #     sender,
        #     DESTINATARI_EMAIL_NUOVO_CONTRATTO_DIGITEL_INSERITO
        # )
        # connection = mail.get_connection()
        # connection.open()
        # connection.send_messages([message])
        # connection.close()


class InstallazioneDigitelManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneDigitelManager, self).get_queryset()
        return qs.filter(fornitore='digitel', categoria='installazione')


class InstallazioneDigitel(ContrattoDigitel):
    objects = InstallazioneDigitelManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione digitel'
        verbose_name_plural = 'installazioni digitel'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione Digitel per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'digitel'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneDigitel.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneDigitel, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'
    get_totale_installazione.allow_tags = True

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.offerta)
        if self.preordine:
            url_file = self.preordine.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.preordine)
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
    get_link_riferimento_lista.allow_tags = True

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'
    get_costi_installazione.allow_tags = True

    def genera_acquisto(self, utente):
        from mastergest.acquisti.models import Acquisto, RigaAcquisto
        codice = 'CONTR_%s' % self.id
        try:
            acquisto_esistente = Acquisto.objects.get(riferimento=codice)
            return acquisto_esistente
        except Acquisto.DoesNotExist:
            nuovo_acquisto = Acquisto.objects.create(
                richiedente=utente,
                riferimento=codice,
                settore='mastervoice',
                note='Creato in automatico da contratto ID:%s - Cliente %s' % (self.pk, self.azienda)
            )
            for materiale in self.materialecontratto_set.all():
                nuova_riga = RigaAcquisto.objects.create(
                    acquisto=nuovo_acquisto,
                    prodotto=materiale.prodotto,
                    quantita=materiale.quantita
                )
                nuova_riga.save()
            return nuovo_acquisto
