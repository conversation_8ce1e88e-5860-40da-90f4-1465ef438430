[{"pk": 2, "model": "contratti.agente", "fields": {"attivo": true, "cognome": "<PERSON><PERSON>", "nome": "<PERSON>"}}, {"pk": 3, "model": "contratti.agente", "fields": {"attivo": true, "cognome": "Sala", "nome": "<PERSON>"}}, {"pk": 1, "model": "contratti.agente", "fields": {"attivo": true, "cognome": "master", "nome": "training"}}, {"pk": 6, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/02", "descrizione": "25 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 7, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/03", "descrizione": "25 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 8, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/04", "descrizione": "25 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 9, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/05", "descrizione": "25 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 10, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/06", "descrizione": "25 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 11, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/07", "descrizione": "25 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 12, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/08", "descrizione": "25 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 13, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/09", "descrizione": "25 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 14, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/02", "descrizione": "26-55 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 15, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/03", "descrizione": "26-55 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 17, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/04", "descrizione": "26-55 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 18, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/05", "descrizione": "26-55 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 19, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/06", "descrizione": "26-55 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 20, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/07", "descrizione": "26-55 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 21, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/08", "descrizione": "26-55 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 22, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/09", "descrizione": "26-55 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 23, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/10", "descrizione": "26-55 utenti / 10 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 24, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/11", "descrizione": "26-55 utenti / 11 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 25, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/12", "descrizione": "26-55 utenti / 12 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 26, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/15", "descrizione": "26-55 utenti / 15 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 27, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/02", "descrizione": "56-100 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 29, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/03", "descrizione": "56-100 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 30, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/04", "descrizione": "56-100 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 31, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/05", "descrizione": "56-100 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 32, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/06", "descrizione": "56-100 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 33, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/07", "descrizione": "56-100 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 34, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/08", "descrizione": "56-100 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 35, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/09", "descrizione": "56-100 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 36, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/10", "descrizione": "56-100 utenti / 10 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 37, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/11", "descrizione": "56-100 utenti / 11 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 38, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/12", "descrizione": "56-100 utenti / 12 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 39, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/15", "descrizione": "56-100 utenti / 15 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 40, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/30", "descrizione": "56-100 utenti / 30 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 41, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/02", "descrizione": "101-200 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 42, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/03", "descrizione": "101-200 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 44, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/04", "descrizione": "101-200 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 45, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/05", "descrizione": "101-200 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 46, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/06", "descrizione": "101-200 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 47, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/07", "descrizione": "101-200 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 48, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/08", "descrizione": "101-200 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 49, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/09", "descrizione": "101-200 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 50, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/10", "descrizione": "101-200 utenti / 10 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 51, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/11", "descrizione": "101-200 utenti / 11 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 52, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/12", "descrizione": "101-200 utenti / 12 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 53, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/15", "descrizione": "101-200 utenti / 15 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 54, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/30", "descrizione": "101-200 utenti / 30 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 1, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "BB", "descrizione": "Opzione BB", "fornitore": "wind", "sottotipo": "opzione", "tipologia": "mobile"}}, {"pk": 4, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "dati_evo", "descrizione": "Sim dati INTERNET EVO", "fornitore": "wind", "sottotipo": "dati", "tipologia": "mobile"}}, {"pk": 3, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "dati_plus", "descrizione": "Sim dati INTERNET PLUS", "fornitore": "wind", "sottotipo": "dati", "tipologia": "mobile"}}, {"pk": 2, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "dati_start", "descrizione": "Sim dati INTERNET START", "fornitore": "wind", "sottotipo": "dati", "tipologia": "mobile"}}, {"pk": 5, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_new_time", "descrizione": "SIM VOCE NEW time + conversioni", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 55, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_mnp_abb", "descrizione": "SIM VOCE MNP da abbonamento time", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 56, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_new_wind", "descrizione": "SIM VOCE NEW wind aziende/basic + conversioni", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 57, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_new_wind", "descrizione": "SIM VOCE MNP da abbonamento wind aziende/basic", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 58, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start", "descrizione": "SIM VOCE Wind Start New", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 59, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start_email", "descrizione": "SIM VOCE Wind Start New con email", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 60, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start_mnp", "descrizione": "SIM VOCE Wind Start MNP", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 61, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start_mnp_email", "descrizione": "SIM VOCE Wind Start MNP con email", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 62, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_m", "descrizione": "SIM VOCE One mobile M new e MNP", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 63, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_m_email", "descrizione": "SIM VOCE One mobile M new e MNP con email", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 64, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_l", "descrizione": "SIM VOCE One mobile L new e MNP", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 65, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_l_email", "descrizione": "SIM VOCE One mobile L new e MNP con email", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 66, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_xl", "descrizione": "SIM VOCE One mobile XL new e MNP", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}, {"pk": 67, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_xl_email", "descrizione": "SIM VOCE One mobile XL new e MNP con email", "fornitore": "wind", "sottotipo": "voce", "tipologia": "mobile"}}]