[{"pk": 1, "model": "contratti.agente", "fields": {"cognome": "master", "nome": "training"}}, {"pk": 2, "model": "contratti.agente", "fields": {"cognome": "<PERSON><PERSON>", "nome": "<PERSON>"}}, {"pk": 3, "model": "contratti.agente", "fields": {"cognome": "Sala", "nome": "<PERSON>"}}, {"pk": 6, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/02", "sottotipo": null, "descrizione": "25 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 7, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/03", "sottotipo": null, "descrizione": "25 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 8, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/04", "sottotipo": null, "descrizione": "25 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 9, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/05", "sottotipo": null, "descrizione": "25 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 10, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/06", "sottotipo": null, "descrizione": "25 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 11, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/07", "sottotipo": null, "descrizione": "25 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 12, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/08", "sottotipo": null, "descrizione": "25 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 13, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/09", "sottotipo": null, "descrizione": "25 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 14, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/02", "sottotipo": null, "descrizione": "26-55 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 15, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/03", "sottotipo": null, "descrizione": "26-55 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 17, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/04", "sottotipo": null, "descrizione": "26-55 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 18, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/05", "sottotipo": null, "descrizione": "26-55 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 19, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/06", "sottotipo": null, "descrizione": "26-55 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 20, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/07", "sottotipo": null, "descrizione": "26-55 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 21, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/08", "sottotipo": null, "descrizione": "26-55 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 22, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/09", "sottotipo": null, "descrizione": "26-55 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 23, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/10", "sottotipo": null, "descrizione": "26-55 utenti / 10 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 24, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/11", "sottotipo": null, "descrizione": "26-55 utenti / 11 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 25, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/12", "sottotipo": null, "descrizione": "26-55 utenti / 12 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 26, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "026/15", "sottotipo": null, "descrizione": "26-55 utenti / 15 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 27, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/02", "sottotipo": null, "descrizione": "56-100 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 29, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/03", "sottotipo": null, "descrizione": "56-100 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 30, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/04", "sottotipo": null, "descrizione": "56-100 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 31, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/05", "sottotipo": null, "descrizione": "56-100 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 32, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/06", "sottotipo": null, "descrizione": "56-100 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 33, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/07", "sottotipo": null, "descrizione": "56-100 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 34, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/08", "sottotipo": null, "descrizione": "56-100 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 35, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/09", "sottotipo": null, "descrizione": "56-100 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 36, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/10", "sottotipo": null, "descrizione": "56-100 utenti / 10 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 37, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/11", "sottotipo": null, "descrizione": "56-100 utenti / 11 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 38, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/12", "sottotipo": null, "descrizione": "56-100 utenti / 12 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 39, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/15", "sottotipo": null, "descrizione": "56-100 utenti / 15 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 40, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "056/30", "sottotipo": null, "descrizione": "56-100 utenti / 30 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 41, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/02", "sottotipo": null, "descrizione": "101-200 utenti / 2 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 42, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/03", "sottotipo": null, "descrizione": "101-200 utenti / 3 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 44, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/04", "sottotipo": null, "descrizione": "101-200 utenti / 4 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 45, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/05", "sottotipo": null, "descrizione": "101-200 utenti / 5 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 46, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/06", "sottotipo": null, "descrizione": "101-200 utenti / 6 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 47, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/07", "sottotipo": null, "descrizione": "101-200 utenti / 7 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 48, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/08", "sottotipo": null, "descrizione": "101-200 utenti / 8 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 49, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/09", "sottotipo": null, "descrizione": "101-200 utenti / 9 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 50, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/10", "sottotipo": null, "descrizione": "101-200 utenti / 10 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 51, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/11", "sottotipo": null, "descrizione": "101-200 utenti / 11 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 52, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/12", "sottotipo": null, "descrizione": "101-200 utenti / 12 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 53, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/15", "sottotipo": null, "descrizione": "101-200 utenti / 15 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 54, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/30", "sottotipo": null, "descrizione": "101-200 utenti / 30 linee", "fornitore": "fastweb", "tipologia": "fisso"}}, {"pk": 1, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "BB", "sottotipo": "opzione", "descrizione": "Opzione BB", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 4, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "dati_evo", "sottotipo": "dati", "descrizione": "Sim dati INTERNET EVO", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 3, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "dati_plus", "sottotipo": "dati", "descrizione": "Sim dati INTERNET PLUS", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 2, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "dati_start", "sottotipo": "dati", "descrizione": "Sim dati INTERNET START", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 55, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_mnp_abb", "sottotipo": "voce", "descrizione": "SIM VOCE MNP da abbonamento time", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 5, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_new_time", "sottotipo": "voce", "descrizione": "SIM VOCE NEW time + conversioni", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 57, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_new_wind", "sottotipo": "voce", "descrizione": "SIM VOCE MNP da abbonamento wind aziende/basic", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 56, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_new_wind", "sottotipo": "voce", "descrizione": "SIM VOCE NEW wind aziende/basic + conversioni", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 64, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_l", "sottotipo": "voce", "descrizione": "SIM VOCE One mobile L new e MNP", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 65, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_l_email", "sottotipo": "voce", "descrizione": "SIM VOCE One mobile L new e MNP con email", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 62, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_m", "sottotipo": "voce", "descrizione": "SIM VOCE One mobile M new e MNP", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 63, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_m_email", "sottotipo": "voce", "descrizione": "SIM VOCE One mobile M new e MNP con email", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 66, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_xl", "sottotipo": "voce", "descrizione": "SIM VOCE One mobile XL new e MNP", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 67, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_one_xl_email", "sottotipo": "voce", "descrizione": "SIM VOCE One mobile XL new e MNP con email", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 58, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start", "sottotipo": "voce", "descrizione": "SIM VOCE Wind Start New", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 59, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start_email", "sottotipo": "voce", "descrizione": "SIM VOCE Wind Start New con email", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 60, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start_mnp", "sottotipo": "voce", "descrizione": "SIM VOCE Wind Start MNP", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 61, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "voce_wind_start_mnp_email", "sottotipo": "voce", "descrizione": "SIM VOCE Wind Start MNP con email", "fornitore": "wind", "tipologia": "mobile"}}, {"pk": 1, "model": "contratti.schemaprovvigionestandard", "fields": {"tipologia": "fisso", "data_inizio_validita": "2012-01-01", "data_fine_validita": "2012-12-31", "descrizione": "Base 2012", "fornitore": "fastweb"}}, {"pk": 2, "model": "contratti.schemaprovvigionestandard", "fields": {"tipologia": "mobile", "data_inizio_validita": "2012-01-01", "data_fine_validita": "2012-12-31", "descrizione": "contratti 2012", "fornitore": "wind"}}, {"pk": 104, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "120.00", "tipo_contratto": 6, "schema_provvigione": 1}}, {"pk": 105, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "140.00", "tipo_contratto": 7, "schema_provvigione": 1}}, {"pk": 106, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "155.00", "tipo_contratto": 8, "schema_provvigione": 1}}, {"pk": 107, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "175.00", "tipo_contratto": 9, "schema_provvigione": 1}}, {"pk": 108, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "190.00", "tipo_contratto": 10, "schema_provvigione": 1}}, {"pk": 109, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "210.00", "tipo_contratto": 11, "schema_provvigione": 1}}, {"pk": 110, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "230.00", "tipo_contratto": 12, "schema_provvigione": 1}}, {"pk": 111, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "245.00", "tipo_contratto": 13, "schema_provvigione": 1}}, {"pk": 112, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "150.00", "tipo_contratto": 14, "schema_provvigione": 1}}, {"pk": 113, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "165.00", "tipo_contratto": 15, "schema_provvigione": 1}}, {"pk": 114, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "185.00", "tipo_contratto": 17, "schema_provvigione": 1}}, {"pk": 115, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "205.00", "tipo_contratto": 18, "schema_provvigione": 1}}, {"pk": 116, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "220.00", "tipo_contratto": 19, "schema_provvigione": 1}}, {"pk": 117, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "240.00", "tipo_contratto": 20, "schema_provvigione": 1}}, {"pk": 118, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "260.00", "tipo_contratto": 21, "schema_provvigione": 1}}, {"pk": 119, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "275.00", "tipo_contratto": 22, "schema_provvigione": 1}}, {"pk": 120, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "290.00", "tipo_contratto": 23, "schema_provvigione": 1}}, {"pk": 121, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "290.00", "tipo_contratto": 24, "schema_provvigione": 1}}, {"pk": 122, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "290.00", "tipo_contratto": 25, "schema_provvigione": 1}}, {"pk": 123, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "300.00", "tipo_contratto": 26, "schema_provvigione": 1}}, {"pk": 124, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "250.00", "tipo_contratto": 27, "schema_provvigione": 1}}, {"pk": 125, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "270.00", "tipo_contratto": 29, "schema_provvigione": 1}}, {"pk": 126, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "285.00", "tipo_contratto": 30, "schema_provvigione": 1}}, {"pk": 127, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "305.00", "tipo_contratto": 31, "schema_provvigione": 1}}, {"pk": 128, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "320.00", "tipo_contratto": 32, "schema_provvigione": 1}}, {"pk": 129, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "340.00", "tipo_contratto": 33, "schema_provvigione": 1}}, {"pk": 130, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "360.00", "tipo_contratto": 34, "schema_provvigione": 1}}, {"pk": 131, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "375.00", "tipo_contratto": 35, "schema_provvigione": 1}}, {"pk": 132, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "395.00", "tipo_contratto": 36, "schema_provvigione": 1}}, {"pk": 133, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "395.00", "tipo_contratto": 37, "schema_provvigione": 1}}, {"pk": 134, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "395.00", "tipo_contratto": 38, "schema_provvigione": 1}}, {"pk": 135, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "400.00", "tipo_contratto": 39, "schema_provvigione": 1}}, {"pk": 136, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "500.00", "tipo_contratto": 40, "schema_provvigione": 1}}, {"pk": 137, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "395.00", "tipo_contratto": 41, "schema_provvigione": 1}}, {"pk": 138, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "410.00", "tipo_contratto": 42, "schema_provvigione": 1}}, {"pk": 139, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "430.00", "tipo_contratto": 44, "schema_provvigione": 1}}, {"pk": 140, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "450.00", "tipo_contratto": 45, "schema_provvigione": 1}}, {"pk": 141, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "465.00", "tipo_contratto": 46, "schema_provvigione": 1}}, {"pk": 142, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "485.00", "tipo_contratto": 47, "schema_provvigione": 1}}, {"pk": 143, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "500.00", "tipo_contratto": 48, "schema_provvigione": 1}}, {"pk": 144, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "520.00", "tipo_contratto": 49, "schema_provvigione": 1}}, {"pk": 145, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "540.00", "tipo_contratto": 50, "schema_provvigione": 1}}, {"pk": 146, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "540.00", "tipo_contratto": 51, "schema_provvigione": 1}}, {"pk": 147, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "540.00", "tipo_contratto": 52, "schema_provvigione": 1}}, {"pk": 148, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "545.00", "tipo_contratto": 53, "schema_provvigione": 1}}, {"pk": 149, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "645.00", "tipo_contratto": 54, "schema_provvigione": 1}}, {"pk": 157, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "15.00", "tipo_contratto": 1, "schema_provvigione": 2}}, {"pk": 158, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "44.00", "tipo_contratto": 4, "schema_provvigione": 2}}, {"pk": 159, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "36.00", "tipo_contratto": 3, "schema_provvigione": 2}}, {"pk": 160, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "16.00", "tipo_contratto": 2, "schema_provvigione": 2}}, {"pk": 161, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "32.00", "tipo_contratto": 55, "schema_provvigione": 2}}, {"pk": 162, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "20.00", "tipo_contratto": 5, "schema_provvigione": 2}}, {"pk": 163, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "56.00", "tipo_contratto": 57, "schema_provvigione": 2}}, {"pk": 164, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "30.00", "tipo_contratto": 56, "schema_provvigione": 2}}, {"pk": 165, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "78.00", "tipo_contratto": 64, "schema_provvigione": 2}}, {"pk": 166, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "93.00", "tipo_contratto": 65, "schema_provvigione": 2}}, {"pk": 167, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "74.00", "tipo_contratto": 62, "schema_provvigione": 2}}, {"pk": 168, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "89.00", "tipo_contratto": 63, "schema_provvigione": 2}}, {"pk": 169, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "90.00", "tipo_contratto": 66, "schema_provvigione": 2}}, {"pk": 170, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "105.00", "tipo_contratto": 67, "schema_provvigione": 2}}, {"pk": 171, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "52.00", "tipo_contratto": 58, "schema_provvigione": 2}}, {"pk": 172, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "68.00", "tipo_contratto": 59, "schema_provvigione": 2}}, {"pk": 173, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "60.00", "tipo_contratto": 60, "schema_provvigione": 2}}, {"pk": 174, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "75.00", "tipo_contratto": 61, "schema_provvigione": 2}}, {"pk": 1, "model": "contratti.schemaprovvigioneagente", "fields": {"agente": 1, "data_fine_validita": "2012-12-31", "mesi_liquidazione": 0, "schema_provvigione": 1, "fornitore": "fastweb", "percentuale_liquidazione": 100, "tipologia": "fisso", "data_inizio_validita": "2012-01-01"}}, {"pk": 2, "model": "contratti.schemaprovvigioneagente", "fields": {"agente": 1, "data_fine_validita": "2012-12-31", "mesi_liquidazione": 0, "schema_provvigione": 2, "fornitore": "wind", "percentuale_liquidazione": 100, "tipologia": "mobile", "data_inizio_validita": "2012-01-01"}}, {"pk": 4, "model": "contratti.schemaprovvigioneagente", "fields": {"agente": 3, "data_fine_validita": "2012-12-31", "mesi_liquidazione": 4, "schema_provvigione": 2, "fornitore": "wind", "percentuale_liquidazione": 60, "tipologia": "mobile", "data_inizio_validita": "2012-01-24"}}, {"pk": 3, "model": "contratti.schemaprovvigioneagente", "fields": {"agente": 2, "data_fine_validita": "2012-12-31", "mesi_liquidazione": 3, "schema_provvigione": 1, "fornitore": "fastweb", "percentuale_liquidazione": 75, "tipologia": "fisso", "data_inizio_validita": "2012-01-24"}}, {"pk": 5, "model": "contratti.schemaprovvigioneagente", "fields": {"agente": 2, "data_fine_validita": "2012-12-31", "mesi_liquidazione": 6, "schema_provvigione": 2, "fornitore": "wind", "percentuale_liquidazione": 79, "tipologia": "mobile", "data_inizio_validita": "2012-07-20"}}, {"pk": 176, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "10.00", "tipo_contratto": 6, "schema_provvigione": 3}}, {"pk": 112, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "120.00", "tipo_contratto": 6, "schema_provvigione": 1}}, {"pk": 177, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "20.00", "tipo_contratto": 7, "schema_provvigione": 3}}, {"pk": 113, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "140.00", "tipo_contratto": 7, "schema_provvigione": 1}}, {"pk": 178, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "30.00", "tipo_contratto": 8, "schema_provvigione": 3}}, {"pk": 114, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "155.00", "tipo_contratto": 8, "schema_provvigione": 1}}, {"pk": 179, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "40.00", "tipo_contratto": 9, "schema_provvigione": 3}}, {"pk": 115, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "175.00", "tipo_contratto": 9, "schema_provvigione": 1}}, {"pk": 180, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "50.00", "tipo_contratto": 10, "schema_provvigione": 3}}, {"pk": 116, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "190.00", "tipo_contratto": 10, "schema_provvigione": 1}}, {"pk": 181, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "60.00", "tipo_contratto": 11, "schema_provvigione": 3}}, {"pk": 117, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "210.00", "tipo_contratto": 11, "schema_provvigione": 1}}, {"pk": 182, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "70.00", "tipo_contratto": 12, "schema_provvigione": 3}}, {"pk": 118, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "230.00", "tipo_contratto": 12, "schema_provvigione": 1}}, {"pk": 183, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "80.00", "tipo_contratto": 13, "schema_provvigione": 3}}, {"pk": 119, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "245.00", "tipo_contratto": 13, "schema_provvigione": 1}}, {"pk": 184, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "90.00", "tipo_contratto": 14, "schema_provvigione": 3}}, {"pk": 120, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "150.00", "tipo_contratto": 14, "schema_provvigione": 1}}, {"pk": 185, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "100.00", "tipo_contratto": 15, "schema_provvigione": 3}}, {"pk": 121, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "165.00", "tipo_contratto": 15, "schema_provvigione": 1}}, {"pk": 186, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "110.00", "tipo_contratto": 17, "schema_provvigione": 3}}, {"pk": 122, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "185.00", "tipo_contratto": 17, "schema_provvigione": 1}}, {"pk": 187, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "120.00", "tipo_contratto": 18, "schema_provvigione": 3}}, {"pk": 123, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "205.00", "tipo_contratto": 18, "schema_provvigione": 1}}, {"pk": 188, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "220.00", "tipo_contratto": 19, "schema_provvigione": 3}}, {"pk": 124, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "220.00", "tipo_contratto": 19, "schema_provvigione": 1}}, {"pk": 189, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "240.00", "tipo_contratto": 20, "schema_provvigione": 3}}, {"pk": 125, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "240.00", "tipo_contratto": 20, "schema_provvigione": 1}}, {"pk": 190, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "260.00", "tipo_contratto": 21, "schema_provvigione": 3}}, {"pk": 126, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "260.00", "tipo_contratto": 21, "schema_provvigione": 1}}, {"pk": 191, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "275.00", "tipo_contratto": 22, "schema_provvigione": 3}}, {"pk": 127, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "275.00", "tipo_contratto": 22, "schema_provvigione": 1}}, {"pk": 192, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "290.00", "tipo_contratto": 23, "schema_provvigione": 3}}, {"pk": 128, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "290.00", "tipo_contratto": 23, "schema_provvigione": 1}}, {"pk": 193, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "290.00", "tipo_contratto": 24, "schema_provvigione": 3}}, {"pk": 129, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "290.00", "tipo_contratto": 24, "schema_provvigione": 1}}, {"pk": 194, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "290.00", "tipo_contratto": 25, "schema_provvigione": 3}}, {"pk": 130, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "290.00", "tipo_contratto": 25, "schema_provvigione": 1}}, {"pk": 195, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "300.00", "tipo_contratto": 26, "schema_provvigione": 3}}, {"pk": 131, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "300.00", "tipo_contratto": 26, "schema_provvigione": 1}}, {"pk": 196, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "250.00", "tipo_contratto": 27, "schema_provvigione": 3}}, {"pk": 132, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "250.00", "tipo_contratto": 27, "schema_provvigione": 1}}, {"pk": 197, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "270.00", "tipo_contratto": 29, "schema_provvigione": 3}}, {"pk": 133, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "270.00", "tipo_contratto": 29, "schema_provvigione": 1}}, {"pk": 198, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "285.00", "tipo_contratto": 30, "schema_provvigione": 3}}, {"pk": 134, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "285.00", "tipo_contratto": 30, "schema_provvigione": 1}}, {"pk": 199, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "305.00", "tipo_contratto": 31, "schema_provvigione": 3}}, {"pk": 135, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "305.00", "tipo_contratto": 31, "schema_provvigione": 1}}, {"pk": 200, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "320.00", "tipo_contratto": 32, "schema_provvigione": 3}}, {"pk": 136, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "320.00", "tipo_contratto": 32, "schema_provvigione": 1}}, {"pk": 201, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "340.00", "tipo_contratto": 33, "schema_provvigione": 3}}, {"pk": 137, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "340.00", "tipo_contratto": 33, "schema_provvigione": 1}}, {"pk": 202, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "360.00", "tipo_contratto": 34, "schema_provvigione": 3}}, {"pk": 138, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "360.00", "tipo_contratto": 34, "schema_provvigione": 1}}, {"pk": 203, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "375.00", "tipo_contratto": 35, "schema_provvigione": 3}}, {"pk": 139, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "375.00", "tipo_contratto": 35, "schema_provvigione": 1}}, {"pk": 204, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 36, "schema_provvigione": 3}}, {"pk": 140, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 36, "schema_provvigione": 1}}, {"pk": 205, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 37, "schema_provvigione": 3}}, {"pk": 141, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 37, "schema_provvigione": 1}}, {"pk": 206, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 38, "schema_provvigione": 3}}, {"pk": 142, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 38, "schema_provvigione": 1}}, {"pk": 207, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "400.00", "tipo_contratto": 39, "schema_provvigione": 3}}, {"pk": 143, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "400.00", "tipo_contratto": 39, "schema_provvigione": 1}}, {"pk": 208, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "500.00", "tipo_contratto": 40, "schema_provvigione": 3}}, {"pk": 144, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "500.00", "tipo_contratto": 40, "schema_provvigione": 1}}, {"pk": 145, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 41, "schema_provvigione": 1}}, {"pk": 209, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "395.00", "tipo_contratto": 41, "schema_provvigione": 3}}, {"pk": 210, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "410.00", "tipo_contratto": 42, "schema_provvigione": 3}}, {"pk": 146, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "410.00", "tipo_contratto": 42, "schema_provvigione": 1}}, {"pk": 211, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "430.00", "tipo_contratto": 44, "schema_provvigione": 3}}, {"pk": 147, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "430.00", "tipo_contratto": 44, "schema_provvigione": 1}}, {"pk": 212, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "450.00", "tipo_contratto": 45, "schema_provvigione": 3}}, {"pk": 148, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "450.00", "tipo_contratto": 45, "schema_provvigione": 1}}, {"pk": 213, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "465.00", "tipo_contratto": 46, "schema_provvigione": 3}}, {"pk": 149, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "465.00", "tipo_contratto": 46, "schema_provvigione": 1}}, {"pk": 214, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "485.00", "tipo_contratto": 47, "schema_provvigione": 3}}, {"pk": 150, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "485.00", "tipo_contratto": 47, "schema_provvigione": 1}}, {"pk": 215, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "500.00", "tipo_contratto": 48, "schema_provvigione": 3}}, {"pk": 151, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "500.00", "tipo_contratto": 48, "schema_provvigione": 1}}, {"pk": 216, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "520.00", "tipo_contratto": 49, "schema_provvigione": 3}}, {"pk": 152, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "520.00", "tipo_contratto": 49, "schema_provvigione": 1}}, {"pk": 217, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "540.00", "tipo_contratto": 50, "schema_provvigione": 3}}, {"pk": 153, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "540.00", "tipo_contratto": 50, "schema_provvigione": 1}}, {"pk": 218, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "540.00", "tipo_contratto": 51, "schema_provvigione": 3}}, {"pk": 154, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "540.00", "tipo_contratto": 51, "schema_provvigione": 1}}, {"pk": 219, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "540.00", "tipo_contratto": 52, "schema_provvigione": 3}}, {"pk": 155, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "540.00", "tipo_contratto": 52, "schema_provvigione": 1}}, {"pk": 220, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "545.00", "tipo_contratto": 53, "schema_provvigione": 3}}, {"pk": 156, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "545.00", "tipo_contratto": 53, "schema_provvigione": 1}}, {"pk": 221, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "645.00", "tipo_contratto": 54, "schema_provvigione": 3}}, {"pk": 157, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "645.00", "tipo_contratto": 54, "schema_provvigione": 1}}, {"pk": 222, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "1.00", "tipo_contratto": 1, "schema_provvigione": 5}}, {"pk": 158, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "15.00", "tipo_contratto": 1, "schema_provvigione": 2}}, {"pk": 223, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "2.00", "tipo_contratto": 4, "schema_provvigione": 5}}, {"pk": 159, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "44.00", "tipo_contratto": 4, "schema_provvigione": 2}}, {"pk": 160, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "36.00", "tipo_contratto": 3, "schema_provvigione": 2}}, {"pk": 224, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "3.00", "tipo_contratto": 3, "schema_provvigione": 5}}, {"pk": 225, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "4.00", "tipo_contratto": 2, "schema_provvigione": 5}}, {"pk": 161, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "16.00", "tipo_contratto": 2, "schema_provvigione": 2}}, {"pk": 162, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "32.00", "tipo_contratto": 55, "schema_provvigione": 2}}, {"pk": 226, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "5.00", "tipo_contratto": 55, "schema_provvigione": 5}}, {"pk": 227, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "6.00", "tipo_contratto": 5, "schema_provvigione": 5}}, {"pk": 163, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "20.00", "tipo_contratto": 5, "schema_provvigione": 2}}, {"pk": 164, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "56.00", "tipo_contratto": 57, "schema_provvigione": 2}}, {"pk": 228, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "7.00", "tipo_contratto": 57, "schema_provvigione": 5}}, {"pk": 229, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "8.00", "tipo_contratto": 56, "schema_provvigione": 5}}, {"pk": 165, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "30.00", "tipo_contratto": 56, "schema_provvigione": 2}}, {"pk": 230, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "9.00", "tipo_contratto": 64, "schema_provvigione": 5}}, {"pk": 166, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "78.00", "tipo_contratto": 64, "schema_provvigione": 2}}, {"pk": 167, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "93.00", "tipo_contratto": 65, "schema_provvigione": 2}}, {"pk": 231, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "10.00", "tipo_contratto": 65, "schema_provvigione": 5}}, {"pk": 168, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "74.00", "tipo_contratto": 62, "schema_provvigione": 2}}, {"pk": 232, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "11.00", "tipo_contratto": 62, "schema_provvigione": 5}}, {"pk": 169, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "89.00", "tipo_contratto": 63, "schema_provvigione": 2}}, {"pk": 233, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "12.00", "tipo_contratto": 63, "schema_provvigione": 5}}, {"pk": 170, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "90.00", "tipo_contratto": 66, "schema_provvigione": 2}}, {"pk": 234, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "13.00", "tipo_contratto": 66, "schema_provvigione": 5}}, {"pk": 171, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "105.00", "tipo_contratto": 67, "schema_provvigione": 2}}, {"pk": 235, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "14.00", "tipo_contratto": 67, "schema_provvigione": 5}}, {"pk": 236, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "15.00", "tipo_contratto": 58, "schema_provvigione": 5}}, {"pk": 172, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "52.00", "tipo_contratto": 58, "schema_provvigione": 2}}, {"pk": 173, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "68.00", "tipo_contratto": 59, "schema_provvigione": 2}}, {"pk": 237, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "16.00", "tipo_contratto": 59, "schema_provvigione": 5}}, {"pk": 174, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "60.00", "tipo_contratto": 60, "schema_provvigione": 2}}, {"pk": 238, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "17.00", "tipo_contratto": 60, "schema_provvigione": 5}}, {"pk": 175, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "75.00", "tipo_contratto": 61, "schema_provvigione": 2}}, {"pk": 239, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "18.00", "tipo_contratto": 61, "schema_provvigione": 5}}]