from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.utils.html import format_html
from django.conf import settings
from django.contrib.contenttypes.models import ContentType

from mastergest.utils.mail import render_email_message
from mastergest.contratti.models import Contratto, Preordine
from mastergest.utils.tasks import invia_email_task

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_NUOVO_CONTRATTO_HAL_INSERITO = [
    '<EMAIL>',
    'm.bizza<PERSON>@mastertraining.it',
    '<EMAIL>',
    '<EMAIL>',
]


class PreordineHalManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineHalManager, self).get_queryset()
        return qs.filter(tipo='hal')


class PreordineHal(Preordine):
    objects = PreordineHalManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine hal'
        verbose_name_plural = 'preordini hal'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'hal'
        return super(PreordineHal, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_preordinehal_change', args=(self.id,))
        return url


class ContrattoHalManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoHalManager, self).get_queryset()
        return qs.filter(fornitore='hal')


class ContrattoHal(Contratto):
    objects = ContrattoHalManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto hal'
        verbose_name_plural = 'contratti hal'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'hal'
        if not self.tipologia:
            self.tipologia = 'fisso'
        nuovo_contratto = False
        if not self.id:
            nuovo_contratto = True
        super(ContrattoHal, self).save(*args, **kwargs)
        if nuovo_contratto:
            self.email_contratto_hal_inserito()

    def get_url(self):
        url = reverse('admin:contratti_contrattohal_change', args=(self.id,))
        return url

    def email_contratto_hal_inserito(self):
        context = dict(
            contratto=self,
            dettaglio=self.dettagliocontratto_set.all(),
            materiale=self.materialecontratto_set.all())
        message = render_email_message(
            'contratto/nuovo_contratto_inserito.email',
            context,
            settings.EMAIL_AMMINISTRAZIONE_ACCOUNT,
            DESTINATARI_EMAIL_NUOVO_CONTRATTO_HAL_INSERITO)
        object_type = ContentType.objects.get_for_model(self)
        alternatives = []
        for alt in message.alternatives:
            alternative = {
                'content': alt[0],
                'mimetype': alt[1]
            }
            alternatives.append(alternative)
        invia_email_task(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=message.subject, messaggio=message.body,
            destinatari=message.recipients(),
            mittente=settings.EMAIL_AMMINISTRAZIONE_ACCOUNT, alternatives=alternatives
        )


class InstallazioneHalManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneHalManager, self).get_queryset()
        return qs.filter(fornitore='hal', categoria='installazione')


class InstallazioneHal(ContrattoHal):
    objects = InstallazioneHalManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione hal'
        verbose_name_plural = 'installazioni hal'
        app_label = 'contratti'

    def __str__(self):
        return u'Installazione Linea IPKOM per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'hal'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneHal.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneHal, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'

    def get_link_riferimento(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html(u'<a href="%s" target="">Vedi</a>' % url_file)
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html(u'<a href="%s" target="">Vedi</a>' % url_file)
        return ''
    get_link_riferimento.short_description = 'Riferimento'

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html(u'<a href="%s" target="">%s</a>' % (url_file, self.offerta))
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html(u'<a href="%s" target="">%s</a>' % (url_file, self.preordine))
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
