from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse

from mastergest.contratti.models import Contratto

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class ContrattoNgiManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoNgiManager, self).get_queryset()
        return qs.filter(fornitore='ngi')


class ContrattoNgi(Contratto):
    objects = ContrattoNgiManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto NGI'
        verbose_name_plural = 'contratti NGI'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'ngi'
        if not self.tipologia:
            self.tipologia = 'fisso'
        return super(ContrattoNgi, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattongi_change', args=(self.id,))
        return url


class InstallazioneNgiManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneNgiManager, self).get_queryset()
        return qs.filter(fornitore='ngi', categoria='installazione')


class InstallazioneNgi(ContrattoNgi):
    objects = InstallazioneNgiManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione NGI'
        verbose_name_plural = 'installazioni NGI'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione NGI per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'ngi'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneNgi.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneNgi, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'
    get_totale_installazione.allow_tags = True

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'
    get_costi_installazione.allow_tags = True

    def get_link_riferimento(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return '<a href="%s" target="">Vedi</a>' % url_file
        if self.preordine:
            url_file = self.preordine.get_url()
            return '<a href="%s" target="">Vedi</a>' % url_file
        return ''
    get_link_riferimento.short_description = 'Riferimento'
    get_link_riferimento.allow_tags = True

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.offerta)
        if self.preordine:
            url_file = self.preordine.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.preordine)
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
    get_link_riferimento_lista.allow_tags = True
