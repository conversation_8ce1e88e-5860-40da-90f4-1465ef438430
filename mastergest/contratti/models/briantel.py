from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.conf import settings
from django.urls import reverse
from django.utils.html import format_html
from django.contrib.contenttypes.models import ContentType

from mastergest.utils.mail import render_email_message
from mastergest.contratti.models import Contratto, Preordine
from mastergest.utils.tasks import invia_email_task

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_NUOVO_CONTRATTO_BRIANTEL_INSERITO = [
    '<EMAIL>',
    'm.bizza<PERSON>@mastertraining.it',
    '<EMAIL>',
    '<EMAIL>',
]


class PreordineBriantelManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineBriantelManager, self).get_queryset()
        return qs.filter(tipo='briantel')


class PreordineBriantel(Preordine):
    objects = PreordineBriantelManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine briantel'
        verbose_name_plural = 'preordini briantel'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'briantel'
        return super(PreordineBriantel, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_preordinebriantel_change', args=(self.id,))
        return url


class ContrattoBriantelManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoBriantelManager, self).get_queryset()
        return qs.filter(fornitore='briantel')


class ContrattoBriantel(Contratto):
    objects = ContrattoBriantelManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto briantel'
        verbose_name_plural = 'contratti briantel'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'briantel'
        if not self.tipologia:
            self.tipologia = 'fisso'
        nuovo_contratto = False
        if not self.id:
            nuovo_contratto = True
        super(ContrattoBriantel, self).save(*args, **kwargs)
        if nuovo_contratto:
            self.email_contratto_briantel_inserito()

    def get_url(self):
        url = reverse('admin:contratti_contrattobriantel_change', args=(self.id,))
        return url

    def email_contratto_briantel_inserito(self):
        context = dict(
            contratto=self,
            dettaglio=self.dettagliocontratto_set.all(),
            materiale=self.materialecontratto_set.all()
        )
        message = render_email_message(
            'contratto/nuovo_contratto_inserito.email',
            context,
            settings.EMAIL_AMMINISTRAZIONE_ACCOUNT,
            DESTINATARI_EMAIL_NUOVO_CONTRATTO_BRIANTEL_INSERITO
        )
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=message.subject, messaggio=message.body,
            destinatari=DESTINATARI_EMAIL_NUOVO_CONTRATTO_BRIANTEL_INSERITO,
        )


class InstallazioneBriantelManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneBriantelManager, self).get_queryset()
        return qs.filter(fornitore='briantel', categoria='installazione')


class InstallazioneBriantel(ContrattoBriantel):
    objects = InstallazioneBriantelManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione briantel'
        verbose_name_plural = 'installazioni briantel'
        app_label = 'contratti'

    def __str__(self):
        return u'Installazione Briantel per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'briantel'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneBriantel.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneBriantel, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'

    def get_link_riferimento(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html(u'<a href="%s" target="">Vedi</a>' % url_file)
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html(u'<a href="%s" target="">Vedi</a>' % url_file)
        return ''
    get_link_riferimento.short_description = 'Riferimento'

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html(u'<a href="%s" target="">%s</a>' % (url_file, self.offerta))
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html(u'<a href="%s" target="">%s</a>' % (url_file, self.preordine))
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
