from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class ContrattoTrenoveManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoTrenoveManager, self).get_queryset()
        return qs.filter(fornitore='trenove')


class ContrattoTrenove(Contratto):
    objects = ContrattoTrenoveManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto Trenove'
        verbose_name_plural = 'contratti Trenove'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'trenove'
        if not self.tipologia:
            self.tipologia = 'fisso'
        return super(ContrattoTrenove, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattotrenove_change', args=(self.id,))
        return url


class InstallazioneTrenoveManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneTrenoveManager, self).get_queryset()
        return qs.filter(fornitore='trenove', categoria='installazione')


class InstallazioneTrenove(ContrattoTrenove):
    objects = InstallazioneTrenoveManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione Trenove'
        verbose_name_plural = 'installazioni Trenove'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione Trenove per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'trenove'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneTrenove.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneTrenove, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'
    get_totale_installazione.allow_tags = True

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'
    get_costi_installazione.allow_tags = True

    def genera_acquisto(self, utente):
        from mastergest.acquisti.models import Acquisto, RigaAcquisto
        codice = 'CONTR_%s' % self.id
        try:
            acquisto_esistente = Acquisto.objects.get(riferimento=codice)
            return acquisto_esistente
        except Acquisto.DoesNotExist:
            nuovo_acquisto = Acquisto.objects.create(
                richiedente=utente,
                riferimento=codice,
                settore='mastervoice',
                note='Creato in automatico da contratto ID:%s - Cliente %s' % (self.pk, self.azienda)
            )
            for materiale in self.materialecontratto_set.all():
                nuova_riga = RigaAcquisto.objects.create(
                    acquisto=nuovo_acquisto,
                    prodotto=materiale.prodotto,
                    quantita=materiale.quantita
                )
                nuova_riga.save()
            return nuovo_acquisto
