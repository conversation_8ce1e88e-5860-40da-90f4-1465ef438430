from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.contrib.contenttypes.models import ContentType

from mastergest.contratti.models import Agente
from mastergest.contratti.models import TIPO_INSTALLAZIONE, TIPO_SOPRALLUOGO
from mastergest.utils.tasks import invia_email_task

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_VERIFICA_PREORDINE = [
    '<EMAIL>',
]

TIPOLOGIA_PREORDINI = (
    ('digitel', 'Digitel'),
    ('fastweb', 'FastWeb'),
    ('mastervoice', 'Mastervoice'),
    ('lineaipkom', 'Linea IpKom'),
    ('ipkom', 'IpKom'),
    ('kpnquest', 'KPNQuest'),
    ('mastercom', 'MasterCom'),
)


class Preordine(models.Model):
    progetto = models.ForeignKey(
        'mastervoice.Progetto', null=True, blank=True, on_delete=models.SET_NULL
    )
    azienda = models.CharField(max_length=200)
    sede_cliente = models.ForeignKey(
        'anagrafe.Sede', null=True, blank=True, on_delete=models.SET_NULL,
        db_index=True
    )
    sede = models.CharField(max_length=200, null=True, blank=True)
    provincia = models.CharField(max_length=200)
    citta = models.CharField(max_length=200, null=True, blank=True)
    tipo_installazione = models.CharField(
        'tipo inst.', max_length=200, choices=TIPO_INSTALLAZIONE, null=True,
        blank=True
    )
    tipo = models.CharField(max_length=200, choices=TIPOLOGIA_PREORDINI)
    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    referente = models.CharField(max_length=200, null=True, blank=True)
    ruolo_referente = models.CharField(max_length=200, null=True, blank=True)
    telefono_referente = models.CharField(max_length=200, null=True, blank=True)
    email_referente = models.CharField(max_length=200, null=True, blank=True)
    data_firma_contratto = models.DateField('data firma contratto')
    verifica_tecnica = models.CharField(
        max_length=200, choices=TIPO_SOPRALLUOGO, null=True, blank=True
    )
    data_verifica_tecnica = models.DateField(
        'data verifica tecnica', null=True, blank=True
    )
    note = models.TextField(blank=True)
    tipo_servizio = models.CharField(max_length=200, null=True, blank=True)
    chiuso = models.BooleanField(default=False)
    offerta = models.ForeignKey(
        'offerte.Offerta', null=True, blank=True, on_delete=models.SET_NULL
    )

    class Meta:
        verbose_name_plural = 'preordini'
        ordering = ['data_firma_contratto', 'azienda']
        app_label = 'contratti'

    def __str__(self):
        return 'Preordine %s per %s sede: %s (%s)' % (self.tipo, self.azienda, self.sede or 'N/A', self.data_firma_contratto)

    def get_url(self):
        url = reverse('admin:contratti_preordine_change', args=(self.id,))
        return url

    def get_cliente(self):
        if self.sede_cliente:
            return self.sede_cliente.anagrafica

    def delete(self, *args, **kwargs):
        from mastergest.attachments.models import Attachment
        Attachment.objects.attachments_for_object(self).delete()
        return super(Preordine, self).delete(*args, **kwargs)

    def email_verifica_tecnica_fatta(self):
        cliente = self.get_cliente() or self.azienda
        titolo = '[PREORDINE] Verifica tecnica completata per Cliente %s' % cliente
        link_preordine = 'http://%s%s' % (settings.URL_GESTIONALE, self.get_url())
        messaggio = '%s\n\r%s' % (titolo, link_preordine)
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo,
            messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_VERIFICA_PREORDINE
        )

    def save(self, *args, **kwargs):
        if self.id:
            old_preordine = Preordine.objects.get(pk=self.id)
            if not old_preordine.verifica_tecnica == 'fatto' and self.verifica_tecnica == 'fatto':
                self.email_verifica_tecnica_fatta()
        super(Preordine, self).save(*args, **kwargs)
        if self.get_cliente():
            from mastergest.attachments.models import Attachment
            elenco_allegati = Attachment.objects.attachments_for_object(self)
            if elenco_allegati:
                for allegato in elenco_allegati:
                    allegato.cliente = self.get_cliente()
                    allegato.save()

    def get_link_offerta_originale(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return '<a href="%s" target="">Vedi</a>' % url_file
        else:
            return ''
    get_link_offerta_originale.short_description = 'Riferimento'
    get_link_offerta_originale.admin_order_field = 'offerta'
    get_link_offerta_originale.allow_tags = True

    def get_link_offerta_originale_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.offerta)
        else:
            return ''
    get_link_offerta_originale_lista.short_description = 'Off. Orig.'
    get_link_offerta_originale_lista.admin_order_field = 'offerta'
    get_link_offerta_originale_lista.allow_tags = True

    def get_totale_preordine(self):
        totale_preordine = DECIMAL_ZERO
        for materiale in self.materialepreordine_set.all():
            if materiale.totale_vendita:
                totale_preordine += materiale.totale_vendita
        return totale_preordine
    get_totale_preordine.short_description = 'Totale Preordine (euro)'
    get_totale_preordine.allow_tags = True

    def get_costi_preordine(self):
        costi_preordine = DECIMAL_ZERO
        for materiale in self.materialepreordine_set.all():
            if materiale.prodotto:
                costi_preordine += (materiale.prodotto.costo * materiale.quantita)
        return costi_preordine
    get_costi_preordine.short_description = 'Costi Preordine (euro)'
    get_costi_preordine.allow_tags = True

    def get_elenco_materiale_display(self):
        materiale_stringa = ''
        for materiale in self.materialepreordine_set.all():
            if not materiale_stringa == '':
                materiale_stringa += '<br>'
            materiale_stringa += '<nobr>' + materiale.__str__() + '</nobr>'
        return materiale_stringa
    get_elenco_materiale_display.short_description = 'Materiale'
    get_elenco_materiale_display.allow_tags = True

    def get_url_per_tipo(self):
        url = reverse('admin:contratti_preordine%s_change' % self.tipo, args=(self.id,))
        return url

    def get_link_display(self):
        url_file = self.get_url_per_tipo()
        return '<a href="%s" target="">%s</a>' % (url_file, self.azienda)
    get_link_display.short_description = 'Preordine'
    get_link_display.admin_order_field = 'id'
    get_link_display.allow_tags = True

    def get_elenco_link_allegati(self):
        from mastergest.attachments.models import Attachment
        elenco_allegati = Attachment.objects.attachments_for_object(self).all()
        stringa_elenco = ''
        if elenco_allegati:
            for allegato in elenco_allegati:
                if not stringa_elenco == '':
                    stringa_elenco += '<br>'
                stringa_elenco += '<nobr>' + allegato.get_link() + '</nobr>'
        return stringa_elenco
    get_elenco_link_allegati.short_description = 'Allegati'
    get_elenco_link_allegati.allow_tags = True


class MaterialePreordine(models.Model):
    preordine = models.ForeignKey(Preordine, on_delete=models.CASCADE)
    prodotto = models.ForeignKey('listino.Prodotto', limit_choices_to=dict(
        a_listino=True, attivo=True), on_delete=models.CASCADE
    )
    quantita = models.PositiveIntegerField(default=1)
    prezzo_listino = models.DecimalField(
        'prezzo listino (euro)', max_digits=9, decimal_places=2,
        null=True, blank=True
    )
    prezzo_vendita = models.DecimalField(
        'prezzo vendita (euro)', max_digits=9, decimal_places=2, null=True,
        blank=True
    )
    totale_vendita = models.DecimalField(
        'totale (euro)', max_digits=9, decimal_places=2, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = 'materiale preordine'
        ordering = ('preordine', 'id')
        app_label = 'contratti'

    def __str__(self):
        return '%sx %s' % (self.quantita, self.prodotto)

    def save(self, *args, **kwargs):
        self.costo = self.prodotto.costo
        self.prezzo_listino = self.prodotto.get_prezzo_listino()
        if self.pk:
            old_materiale = MaterialePreordine.objects.get(pk=self.pk)
            if not old_materiale.prodotto == self.prodotto:
                self.prezzo_vendita = (self.prodotto.get_prezzo_listino()).quantize(DECIMAL_ZERO)
        if not self.prezzo_vendita:
            self.prezzo_vendita = (self.prodotto.get_prezzo_listino()).quantize(DECIMAL_ZERO)
        if self.prezzo_vendita:
            self.totale_vendita = (self.prezzo_vendita * self.quantita).quantize(DECIMAL_ZERO)
        return super(MaterialePreordine, self).save(*args, **kwargs)

    def get_totale_riga(self):
        if self.prezzo_vendita and self.quantita:
            return (self.prezzo_vendita * self.quantita).quantize(DECIMAL_ZERO)
        else:
            return DECIMAL_ZERO


class PreordineAgentiManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineAgentiManager, self).get_queryset()
        return qs.filter(tipo__in=('fastweb', 'mastervoice'), chiuso=False)


class PreordineAgenti(Preordine):
    objects = PreordineAgentiManager()

    class Meta:
        proxy = True
        verbose_name = 'situazione preordine'
        verbose_name_plural = 'situazione preordini'
        app_label = 'contratti'

    def get_url(self):
        url = reverse('admin:contratti_preordineagenti_change', args=(self.id,))
        return url
