from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.db import models
from django.urls import reverse
from django.conf import settings
from django.contrib.contenttypes.models import ContentType

from mastergest.utils.mail import render_email_message
from mastergest.contratti.models import Contratto
from mastergest.utils.tasks import invia_email_task

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_NUOVO_CONTRATTO_FWR_INSERITO = [
    '<EMAIL>',
    'm.bizza<PERSON>@mastertraining.it',
    '<EMAIL>',
    '<EMAIL>',
]


class ContrattoFastwebResellerManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoFastwebResellerManager, self).get_queryset()
        return qs.filter(fornitore='fastweb_reseller')


class ContrattoFastwebReseller(Contratto):
    objects = ContrattoFastwebResellerManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto fastweb reseller'
        verbose_name_plural = 'contratti fastweb reseller'
        app_label = 'contratti'

    def __str__(self):
        return 'Contr.FW Reseller n.%s del %s - %s' % (self.pk, self.data_consegna, self.azienda)

    def save(self, *args, **kwargs):
        self.fornitore = 'fastweb_reseller'
        if not self.tipologia:
            self.tipologia = 'fisso'
        nuovo_contratto = False
        if not self.id:
            nuovo_contratto = True
        if self.pk:
            vecchio_contratto = ContrattoFastwebReseller.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchio_contratto.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        super(ContrattoFastwebReseller, self).save(*args, **kwargs)
        if nuovo_contratto:
            self.email_contratto_fwr_inserito()

    def email_contratto_fwr_inserito(self):
        context = dict(
            contratto=self,
            dettaglio=self.dettagliocontratto_set.all(),
            materiale=self.materialecontratto_set.all())
        message = render_email_message(
            'contratto/nuovo_contratto_inserito.email',
            context,
            settings.EMAIL_AMMINISTRAZIONE_ACCOUNT,
            DESTINATARI_EMAIL_NUOVO_CONTRATTO_FWR_INSERITO)
        object_type = ContentType.objects.get_for_model(self)
        alternatives = []
        for alt in message.alternatives:
            alternative = {
                'content': alt[0],
                'mimetype': alt[1]
            }
            alternatives.append(alternative)
        invia_email_task(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=message.subject, messaggio=message.body,
            destinatari=message.recipients(),
            mittente=settings.EMAIL_AMMINISTRAZIONE_ACCOUNT, alternatives=alternatives
        )

    def get_url(self):
        url = reverse('admin:contratti_contrattofastwebreseller_change', args=(self.id,))
        return url


class InstallazioneFastwebReseller(ContrattoFastwebReseller):

    class Meta:
        proxy = True
        verbose_name = 'installazione fastweb reseller'
        verbose_name_plural = 'installazioni fastweb reseller'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione FW Reseller per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'fastweb_reseller'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneFastwebReseller.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneFastwebReseller, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'
    get_totale_installazione.allow_tags = True

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.offerta)
        if self.preordine:
            url_file = self.preordine.get_url()
            return '<a href="%s" target="">%s</a>' % (url_file, self.preordine)
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
    get_link_riferimento_lista.allow_tags = True

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'
    get_costi_installazione.allow_tags = True

    def genera_acquisto(self, utente):
        from mastergest.acquisti.models import Acquisto, RigaAcquisto
        codice = 'CONTR_%s' % self.id
        try:
            acquisto_esistente = Acquisto.objects.get(riferimento=codice)
            return acquisto_esistente
        except Acquisto.DoesNotExist:
            nuovo_acquisto = Acquisto.objects.create(
                richiedente=utente,
                riferimento=codice,
                settore='mastervoice',
                note='Creato in automatico da contratto ID:%s - Cliente %s' % (self.pk, self.azienda)
            )
            for materiale in self.materialecontratto_set.all():
                nuova_riga = RigaAcquisto.objects.create(
                    acquisto=nuovo_acquisto,
                    prodotto=materiale.prodotto,
                    quantita=materiale.quantita
                )
                nuova_riga.save()
            return nuovo_acquisto
