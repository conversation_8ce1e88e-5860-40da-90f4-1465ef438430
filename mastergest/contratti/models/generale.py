from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

TIPO_INSTALLAZIONE = (
    ('mv', 'Mastervoice Open'),
    ('mv_pac', 'Mastervoice PaC'),
    ('mv_assistente_virtuale', 'Mastervoice Assistente Virtuale'),
    ('upgrade_mv', 'upgrade Mastervoice'),
    ('accessori', 'accessori'),
    ('', '---------------------------'),
    ('mg', 'Mastergate (NON USARE)'),
    ('ipkom', 'numeri IPKOM (NON USARE)'),
    ('kpnquest', 'KPN (NON USARE)'),
)

TIPO_SOPRALLUOGO = (
    ('si', 'si'),
    ('no', 'no'),
    ('fatto', 'fatto'),
)

TIPI_AGENTI = (
    ('struttura', 'struttura'),
    ('capo_area', 'capo area'),
    ('base', 'base'),
)

SOTTOTIPI_AGENTI = (
    ('distributore', 'distributore'),
    ('agente', 'agente'),
    ('procacciatore', 'procacciatore'),
    ('consulente', 'consulente'),
    ('collaboratore', 'collaboratore'),
    ('altro', 'altro'),
)

STATO_DOCUMENTI_AGENTE = (
    ('manca', 'manca'),
    ('manca_firma', 'manca firma'),
    ('ok', 'ok'),
    ('disdetto', 'disdetto'),
    ('da_correggere', 'da correggere'),
)

TIPOLOGIA_CONTRATTI = (
    ('fisso', 'fisso'),
    ('mobile', 'mobile'),
)

SUB_TIPOLOGIA_CONTRATTI = (
    ('voce', 'voce'),
    ('dati', 'dati'),
    ('opzione', 'opzione'),
)

TIPI_FORNITORI_CONTRATTI = (
    ('acanto', 'Acantho'),
    ('briantel', 'Briantel'),
    ('digitel', 'Digitel'),
    ('fastweb', 'FastWeb'),
    ('fastweb_reseller', 'FastWeb Reseller'),
    ('3italia', 'H3G'),
    ('kpnquest', 'KPNQuest'),
    ('lineaipkom', 'Linea Ipkom'),
    ('hal', 'Hal'),
    ('ipkom', 'Ipkom'),
    ('mastercom', 'Mastercom'),
    ('mastervoice', 'Mastervoice'),
    ('magister', 'Magister'),
    ('mclink', 'MC Link'),
    ('ngi', 'NGI'),
    ('netandwork', 'Net And work'),
    ('onesim', 'OneSim'),
    ('tim', 'Tim'),
    ('trenove', 'Trenove'),
    ('twt', 'Twt'),
    ('vodafone', 'Vodafone'),
    ('welcome', 'Welcome'),
    ('wind', 'Wind'),
)

TIPI_COMPENSO = (
    ('provvigione', 'provvigione'),
    ('storno', 'storno'),
    ('bonifico', 'bonifico'),
    ('rata', 'rata'),
    ('gara', 'gara'),
)


class Agente(models.Model):
    cognome = models.CharField(max_length=200)
    nome = models.CharField(max_length=200)
    mastertraining = models.BooleanField(default=False)
    telefono = models.CharField(max_length=200, null=True, blank=True)
    email = models.CharField(max_length=200, null=True, blank=True)
    tipo = models.CharField(
        max_length=200, choices=TIPI_AGENTI, null=True, blank=True
    )
    sottotipo = models.CharField(
        max_length=200, choices=SOTTOTIPI_AGENTI, null=True, blank=True
    )
    stato_documenti = models.CharField(
        max_length=200, choices=STATO_DOCUMENTI_AGENTE, null=True, blank=True
    )
    attivo = models.BooleanField(default=True)
    capo_area = models.ForeignKey(
        'self', null=True, blank=True, on_delete=models.SET_NULL,
        related_name='capo_area_pk'
    )

    class Meta:
        verbose_name_plural = 'agenti'
        ordering = ['cognome', 'nome']
        app_label = 'contratti'

    def __str__(self):
        return '%s %s' % (self.cognome, self.nome)

    def get_elenco_subagenti(self):
        elenco_subagenti = Agente.objects.filter(capo_area=self).order_by('cognome', 'nome')
        return elenco_subagenti

    def get_elenco_utenti_subagenti(self, incluso=False):
        from mastergest.anagrafe.models import ProfiloUtente
        elenco_utenti = []
        elenco_subagenti = self.get_elenco_subagenti()
        if elenco_subagenti:
            for subagente in elenco_subagenti:
                try:
                    subutente = ProfiloUtente.objects.get(agente=subagente)
                    elenco_utenti.append(subutente.user)
                except:
                    pass
            if incluso:
                elenco_utenti.append(ProfiloUtente.objects.get(agente=self).user)
        return elenco_utenti


class TipoGara(models.Model):
    descrizione = models.CharField(max_length=200)

    class Meta:
        verbose_name_plural = 'tipi gare'
        app_label = 'contratti'

    def __str__(self):
        return '%s' % (self.descrizione)


class TipoContratto(models.Model):
    codice = models.CharField(max_length=200)
    descrizione = models.CharField(max_length=200)
    fornitore = models.CharField(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    tipologia = models.CharField(max_length=200, choices=TIPOLOGIA_CONTRATTI)
    sottotipo = models.CharField(max_length=200, choices=SUB_TIPOLOGIA_CONTRATTI, null=True, blank=True)
    attivo = models.BooleanField(default=True)
    tipo_gara = models.ForeignKey(
        TipoGara, null=True, blank=True, on_delete=models.PROTECT
    )
    costo = models.DecimalField(
        'costo al bimestre (euro)', max_digits=9, decimal_places=2, null=True, blank=True
    )
    tcg = models.DecimalField(
        'TCG', max_digits=9, decimal_places=2, default=Decimal('25.82')
    )
    per_analisi = models.BooleanField(default=True)
    profilo_standard = models.BooleanField(default=False)
    soglia_minuti_inferiore = models.PositiveIntegerField(default=0)
    soglia_minuti_superiore = models.PositiveIntegerField(default=0)
    profilo_tariffario = models.ForeignKey(
        'offerte.ProfiloTariffario', null=True, blank=True, on_delete=models.SET_NULL,
    )

    class Meta:
        verbose_name_plural = 'tipi contratti'
        ordering = ['fornitore', 'tipologia', 'codice', 'descrizione']
        app_label = 'contratti'

    def __str__(self):
        return '%s %s - %s' % (self.fornitore, self.tipologia, self.descrizione)


class TipoContrattoMastervoiceManager(models.Manager):

    def get_queryset(self):
        qs = super(TipoContrattoMastervoiceManager, self).get_queryset()
        return qs.filter(fornitore='mastervoice')


class TipoContrattoMastervoice(TipoContratto):
    objects = TipoContrattoMastervoiceManager()

    class Meta:
        proxy = True
        ordering = ('descrizione', )
        verbose_name_plural = 'tipi contratto mastervoice'
        app_label = 'contratti'

    def __str__(self):
        return '%s' % self.descrizione

    def save(self, *args, **kwargs):
        self.fornitore = 'mastervoice'
        return super(TipoContrattoMastervoice, self).save(*args, **kwargs)


class OpzioneTelefoniaManager(models.Manager):

    def get_queryset(self):
        qs = super(OpzioneTelefoniaManager, self).get_queryset()
        return qs.filter(per_analisi=True)


class OpzioneTelefonia(TipoContratto):
    objects = OpzioneTelefoniaManager()

    class Meta:
        proxy = True
        verbose_name = 'opzione telefonia'
        verbose_name_plural = 'opzioni telefonia'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.per_analisi = True
        return super(OpzioneTelefonia, self).save(*args, **kwargs)

    def __str__(self):
        return '%s - %s' % (self.fornitore, self.descrizione)


class ProfiloTelefoniaManager(models.Manager):

    def get_queryset(self):
        qs = super(ProfiloTelefoniaManager, self).get_queryset()
        return qs.filter(profilo_standard=True)


class ProfiloTelefonia(TipoContratto):
    objects = ProfiloTelefoniaManager()

    class Meta:
        proxy = True
        verbose_name = 'profilo standard'
        verbose_name_plural = 'profili standard'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.profilo_standard = True
        return super(ProfiloTelefonia, self).save(*args, **kwargs)

    def __str__(self):
        return '%s - %s' % (self.fornitore, self.descrizione)
