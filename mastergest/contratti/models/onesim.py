from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class ContrattoOnesimManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoOnesimManager, self).get_queryset()
        return qs.filter(fornitore='onesim')


class ContrattoOnesim(Contratto):
    objects = ContrattoOnesimManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto onesim'
        verbose_name_plural = 'contratti onesim'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'onesim'
        if not self.tipologia:
            self.tipologia = 'fisso'
        return super(ContrattoOnesim, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattoonesim_change', args=(self.id,))
        return url
