from datetime import date
from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.conf import settings
from django.utils.html import format_html
from django.contrib.contenttypes.models import ContentType

from mastergest.contratti.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>rat<PERSON>
from mastergest.contratti.models import SchemaProvvigioneAgente
from mastergest.contratti.models import DettaglioValoreSchemaAgente
from mastergest.contratti.models import DettaglioSchemaProvvigioneAgente
from mastergest.contratti.models import TIPI_FORNITORI_CONTRATTI
from mastergest.contratti.models import TIPOLOGIA_CONTRATTI, TIPO_INSTALLAZIONE
from mastergest.contratti.models import TIPO_SOPRALLUOGO, TIPI_COMPENSO
from mastergest.offerte.models import TIPI_OFFERTE_MASTERCOM
from mastergest.utils.mail import render_email_message
from mastergest.utils.web import get_valuta_display
from mastergest.utils.tasks import invia_email_task
from mastergest.canoni.constants import TIPI_RINNOVO, PERIODICITA
from mastergest.anagrafe.models import GestioneAziendaAware


getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_GENERA_CONTRATTO_PREORDINE = [
    '<EMAIL>',
    '<EMAIL>',
]

DESTINATARI_EMAIL_COLLAUDO_INSTALLAZIONE = [
    '<EMAIL>',
    '<EMAIL>',
]

DESTINATARI_EMAIL_NUOVO_CONTRATTO_INSERITO = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
]

TIPI_MATERIALE_MASTERCOM = (
    ('server', 'server MAstercom'),
    ('tablet', 'tablet'),
    ('firewall', 'Firewall'),
    ('totem', 'Mastervoice'),
    ('elms', 'elms'),
    ('clinic_point', 'Clinic Point'),
    ('access_point', 'Access Point'),
    ('rete', 'Rete'),
    ('elettrico', 'Elettrico'),
    ('accessori', 'accessori'),
)

TIPI_DATA_ATTIVAZIONE = (
    ('presunta', 'presunta'),
    ('comunicata', 'comunicata'),
    ('reale', 'reale'),
)

STATI_FASTWEB = (
    ('aperto', 'aperto'),
    ('chiuso', 'chiuso'),
)

TIPI_TECNOLOGIA_ADSL = (
    ('ADSL_ULL', 'ADSL_ULL'),
    ('ADSL_WHS', 'ADSL_WHS'),
    ('CVP_HDSL', 'CVP_HDSL'),
    ('FIBRA', 'FIBRA'),
    ('SHDSL', 'SHDSL'),
)

FORME_CONTRATTI = (
    ('vendita', 'Vendita'),
    ('open', 'Servizio a Canone'),
    ('', '-------------------------'),
    ('comodato', "Comodato d'uso (NON USARE)"),
    ('prova', 'Comodato con prova (NON USARE)'),
    ('rent', 'Rent Strumentale (NON USARE)'),
    ('opentop', 'Opentop (NON USARE)'),
)

STATI_INSTALLAZIONE = (
    ('attesa_linea', 'in attesa linea'),
    ('attesa_sviluppo', 'in attesa sviluppo software'),
    ('da_installare', 'da installare'),
    ('da_installare_remoto', 'da installare (remoto)'),
    ('da_installare_loco', 'da installare (in loco)'),
    ('da_spedire', 'da spedire'),
    ('spedito', 'spedito'),
    ('test', 'in test'),
    ('bloccato', 'bloccato (vedi note)'),
    ('installato', 'installato'),
    ('collaudato', 'collaudato'),
)

CATEGORIA_CONTRATTO = (
    ('installazione', 'installazione'),
    ('canone', 'canone'),
)


class StatoContratto(models.Model):
    descrizione = models.CharField(max_length=200)
    ignora_provvigioni_relative = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = 'stati contratto'
        ordering = ['descrizione']
        app_label = 'contratti'

    def __str__(self):
        return '%s' % self.descrizione


class MotivazioneStatoContratto(models.Model):
    descrizione = models.CharField(max_length=200)

    class Meta:
        verbose_name_plural = 'motivazioni stato contratto'
        ordering = ['descrizione']
        app_label = 'contratti'

    def __str__(self):
        return '%s' % self.descrizione


class Contratto(GestioneAziendaAware):
    progetto = models.ForeignKey(
        'mastervoice.Progetto', null=True, blank=True, on_delete=models.SET_NULL
    )
    contratto_master = models.ForeignKey(
        'self', null=True, blank=True, on_delete=models.CASCADE
    )
    azienda = models.CharField(max_length=200)
    agente = models.ForeignKey(Agente, on_delete=models.PROTECT)
    fornitore = models.CharField(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    tipologia = models.CharField(max_length=200, choices=TIPOLOGIA_CONTRATTI, null=True, blank=True)
    account = models.CharField(max_length=200, null=True, blank=True)
    tecnologia = models.CharField(
        max_length=200, null=True, blank=True, choices=TIPI_TECNOLOGIA_ADSL)
    inserita_pda = models.BooleanField(default=False)
    data_inserimento_pda = models.DateField('inserimento PDA', null=True, blank=True)
    data_stipula = models.DateField('stipula', null=True, blank=True)
    data_consegna = models.DateField('competenza', default=date.today)
    data_attivazione = models.DateField('attivazione', null=True, blank=True)
    fatturato = models.BooleanField(default=False)
    data_fatturazione = models.DateField('fatturazione', null=True, blank=True)
    pagato = models.BooleanField(default=False)
    note = models.TextField(blank=True)
    stato = models.ForeignKey(
        StatoContratto, null=True, blank=True, on_delete=models.PROTECT
    )
    motivazione_stato = models.ForeignKey(
        MotivazioneStatoContratto, null=True, blank=True, on_delete=models.PROTECT
    )
    note_stato = models.TextField(blank=True)
    # Provvigioni Agente
    gettone_totale_agente = models.DecimalField(
        'gettone tot.ag.', max_digits=9, decimal_places=2, default=0)
    premio_agente = models.DecimalField(
        'premio ag.', max_digits=9, decimal_places=2, default=0)
    totale_agente = models.DecimalField(
        'tot. ag.', max_digits=9, decimal_places=2, default=0)
    # Provvigioni Master
    gettone_totale_mastertraining = models.DecimalField(
        'gettone MT', max_digits=9, decimal_places=2, default=0)
    gara_accelarazione = models.DecimalField(
        'gara acc.', max_digits=9, decimal_places=2, default=0)
    gara_valore = models.DecimalField(
        'gara val.', max_digits=9, decimal_places=2, default=0)
    gettone_quantita = models.DecimalField(
        'gara qta', max_digits=9, decimal_places=2, default=0)
    totale_mastertraining = models.DecimalField(
        'tot. MT', max_digits=9, decimal_places=2, default=0)
    totale_prodotti = models.PositiveIntegerField(default=0)
    numero_storni = models.PositiveIntegerField(default=0)
    importo_rata_mensile = models.DecimalField(
        'imp. rata mensile', max_digits=9, decimal_places=2,
        default=0)
    importo_rata_mensile_agente = models.DecimalField(
        'imp. rata mensile agente', max_digits=9, decimal_places=2, default=0)
    premio_rata_mensile_agente = models.DecimalField(
        'premio rata mensile ag.', max_digits=9, decimal_places=2, default=0)
    data_inizio_rate = models.DateField(
        'data collaudo/inst.', null=True, blank=True)
    costo_materiale = models.DecimalField(
        'costo materiale', max_digits=9, decimal_places=2, default=0)
    numero_rate = models.PositiveIntegerField(default=0)
    valore_contratto = models.DecimalField(
        'valore contratto', max_digits=9, decimal_places=2,
        default=DECIMAL_ZERO, null=True)
    numero_mesi_valore_contratto = models.PositiveIntegerField('Num. mesi valore contratto', default=0)
    gettone_valore_contratto = models.DecimalField(
        'gettone valore contratto agente', max_digits=9, decimal_places=2,
        default=0)
    gettone_valore_contratto_master = models.DecimalField(
        'gettone valore contratto master', max_digits=9, decimal_places=2,
        default=0)
    provincia = models.CharField('Prov.', max_length=50, null=True, blank=True)
    numero_contratti_fastweb = models.PositiveIntegerField('N. Contr. FW', default=0)
    data_chiusura_fastweb = models.DateField('Data Chiusura FW', null=True, blank=True)
    documentazione = models.BooleanField('doc. completa', default=True)
    note_documentazione = models.TextField(blank=True)
    forma_contratto = models.CharField('forma contr.', max_length=200, choices=FORME_CONTRATTI, null=True, blank=True)
    tipo_installazione = models.CharField('tipo inst.', max_length=200, choices=TIPO_INSTALLAZIONE, null=True, blank=True)
    sopralluogo = models.CharField(max_length=200, choices=TIPO_SOPRALLUOGO, null=True, blank=True)
    data_sopralluogo = models.DateField('data sopralluogo', null=True, blank=True)
    data_consegna_contratto = models.DateField('consegna', default=date.today)
    data_fine_prova = models.DateField('fine prova', null=True, blank=True)
    stato_installazione = models.CharField('stato inst.', max_length=200, choices=STATI_INSTALLAZIONE, null=True, blank=True)
    referente = models.CharField(max_length=200, null=True, blank=True)
    ruolo_referente = models.CharField(max_length=200, null=True, blank=True)
    telefono_referente = models.CharField(max_length=200, null=True, blank=True)
    email_referente = models.CharField(max_length=200, null=True, blank=True)
    project_manager = models.CharField(max_length=200, null=True, blank=True)
    offerta = models.ForeignKey(
        'offerte.Offerta', null=True, blank=True, on_delete=models.SET_NULL)
    preordine = models.ForeignKey(
        Preordine, null=True, blank=True, on_delete=models.SET_NULL)
    sede = models.ForeignKey(
        'anagrafe.Sede', null=True, blank=True, on_delete=models.SET_NULL,
        db_index=True)
    # Dati per Installazione Fastweb
    tipo_data_attivazione = models.CharField(max_length=200, choices=TIPI_DATA_ATTIVAZIONE, null=True, blank=True)
    allegato_contratto_mastervoice = models.BooleanField('All. Contr. MV', default=False)
    data_portabilita = models.DateField(null=True, blank=True)
    tipo_data_portabilita = models.CharField(max_length=200, choices=TIPI_DATA_ATTIVAZIONE, null=True, blank=True)
    chi_deve_fare = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.PROTECT
    )
    cosa_deve_fare = models.TextField(blank=True)
    stato_fastweb = models.CharField('stato FW', max_length=200, choices=STATI_FASTWEB, null=True, blank=True)
    # Dati per contratto IPKOM
    data_firma_contratto = models.DateField(null=True, blank=True)
    tipo_servizio = models.CharField(max_length=200, null=True, blank=True)
    verifica_tecnica = models.CharField(max_length=200, choices=TIPO_SOPRALLUOGO, null=True, blank=True)
    data_verifica_tecnica = models.DateField('data verifica tecnica', null=True, blank=True)
    categoria = models.CharField(max_length=200, choices=CATEGORIA_CONTRATTO, null=True, blank=True, default='installazione')
    tipo_contratto_mastercom = models.CharField(
        'tipo contratto', max_length=200, choices=TIPI_OFFERTE_MASTERCOM,
        null=True, blank=True)
    # DATI PRECOMPILAZIONE CANONI
    numero_rate_canone = models.PositiveIntegerField('num. rate', default=1)
    tipo_rinnovo_canone = models.CharField(max_length=200, choices=TIPI_RINNOVO, default='manuale')
    periodicita_canone = models.CharField(max_length=200, choices=PERIODICITA, default=360)
    tipologia_canone = models.ForeignKey(
        'canoni.Tipologia', default=1, on_delete=models.PROTECT
    )
    categoria_canone = models.ForeignKey(
        'canoni.Categoria', null=True, blank=True, on_delete=models.PROTECT
    )
    descrizione_aggiuntiva_fattura_canone = models.CharField(
        'descr. agg. fattura', max_length=200, null=True, blank=True
    )
    anticipato_canone = models.BooleanField('anticipato', default=False)
    con_ratino_canone = models.BooleanField('con ratino', default=False)
    importo_rata_canone = models.DecimalField(
        'importo rata', max_digits=9, decimal_places=2, default=0
    )
    numero_rate_rinnovo_canone = models.PositiveIntegerField('rate rinnovo', default=1)
    ha_canoni_attivi = models.BooleanField(default=False)
    cessato = models.BooleanField(default=False)
    data_cessazione = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name_plural = 'contratti'
        ordering = ['-data_consegna', ]
        app_label = 'contratti'

    def __str__(self):
        return 'Contr. %s n.%s del %s - %s' % (self.fornitore, self.pk, self.data_consegna, self.azienda)

    def calcola_provvigione_valore(self, agente=None):
        if self.data_inizio_rate and agente:
            valore_fascia = self.numero_mesi_valore_contratto * self.valore_contratto
            data_inizio_rate = self.data_inizio_rate
            fornitore = self.fornitore
            tipologia = self.tipologia
            provvigioni_agente = SchemaProvvigioneAgente.objects.filter(agente=agente)
            schemi_provvigioni_corrente = provvigioni_agente.filter(
                data_inizio_validita__lte=data_inizio_rate,
                data_fine_validita__gte=data_inizio_rate,
                fornitore=fornitore,
                tipologia=tipologia)
            if schemi_provvigioni_corrente.count() == 1:
                try:
                    fascia_provvigione = schemi_provvigioni_corrente[0].dettagliovaloreschemaagente_set.get(
                        da_quantita__lte=valore_fascia,
                        a_quantita__gte=valore_fascia)
                    return fascia_provvigione.provvigione
                except DettaglioValoreSchemaAgente.DoesNotExist:
                    pass
        return Decimal('0.00')

    def calcola_percentuale_rate_agente(self):
        if self.data_inizio_rate and self.agente:
            data_inizio_rate = self.data_inizio_rate
            fornitore = self.fornitore
            tipologia = self.tipologia
            provvigioni_agente = SchemaProvvigioneAgente.objects.filter(agente=self.agente)
            schemi_provvigioni_corrente = provvigioni_agente.filter(
                data_inizio_validita__lte=data_inizio_rate,
                data_fine_validita__gte=data_inizio_rate,
                fornitore=fornitore,
                tipologia=tipologia)
            if schemi_provvigioni_corrente.count() == 1:
                return schemi_provvigioni_corrente[0].percentuale_provvigione_rate
        return 0

    def get_numero_storni(self):
        numero_storni = 0
        if self.dettagliocontratto_set.all():
            for dettaglio in self.dettagliocontratto_set.all():
                if not dettaglio.storno_agente == Decimal('0.00'):
                    numero_storni += 1
        return numero_storni

    def get_riepilogo_prodotti_gare(self):
        riepilogo = dict()
        if self.dettagliocontratto_set.all():
            for dettaglio in self.dettagliocontratto_set.all():
                if dettaglio.tipo_contratto.tipo_gara:
                    if dettaglio.tipo_contratto.tipo_gara.descrizione in riepilogo:
                        riepilogo[dettaglio.tipo_contratto.tipo_gara.descrizione] += dettaglio.quantita
                    else:
                        riepilogo[dettaglio.tipo_contratto.tipo_gara.descrizione] = dettaglio.quantita
        riepilogo_stringa = ' '.join(['%s: %s' % (key, value) for (key, value) in list(riepilogo.items())])
        return riepilogo_stringa
    get_riepilogo_prodotti_gare.short_description = 'tot. gare'

    def get_numero_canoni_attivi(self):
        canoni_attivi = 0
        elenco_canoni = self.canone_set.all()
        if elenco_canoni:
            for canone in elenco_canoni:
                if not canone.stato:
                    canoni_attivi += 1
        return canoni_attivi

    def get_totale_valore_contratto(self):
        if self.numero_mesi_valore_contratto and self.valore_contratto:
            return self.numero_mesi_valore_contratto * self.valore_contratto
        return Decimal('0.00')

    def aggiorna_totali(self):
        gettone_totale_mastertraining = Decimal('0.00')
        gettone_totale_agente = Decimal('0.00')
        totale_prodotti = 0
        for dettaglio in self.dettagliocontratto_set.all():
            gettone_totale_mastertraining += dettaglio.get_gettone_totale_mastertraining()
            gettone_totale_agente += dettaglio.get_gettone_totale_agente()
            if self.fornitore == 'mastervoice':
                totale_prodotti += 1
            else:
                totale_prodotti += dettaglio.quantita
        self.gettone_totale_agente = gettone_totale_agente
        self.gettone_totale_mastertraining = gettone_totale_mastertraining
        self.totale_mastertraining = (
            gettone_totale_mastertraining +
            self.gettone_quantita +
            self.gara_accelarazione +
            self.gara_valore +
            self.gettone_valore_contratto_master -
            self.costo_materiale
        )
        self.totale_agente = gettone_totale_agente + self.premio_agente + self.gettone_valore_contratto
        self.numero_storni = self.get_numero_storni()
        self.totale_prodotti = totale_prodotti

    def delete(self, *args, **kwargs):
        Contratto.objects.filter(contratto_master=self).delete()
        from mastergest.attachments.models import Attachment
        Attachment.objects.attachments_for_object(self).delete()
        return super(Contratto, self).delete(*args, **kwargs)

    def save(self, *args, **kwargs):
        vecchio_contratto = None
        if self.valore_contratto and self.numero_mesi_valore_contratto:
            agente_master = Agente.objects.get(mastertraining=True)
            self.gettone_valore_contratto = self.calcola_provvigione_valore(self.agente)
            self.gettone_valore_contratto_master = self.calcola_provvigione_valore(agente_master)
        if self.importo_rata_mensile:
            percentuale_agente = self.calcola_percentuale_rate_agente()
            self.importo_rata_mensile_agente = (percentuale_agente * (self.importo_rata_mensile / 100)).quantize(Decimal('0'))
        if self.id:
            vecchio_contratto = Contratto.objects.get(id=self.id)
        if self.data_inserimento_pda:
            self.inserita_pda = True
        else:
            self.inserita_pda = False
        if self.data_fatturazione:
            self.fatturato = True
        else:
            self.fatturato = False
        self.aggiorna_totali()
        super(Contratto, self).save(*args, **kwargs)
        if vecchio_contratto:
            if vecchio_contratto.agente == self.agente and vecchio_contratto.data_inizio_rate == self.data_inizio_rate:
                pass
            else:
                for dettaglio in self.dettagliocontratto_set.all():
                    dettaglio.gettone_agente = None
                    dettaglio.gettone_mastertraining = None
                    dettaglio.save(aggiorna_contratto=False)
                self.aggiorna_totali()
                super(Contratto, self).save(*args, **kwargs)

    def email_contratto_inserito(self):
        context = dict(
            contratto=self,
            dettaglio=self.dettagliocontratto_set.all(),
            materiale=self.materialecontratto_set.all())
        message = render_email_message(
            'contratto/nuovo_contratto_inserito.email',
            context,
            settings.EMAIL_AMMINISTRAZIONE_ACCOUNT,
            DESTINATARI_EMAIL_NUOVO_CONTRATTO_INSERITO)
        object_type = ContentType.objects.get_for_model(self)
        alternatives = []
        for alt in message.alternatives:
            alternative = {
                'content': alt[0],
                'mimetype': alt[1]
            }
            alternatives.append(alternative)
        invia_email_task(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=message.subject, messaggio=message.body,
            destinatari=message.recipients(),
            mittente=settings.EMAIL_AMMINISTRAZIONE_ACCOUNT, alternatives=alternatives
        )

    def get_percentuale_liquidabile_agente(self):
        if self.data_inizio_rate:
            agente = self.agente
            data_inizio_rate = self.data_inizio_rate
            fornitore = self.fornitore
            tipologia = self.tipologia
            provvigioni_agente = SchemaProvvigioneAgente.objects.filter(agente=agente)
            schemi_provvigioni_corrente = provvigioni_agente.filter(
                data_inizio_validita__lte=data_inizio_rate,
                data_fine_validita__gte=data_inizio_rate,
                fornitore=fornitore, tipologia=tipologia)
            if schemi_provvigioni_corrente.count() == 1:
                dettaglio_provvigione = schemi_provvigioni_corrente[0]
                return dettaglio_provvigione.percentuale_liquidazione
        return 0

    def get_schema_provvigione_agente(self):
        if self.data_inizio_rate:
            agente = self.agente
            data_inizio_rate = self.data_inizio_rate
            fornitore = self.fornitore
            tipologia = self.tipologia
            provvigioni_agente = SchemaProvvigioneAgente.objects.filter(agente=agente)
            schemi_provvigioni_corrente = provvigioni_agente.filter(
                data_inizio_validita__lte=data_inizio_rate,
                data_fine_validita__gte=data_inizio_rate,
                fornitore=fornitore, tipologia=tipologia)
            if schemi_provvigioni_corrente.count() == 1:
                dettaglio_provvigione = schemi_provvigioni_corrente[0]
                return dettaglio_provvigione

    def _get_totale_rate_master(self):
        if self.numero_rate and self.importo_rata_mensile:
            return self.numero_rate * self.importo_rata_mensile
        else:
            return Decimal('0.00')
    totale_rate_master = property(_get_totale_rate_master, doc='importo totale rate (MT)')

    def _get_totale_rate_agente(self):
        if self.numero_rate and self.importo_rata_mensile_agente:
            rata_mensile_agente = self.importo_rata_mensile_agente
            if self.premio_rata_mensile_agente:
                rata_mensile_agente += self.premio_rata_mensile_agente
            return self.numero_rate * rata_mensile_agente
        else:
            return Decimal('0.00')
    totale_rate_agente = property(_get_totale_rate_agente, doc='importo totale rate (Agente)')

    def get_totale_mastertraining_display(self):
        return get_valuta_display(self.totale_mastertraining)
    get_totale_mastertraining_display.short_description = 'tot. MT'
    get_totale_mastertraining_display.admin_order_field = 'totale_mastertraining'

    def get_totale_agente_display(self):
        return get_valuta_display(self.totale_agente)
    get_totale_agente_display.short_description = 'tot. ag.'
    get_totale_agente_display.admin_order_field = 'totale_agente'

    def get_gettone_totale_mastertraining_display(self):
        return get_valuta_display(self.gettone_totale_mastertraining)
    get_gettone_totale_mastertraining_display.short_description = 'gettone MT'
    get_gettone_totale_mastertraining_display.admin_order_field = 'gettone_totale_mastertraining'

    def get_gettone_totale_agente_display(self):
        return get_valuta_display(self.gettone_totale_agente)
    get_gettone_totale_agente_display.short_description = 'gettone ag.'
    get_gettone_totale_agente_display.admin_order_field = 'gettone_totale_agente'

    def get_url(self):
        if self.gestione_azienda:
            url = reverse('admin:contratti_contratto%s_change' % self.gestione_azienda, args=(self.id,))
        else:
            url = reverse('admin:contratti_contratto_change', args=(self.id,))
        return url

    def get_url_per_tipo(self):
        fornitore = self.fornitore
        if self.fornitore == '3italia':
            fornitore = '3'
        if self.fornitore == 'fastweb_reseller':
            fornitore = 'fastwebreseller'
        if self.fornitore == 'kpn':
            fornitore = 'kpnquest'
        url = reverse('admin:contratti_contratto%s_change' % fornitore, args=(self.id,))
        return url

    def get_url_installazione_per_tipo(self):
        fornitore = self.fornitore
        if self.fornitore == '3italia':
            fornitore = '3'
        if self.fornitore == 'fastweb_reseller':
            fornitore = 'fastwebreseller'
        if self.fornitore == 'kpn':
            fornitore = 'kpnquest'
        url = reverse('admin:contratti_installazione%s_change' % fornitore, args=(self.id,))
        return url

    def get_link_display(self):
        url_file = self.get_url_per_tipo()
        return format_html('<a href="%s" target="">%s</a>' % (url_file, self.azienda))
    get_link_display.short_description = 'Contratto'
    get_link_display.admin_order_field = 'id'

    def get_link_offerta(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.offerta.id))
        else:
            return ''
    get_link_offerta.short_description = 'Off.'
    get_link_offerta.admin_order_field = 'offerta'

    def get_link_preordine(self):
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.preordine.id))
        else:
            return ''
    get_link_preordine.short_description = 'Pre.'
    get_link_preordine.admin_order_field = 'preordine'

    def get_sede_display(self):
        if self.sede:
            return self.sede
        else:
            return self.azienda
    get_sede_display.short_description = 'Sede/Azienda'
    get_sede_display.admin_order_field = 'sede'

    def get_cliente(self):
        if self.sede:
            return self.sede.anagrafica

    def get_elenco_materiale_display(self):
        materiale_stringa = ''
        if not self.fornitore == 'mastervoice':
            for dettaglio in self.dettagliocontratto_set.all():
                if not materiale_stringa == '':
                    materiale_stringa += '<br>'
                descr = dettaglio.__str__()
                if len(descr) > 50:
                    descr = descr[0:50] + '...'
                materiale_stringa += '<nobr>' + descr + '</nobr>'
        for materiale in self.materialecontratto_set.all():
            if not materiale_stringa == '':
                materiale_stringa += '<br>'
            mat = materiale.__str__()
            if len(mat) > 50:
                mat = mat[0:50] + '...'
            materiale_stringa += '<nobr>' + mat + '</nobr>'
        return format_html(materiale_stringa)
    get_elenco_materiale_display.short_description = 'Materiale'

    def get_elenco_link_allegati(self):
        from mastergest.attachments.models import Attachment
        elenco_allegati = Attachment.objects.attachments_for_object(self).all()
        stringa_elenco = ''
        if elenco_allegati:
            for allegato in elenco_allegati:
                if not stringa_elenco == '':
                    stringa_elenco += '<br>'
                stringa_elenco += '<nobr>' + allegato.get_link() + '</nobr>'
        return format_html(stringa_elenco)
    get_elenco_link_allegati.short_description = 'Allegati'

    def get_elenco_canoni(self):
        elenco_canoni = self.canone_set.all()
        stringa_elenco = ''
        if elenco_canoni:
            for canone in elenco_canoni:
                if not stringa_elenco == '':
                    stringa_elenco += '<br>'
                if canone.stato:
                    stringa_elenco += '<nobr><a href="%s" target="">%s (%s)</a>' % (canone.get_url(), canone.id, canone.stato) + '</nobr>'
                else:
                    stringa_elenco += '<nobr><a href="%s" target="">%s</a>' % (canone.get_url(), canone.id) + '</nobr>'
        return format_html(stringa_elenco)
    get_elenco_canoni.short_description = 'Canoni'

    def get_link_progetto(self):
        if self.progetto:
            url_file = self.progetto.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.progetto))
        else:
            return ''
    get_link_progetto.short_description = 'Link.'
    get_link_progetto.admin_order_field = 'progetto'

    def email_collaudo_eseguito(self):
        cliente = self.get_cliente() or self.azienda
        titolo = '[INSTALLAZIONE] Collaudo eseguito per Cliente %s' % cliente
        link_installazione = 'http://%s' % settings.URL_GESTIONALE + self.get_url_installazione_per_tipo()
        link_contratto = 'http://%s' % settings.URL_GESTIONALE + self.get_url_per_tipo()
        messaggio = '%s\n\r Installazione: %s\n\r Contratto: %s\n\r Note:\n\r%s Note Stato:\n\r%s' % (
            titolo, link_installazione, link_contratto, self.note, self.note_stato
        )
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo,
            messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_COLLAUDO_INSTALLAZIONE,
        )

    def email_installazione_bloccata(self):
        cliente = self.get_cliente() or self.azienda
        titolo = '[INSTALLAZIONE] Installazione Bloccata per Cliente %s' % cliente
        link_installazione = 'http://%s' % settings.URL_GESTIONALE + self.get_url_installazione_per_tipo()
        link_contratto = 'http://%s' % settings.URL_GESTIONALE + self.get_url_per_tipo()
        messaggio = '%s\n\r Installazione: %s\n\r Contratto: %s\n\r Note:\n\r%s Note Stato:\n\r%s' % (
            titolo, link_installazione, link_contratto, self.note, self.note_stato
        )
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo,
            messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_COLLAUDO_INSTALLAZIONE,
        )

    def get_sede_filtro_link(self):
        if self.sede:
            url = '?sede__id__exact=%s' % self.sede.id
            return format_html('<a href="%s">%s</a>' % (url, self.sede))
        return self.sede
    get_sede_filtro_link.short_description = 'Sede Cliente'
    get_sede_filtro_link.admin_order_field = 'sede'


class ContrattoMastertrainingManager(models.Manager):
    
        def get_queryset(self):
            qs = super(ContrattoMastertrainingManager, self).get_queryset()
            return qs.filter(gestione_azienda='mastertraining')


class ContrattoMastertraining(Contratto):
    objects = ContrattoMastertrainingManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Contratti Mastertraining'
        
    def save(self, *args, **kwargs):
        self.gestione_azienda = 'mastertraining'
        super(ContrattoMastertraining, self).save(*args, **kwargs)


class ContrattoMagisterManager(models.Manager):
    
        def get_queryset(self):
            qs = super(ContrattoMagisterManager, self).get_queryset()
            return qs.filter(gestione_azienda='magister')


class ContrattoMagister(Contratto):
    objects = ContrattoMagisterManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Contratti Magister'
        
    def save(self, *args, **kwargs):
        self.gestione_azienda = 'magister'
        self.fornitore = 'magister'
        super(ContrattoMagister, self).save(*args, **kwargs)


class Compenso(models.Model):
    contratto = models.ForeignKey(
        Contratto, null=True, blank=True, on_delete=models.CASCADE
    )
    agente = models.ForeignKey(Agente, on_delete=models.PROTECT)
    data = models.DateField('data')
    tipo = models.CharField(
        'tipo', max_length=200, choices=TIPI_COMPENSO, default='provvigione')
    importo = models.DecimalField(
        'importo', max_digits=9, decimal_places=2, default=0)
    valore = models.DecimalField(max_digits=9, decimal_places=2, default=0)
    descrizione = models.CharField(max_length=200, null=True, blank=True)
    consolidato = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = 'compensi'
        ordering = ['-data', 'contratto', 'agente', 'tipo']
        app_label = 'contratti'

    def __str__(self):
        return '%s %s del %s (%s)' % (self.tipo, self.agente, self.data, self.contratto)

    def save(self, *args, **kwargs):
        if self.importo:
            if self.tipo == 'storno' or self.tipo == 'bonifico':
                self.valore = -self.importo
            else:
                self.valore = self.importo
        if self.tipo == 'rata':
            if not self.id:
                self.consolidato = True
        if self.agente:
            if self.agente.mastertraining:
                self.consolidato = True
        super(Compenso, self).save(*args, **kwargs)

    def get_link_contratto(self):
        if self.contratto:
            return self.contratto.get_link_display()
    get_link_contratto.short_description = 'Vedi'
    get_link_contratto.admin_order_field = 'contratto'

    def is_primo(self):
        elenco_compensi_contratto = self.contratto.compenso_set.filter(agente=self.agente).order_by('data')
        if elenco_compensi_contratto[0] == self:
            return True
        else:
            return False


class CompensoAgenteManager(models.Manager):

    def get_queryset(self):
        qs = super(CompensoAgenteManager, self).get_queryset()
        return qs.filter(consolidato=False)


class CompensoAgente(Compenso):
    objects = CompensoAgenteManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'compensi da maturare'
        verbose_name = 'compenso da maturare'
        app_label = 'contratti'
        ordering = ('contratto__azienda', )


class GestioneContratto(models.Model):
    contratto_origine = models.OneToOneField(
        Contratto, related_name='contratto_origine_pk', primary_key=True,
        verbose_name='contr. da elim.', on_delete=models.CASCADE
    )
    contratto_destinazione = models.ForeignKey(
        Contratto, null=True, blank=True,
        related_name='contratto_destinazione_pk',
        verbose_name='contr. che rim.', on_delete=models.CASCADE
    )
    sposta_canoni = models.BooleanField('can.', default=True)
    sposta_materiali = models.BooleanField('mat.', default=True)
    sposta_preordini = models.BooleanField('pre.', default=True)
    sposta_offerte = models.BooleanField('off.', default=True)
    sposta_progetto = models.BooleanField('prog.', default=True)
    sposta_allegati = models.BooleanField('all.', default=True)
    elimina_corrente = models.BooleanField('del.', default=True)
    fatto = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = 'gestione contratti'
        ordering = [
            'contratto_origine__azienda',
            'contratto_origine__data_consegna_contratto'
        ]
        app_label = 'contratti'

    def __str__(self):
        return '%s -> %s' % (self.contratto_origine, self.contratto_destinazione)

    def get_id_contratto_origine(self):
        return self.contratto_origine.id
    get_id_contratto_origine.short_description = 'ID'
    get_id_contratto_origine.admin_order_field = 'contratto_origine'

    def get_link_contratto_origine(self):
        nome_contratto = '%s del %s (%s)' % (
            self.contratto_origine.get_sede_display(),
            self.contratto_origine.data_consegna_contratto,
            self.contratto_origine.agente)
        url_file = self.contratto_origine.get_url_per_tipo()
        return format_html('<a href="%s" target="">%s</a>' % (url_file, nome_contratto))
    get_link_contratto_origine.short_description = 'Contr. da eliminare'
    get_link_contratto_origine.admin_order_field = 'contratto_origine__azienda'

    def get_elenco_materiale_display(self):
        return self.contratto_origine.get_elenco_materiale_display()
    get_elenco_materiale_display.short_description = 'Materiale'

    def get_categoria_contratto(self):
        return self.contratto_origine.categoria
    get_categoria_contratto.short_description = 'Cat.'

    def get_elenco_link_allegati(self):
        return self.contratto_origine.get_elenco_link_allegati()
    get_elenco_link_allegati.short_description = 'Allegati'

    def get_elenco_canoni(self):
        return self.contratto_origine.get_elenco_canoni()
    get_elenco_canoni.short_description = 'Canoni'

    def get_link_offerta(self):
        return self.contratto_origine.get_link_offerta()
    get_link_offerta.short_description = 'Off.'
    get_link_offerta.admin_order_field = 'contratto_origine__offerta'

    def get_link_progetto(self):
        return self.contratto_origine.get_link_progetto()
    get_link_progetto.short_description = 'Prog.'
    get_link_progetto.admin_order_field = 'contratto_origine__progetto'

    def get_link_preordine(self):
        return self.contratto_origine.get_link_preordine()
    get_link_preordine.short_description = 'Pre.'
    get_link_preordine.admin_order_field = 'contratto_origine__preordine'

    def save(self, *args, **kwargs):
        if self.contratto_destinazione:
            dest = GestioneContratto.objects.get(contratto_origine=self.contratto_destinazione)
            contratto_destinazione_update = Contratto.objects.filter(pk=self.contratto_destinazione.pk)
            if self.sposta_canoni:
                if self.contratto_origine.sede and not self.contratto_destinazione.sede:
                    contratto_destinazione_update.update(sede=self.contratto_origine.sede)
                for canone in self.contratto_origine.canone_set.all():
                    canone.contratto = self.contratto_destinazione
                    canone.save()
            if self.sposta_materiali:
                for materiale in self.contratto_origine.materialecontratto_set.all():
                    materiale.contratto = self.contratto_destinazione
                    materiale.save()
            if self.sposta_offerte:
                if self.contratto_origine.offerta:
                    contratto_destinazione_update.update(offerta=self.contratto_origine.offerta)
            if self.sposta_preordini:
                if self.contratto_origine.preordine:
                    contratto_destinazione_update.update(preordine=self.contratto_origine.preordine)
            if self.sposta_progetto:
                if self.contratto_origine.progetto:
                    contratto_destinazione_update.update(progetto=self.contratto_origine.progetto)
            if self.sposta_allegati:
                from mastergest.attachments.models import Attachment
                elenco_allegati = Attachment.objects.attachments_for_object(self.contratto_origine).all()
                if elenco_allegati:
                    for allegato in elenco_allegati:
                        allegato.content_object = self.contratto_destinazione
                        allegato.save()
            if self.elimina_corrente:
                self.contratto_origine.delete()
                self.delete()
            dest.fatto = True
            dest.save()
            return
        super(GestioneContratto, self).save(*args, **kwargs)


class MaterialeContratto(models.Model):
    contratto = models.ForeignKey(Contratto, on_delete=models.CASCADE)
    prodotto = models.ForeignKey(
        'listino.Prodotto', limit_choices_to=dict(a_listino=True),
        on_delete=models.PROTECT
    )
    quantita = models.PositiveIntegerField(default=1)
    prezzo_listino = models.DecimalField(
        'prezzo listino (euro)', max_digits=9, decimal_places=2, null=True,
        blank=True
    )
    prezzo_vendita = models.DecimalField(
        'prezzo vendita (euro)', max_digits=9, decimal_places=2, null=True,
        blank=True
    )
    totale_vendita = models.DecimalField(
        'totale (euro)', max_digits=9, decimal_places=2, null=True, blank=True
    )
    quantita_installata = models.PositiveIntegerField(default=0)
    tipo = models.CharField(
        max_length=200, choices=TIPI_MATERIALE_MASTERCOM, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = 'materiale contratto'
        ordering = ('contratto', 'id')
        app_label = 'contratti'

    def __str__(self):
        return '%sx %s' % (self.quantita, self.prodotto)

    def save(self, *args, **kwargs):
        self.costo = self.prodotto.costo
        self.prezzo_listino = self.prodotto.get_prezzo_listino()
        if self.pk:
            old_materiale = MaterialeContratto.objects.get(pk=self.pk)
            if not old_materiale.prodotto == self.prodotto:
                self.prezzo_vendita = (self.prodotto.get_prezzo_listino()).quantize(DECIMAL_ZERO)
        if not self.prezzo_vendita:
            self.prezzo_vendita = (self.prodotto.get_prezzo_listino()).quantize(DECIMAL_ZERO)
        if self.prezzo_vendita:
            self.totale_vendita = (self.prezzo_vendita * self.quantita).quantize(DECIMAL_ZERO)
        return super(MaterialeContratto, self).save(*args, **kwargs)

    def get_totale_riga(self):
        if self.prezzo_vendita and self.quantita:
            return (self.prezzo_vendita * self.quantita).quantize(DECIMAL_ZERO)
        else:
            return DECIMAL_ZERO


class DettaglioContratto(models.Model):
    contratto = models.ForeignKey(Contratto, on_delete=models.CASCADE)
    tipo_contratto = models.ForeignKey(TipoContratto, on_delete=models.PROTECT)
    quantita = models.PositiveIntegerField('valore/q.ta', default=1)
    gettone_agente = models.DecimalField(
        max_digits=9, decimal_places=2, default=0)
    storno_agente = models.DecimalField(
        max_digits=9, decimal_places=2, default=0)
    gettone_mastertraining = models.DecimalField(
        max_digits=9, decimal_places=2, default=0)
    totale_agente = models.DecimalField(
        max_digits=9, decimal_places=2, default=0)
    totale_mastertraining = models.DecimalField(
        max_digits=9, decimal_places=2, default=0)

    class Meta:
        verbose_name_plural = 'dettagli contratto'
        ordering = ['contratto', 'tipo_contratto']
        app_label = 'contratti'

    def __str__(self):
        return '%sx %s' % (self.quantita, self.tipo_contratto)

    def calcola_gettone_agente(self, agente=None, schema_provvigione=None):
        if self.contratto:
            if self.contratto.data_inizio_rate:
                if not agente:
                    agente = self.contratto.agente
                if not schema_provvigione:
                    data_inizio_rate = self.contratto.data_inizio_rate
                    fornitore = self.contratto.fornitore
                    tipologia = self.contratto.tipologia
                    provvigioni_agente = SchemaProvvigioneAgente.objects.filter(agente=agente)
                    schemi_provvigioni_corrente = provvigioni_agente.filter(
                        data_inizio_validita__lte=data_inizio_rate,
                        data_fine_validita__gte=data_inizio_rate,
                        fornitore=fornitore,
                        tipologia=tipologia)
                    if schemi_provvigioni_corrente.count() == 1:
                        try:
                            dettaglio_provvigione = schemi_provvigioni_corrente[0].dettaglioschemaprovvigioneagente_set.get(
                                tipo_contratto=self.tipo_contratto)
                            return dettaglio_provvigione.provvigione
                        except DettaglioSchemaProvvigioneAgente.DoesNotExist:
                            pass
                else:
                    try:
                        dettaglio_provvigione = schema_provvigione.dettaglioschemaprovvigioneagente_set.get(tipo_contratto=self.tipo_contratto)
                        return dettaglio_provvigione.provvigione
                    except DettaglioSchemaProvvigioneAgente.DoesNotExist:
                        pass
            return Decimal('0.00')

    def save(self, aggiorna_contratto=True, *args, **kwargs):
        agente_master = Agente.objects.get(mastertraining=True)
        if not self.gettone_agente or self.gettone_agente == Decimal('0.00'):
            self.gettone_agente = self.calcola_gettone_agente()
        if not self.gettone_mastertraining or self.gettone_mastertraining == Decimal('0.00'):
            self.gettone_mastertraining = self.calcola_gettone_agente(agente_master)
        self.totale_agente = self.get_gettone_totale_agente()
        self.totale_mastertraining = self.get_gettone_totale_mastertraining()
        super(DettaglioContratto, self).save(*args, **kwargs)
        if aggiorna_contratto:
            self.contratto.save()

    def delete(self, *args, **kwargs):
        super(DettaglioContratto, self).delete(*args, **kwargs)
        self.contratto.save()

    def get_gettone_totale_agente(self):
        gettone_totale = (self.gettone_agente * self.quantita) - self.storno_agente
        return gettone_totale

    def get_gettone_totale_mastertraining(self):
        gettone_totale = (self.gettone_mastertraining * self.quantita)
        return gettone_totale
