from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class Contratto3Manager(models.Manager):

    def get_queryset(self):
        qs = super(Contratto3Manager, self).get_queryset()
        return qs.filter(fornitore='3italia')


class Contratto3(Contratto):
    objects = Contratto3Manager()

    class Meta:
        proxy = True
        verbose_name = 'contratto H3g Italia'
        verbose_name_plural = 'contratti H3g Italia'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = '3italia'
        if not self.tipologia:
            self.tipologia = 'mobile'
        return super(Contratto3, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contratto3_change', args=(self.id,))
        return url
