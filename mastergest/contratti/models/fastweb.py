from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto, Preordine
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class PreordineFastwebManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineFastwebManager, self).get_queryset()
        return qs.filter(tipo='fastweb')


class PreordineFastweb(Preordine):
    objects = PreordineFastwebManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine fastweb'
        verbose_name_plural = 'preordini fastweb'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'fastweb'
        return super(PreordineFastweb, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_preordinefastweb_change', args=(self.id,))
        return url


class ContrattoFastwebManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoFastwebManager, self).get_queryset()
        return qs.filter(fornitore='fastweb')


class ContrattoFastweb(Contratto):
    objects = ContrattoFastwebManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto fastweb'
        verbose_name_plural = 'contratti fastweb'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'fastweb'
        if not self.tipologia:
            self.tipologia = 'fisso'
        return super(ContrattoFastweb, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattofastweb_change', args=(self.id,))
        return url


class InstallazioneFastweb(ContrattoFastweb):

    class Meta:
        proxy = True
        verbose_name = 'installazione fastweb'
        verbose_name_plural = 'installazioni fastweb'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione FW per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'fastweb'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneFastweb.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneFastweb, self).save(*args, **kwargs)
