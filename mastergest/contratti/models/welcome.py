from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class ContrattoWelcomeManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoWelcomeManager, self).get_queryset()
        return qs.filter(fornitore='welcome')


class ContrattoWelcome(Contratto):
    objects = ContrattoWelcomeManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto Welcome'
        verbose_name_plural = 'contratti Welcome'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'welcome'
        if not self.tipologia:
            self.tipologia = 'fisso'
        return super(ContrattoWelcome, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattowelcome_change', args=(self.id,))
        return url


class InstallazioneWelcome(ContrattoWelcome):

    class Meta:
        proxy = True
        verbose_name = 'installazione welcome'
        verbose_name_plural = 'installazioni welcome'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione Welcome per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'welcome'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneWelcome.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneWelcome, self).save(*args, **kwargs)
