from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.utils.html import format_html
from django.contrib.contenttypes.models import ContentType

from mastergest.contratti.models import <PERSON><PERSON><PERSON>, <PERSON>ord<PERSON>, Agente
from mastergest.contratti.models import MaterialeContratto
from mastergest.contratti.models import DESTINATARI_EMAIL_GENERA_CONTRATTO_PREORDINE
from mastergest.utils.tasks import invia_email_task

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class PreordineMastervoiceManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineMastervoiceManager, self).get_queryset()
        return qs.filter(tipo='mastervoice')


class PreordineMastervoice(Preordine):
    objects = PreordineMastervoiceManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine mastervoice'
        verbose_name_plural = 'preordini mastervoice'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'mastervoice'
        return super(PreordineMastervoice, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_preordinemastervoice_change', args=(self.id,))
        return url

    def email_contratto_generato(self, contratto):
        cliente = self.azienda or self.get_cliente()
        titolo = '[CONTRATTI] Generato nuovo contratto da preordine codice %s - relativo a %s' % (self.id, cliente)
        link_contratto = 'http://%s/contratti/installazionemastervoice/%s/' % (settings.URL_GESTIONALE, contratto.pk)
        messaggio = '%s\n\r%s' % (titolo, link_contratto)
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo,
            messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_GENERA_CONTRATTO_PREORDINE
        )

    def genera_contratto(self):
        if self.id:
            nuovo_contratto = Contratto()
            nuovo_contratto.azienda = self.azienda
            nuovo_contratto.sede = self.sede_cliente
            nuovo_contratto.provincia = self.provincia
            nuovo_contratto.fornitore = 'mastervoice'
            nuovo_contratto.referente = self.referente
            nuovo_contratto.ruolo_referente = self.ruolo_referente
            nuovo_contratto.telefono_referente = self.telefono_referente
            nuovo_contratto.email_referente = self.email_referente
            nuovo_contratto.note = self.note
            if self.agente:
                nuovo_contratto.agente = self.agente
            else:
                agente_master = Agente.objects.get(mastertraining=True)
                nuovo_contratto.agente = agente_master
            nuovo_contratto.preordine = self
            nuovo_contratto.forma_contratto = 'vendita'
            nuovo_contratto.tipo_installazione = 'mv'
            nuovo_contratto.save()
            if self.materialepreordine_set.all():
                for materiale in self.materialepreordine_set.all():
                    MaterialeContratto.objects.create(
                        contratto=nuovo_contratto, prodotto=materiale.prodotto,
                        quantita=materiale.quantita,
                        prezzo_listino=materiale.prezzo_listino,
                        prezzo_vendita=materiale.prezzo_vendita,
                        totale_vendita=materiale.totale_vendita)
            from mastergest.attachments.models import Attachment
            elenco_allegati = Attachment.objects.attachments_for_object(self).all()
            if elenco_allegati:
                for allegato in elenco_allegati:
                    allegato.content_object = nuovo_contratto
                    allegato.save()
            self.email_contratto_generato(nuovo_contratto)
            return nuovo_contratto


class ContrattoMastervoiceManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoMastervoiceManager, self).get_queryset()
        return qs.filter(fornitore='mastervoice')


class ContrattoMastervoice(Contratto):
    objects = ContrattoMastervoiceManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto mastervoice'
        verbose_name_plural = 'contratti mastervoice'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'mastervoice'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.valore_contratto and not self.numero_mesi_valore_contratto:
            self.numero_mesi_valore_contratto = 1
        return super(ContrattoMastervoice, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattomastervoice_change', args=(self.id,))
        return url


class InstallazioneMastervoiceManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneMastervoiceManager, self).get_queryset()
        return qs.filter(fornitore='mastervoice', categoria='installazione')


class InstallazioneMastervoice(ContrattoMastervoice):
    objects = InstallazioneMastervoiceManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione mastervoice'
        verbose_name_plural = 'installazioni mastervoice'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione MV per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'mastervoice'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneMastervoice.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
            if self.stato_installazione == 'bloccato' and not vecchia_installazione.stato_installazione == 'bloccato':
                self.email_installazione_bloccata()
        return super(InstallazioneMastervoice, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'

    def get_link_riferimento(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html('<a href="%s" target="">Vedi</a>' % url_file)
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html('<a href="%s" target="">Vedi</a>' % url_file)
        return ''
    get_link_riferimento.short_description = 'Riferimento'

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.offerta))
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html('<a href="%s" target="">%s</a>' % (url_file, self.preordine))
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'

    def genera_acquisto(self, utente):
        from mastergest.acquisti.models import Acquisto, RigaAcquisto
        codice = 'CONTR_%s' % self.id
        try:
            acquisto_esistente = Acquisto.objects.get(riferimento=codice)
            return acquisto_esistente
        except Acquisto.DoesNotExist:
            nuovo_acquisto = Acquisto.objects.create(
                richiedente=utente,
                riferimento=codice,
                settore='mastervoice',
                note='Creato in automatico da contratto ID:%s - Cliente %s' % (self.pk, self.azienda)
            )
            for materiale in self.materialecontratto_set.all():
                nuova_riga = RigaAcquisto.objects.create(
                    acquisto=nuovo_acquisto,
                    prodotto=materiale.prodotto,
                    quantita=materiale.quantita
                )
                nuova_riga.save()
            return nuovo_acquisto


class InstallazioneMastervoiceAgenti(InstallazioneMastervoice):

    class Meta:
        proxy = True
        verbose_name = 'situazione installazioni'
        verbose_name_plural = 'situazione installazioni'
        app_label = 'contratti'
