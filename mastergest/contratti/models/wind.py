from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class ContrattoWindManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoWindManager, self).get_queryset()
        return qs.filter(fornitore='wind')


class ContrattoWind(Contratto):
    objects = ContrattoWindManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto wind'
        verbose_name_plural = 'contratti wind'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'wind'
        if not self.tipologia:
            self.tipologia = 'mobile'
        return super(ContrattoWind, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattowind_change', args=(self.id,))
        return url
