from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.contrib.contenttypes.models import ContentType

from mastergest.contratti.models import <PERSON><PERSON><PERSON>, <PERSON>ord<PERSON>, Agente
from mastergest.contratti.models import MaterialeContratto
from mastergest.utils.tasks import invia_email_task

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_VERIFICA_PREORDINE_MASTERCOM = [
    '<EMAIL>',
    '<EMAIL>',
]


class PreordineMastercomManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineMastercomManager, self).get_queryset()
        return qs.filter(tipo='mastercom')


class PreordineMastercom(Preordine):
    objects = PreordineMastercomManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine mastercom'
        verbose_name_plural = 'preordini mastercom'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'mastercom'
        return super(PreordineMastercom, self).save(*args, **kwargs)

    def email_verifica_tecnica_fatta(self):
        cliente = self.get_cliente() or self.azienda
        titolo = '[PREORDINE] Verifica tecnica completata per Scuola %s' % cliente
        link_preordine = 'http://%s%s' % (settings.URL_GESTIONALE, self.get_url())
        messaggio = '%s\n\r%s' % (titolo, link_preordine)
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo,
            messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_VERIFICA_PREORDINE_MASTERCOM,
        )

    def email_contratto_generato(self, contratto):
        cliente = self.azienda or self.get_cliente()
        titolo = '[CONTRATTI] Generato nuovo contratto da preordine codice %s - relativo a %s' % (self.id, cliente)
        link_contratto = 'http://%s/contratti/contrattomastercom/%s/' % (settings.URL_GESTIONALE, contratto.pk)
        messaggio = '%s\n\r%s' % (titolo, link_contratto)
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo,
            messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_VERIFICA_PREORDINE_MASTERCOM,
        )

    def genera_contratto(self):
        if self.id:
            nuovo_contratto = Contratto()
            nuovo_contratto.azienda = self.azienda
            nuovo_contratto.sede = self.sede_cliente
            nuovo_contratto.provincia = self.provincia
            nuovo_contratto.fornitore = 'mastercom'
            nuovo_contratto.referente = self.referente
            nuovo_contratto.ruolo_referente = self.ruolo_referente
            nuovo_contratto.telefono_referente = self.telefono_referente
            nuovo_contratto.email_referente = self.email_referente
            nuovo_contratto.note = self.note
            if self.agente:
                nuovo_contratto.agente = self.agente
            else:
                agente_master = Agente.objects.get(mastertraining=True)
                nuovo_contratto.agente = agente_master
            nuovo_contratto.preordine = self
            nuovo_contratto.forma_contratto = 'vendita'
            nuovo_contratto.save()
            if self.materialepreordine_set.all():
                for materiale in self.materialepreordine_set.all():
                    MaterialeContratto.objects.create(
                        contratto=nuovo_contratto, prodotto=materiale.prodotto,
                        quantita=materiale.quantita,
                        prezzo_listino=materiale.prezzo_listino,
                        prezzo_vendita=materiale.prezzo_vendita,
                        totale_vendita=materiale.totale_vendita)
            from mastergest.attachments.models import Attachment
            elenco_allegati = Attachment.objects.attachments_for_object(self).all()
            if elenco_allegati:
                for allegato in elenco_allegati:
                    allegato.content_object = nuovo_contratto
                    allegato.save()
            self.email_contratto_generato(nuovo_contratto)
            return nuovo_contratto

    def get_url(self):
        url = reverse('admin:contratti_preordinemastercom_change', args=(self.id,))
        return url


class ContrattoMastercomManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoMastercomManager, self).get_queryset()
        return qs.filter(fornitore='mastercom')


class ContrattoMastercom(Contratto):
    objects = ContrattoMastercomManager()

    class Meta:
        proxy = True
        verbose_name = 'Buono d\'ordine mastercom'
        verbose_name_plural = 'Buoni d\'ordine mastercom'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'mastercom'
        return super(ContrattoMastercom, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:mastercom_contrattomastercom_change', args=(self.id,))
        return url


class InstallazioneMastercomManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneMastercomManager, self).get_queryset()
        return qs.filter(fornitore='mastercom')


class InstallazioneMastercom(Contratto):
    objects = InstallazioneMastercomManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione mastercom'
        verbose_name_plural = 'installazioni mastercom'
        app_label = 'contratti'

    def __str__(self):
        return 'Installazione MC per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'mastercom'
        return super(InstallazioneMastercom, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_installazionemastercom_change', args=(self.id,))
        return url

    def genera_acquisto(self, utente):
        from mastergest.acquisti.models import Acquisto, RigaAcquisto
        codice = 'CONTR_%s' % self.id
        try:
            acquisto_esistente = Acquisto.objects.get(riferimento=codice)
            return acquisto_esistente
        except Acquisto.DoesNotExist:
            nuovo_acquisto = Acquisto.objects.create(
                richiedente=utente,
                riferimento=codice,
                settore='mastercom',
                note='Creato in automatico da contratto ID:%s - Cliente %s' % (self.pk, self.azienda)
            )
            for materiale in self.materialecontratto_set.all():
                nuova_riga = RigaAcquisto.objects.create(
                    acquisto=nuovo_acquisto,
                    prodotto=materiale.prodotto, quantita=materiale.quantita
                )
                nuova_riga.save()
            return nuovo_acquisto

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'
    get_totale_installazione.allow_tags = True

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'
    get_costi_installazione.allow_tags = True
