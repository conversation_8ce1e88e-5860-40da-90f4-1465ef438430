from django.db import models
from decimal import Decimal, getcontext, ROUND_HALF_UP
from django.urls import reverse
from mastergest.contratti.models import Contratto
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class ContrattoTimManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoTimManager, self).get_queryset()
        return qs.filter(fornitore='tim')


class ContrattoTim(Contratto):
    objects = ContrattoTimManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto Tim'
        verbose_name_plural = 'contratti Tim'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'tim'
        if not self.tipologia:
            self.tipologia = 'mobile'
        return super(ContrattoTim, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_contrattotim_change', args=(self.id,))
        return url
