from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.utils.html import format_html

from mastergest.utils.mail import render_email_message
from mastergest.contratti.models import Contratto, Preordine
getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')

DESTINATARI_EMAIL_NUOVO_CONTRATTO_NETANDWORK_INSERITO = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
]


class PreordineNetAndWorkManager(models.Manager):

    def get_queryset(self):
        qs = super(PreordineNetAndWorkManager, self).get_queryset()
        return qs.filter(tipo='netandwork')


class PreordineNetAndWork(Preordine):
    objects = PreordineNetAndWorkManager()

    class Meta:
        proxy = True
        verbose_name = 'preordine net and work'
        verbose_name_plural = 'preordini net and work'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.tipo = 'netandwork'
        return super(PreordineNetAndWork, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_preordinenetandwork_change', args=(self.id,))
        return url


class ContrattoNetAndWorkManager(models.Manager):

    def get_queryset(self):
        qs = super(ContrattoNetAndWorkManager, self).get_queryset()
        return qs.filter(fornitore='netandwork')


class ContrattoNetAndWork(Contratto):
    objects = ContrattoNetAndWorkManager()

    class Meta:
        proxy = True
        verbose_name = 'contratto net and work'
        verbose_name_plural = 'contratti net and work'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.fornitore = 'netandwork'
        if not self.tipologia:
            self.tipologia = 'fisso'
        nuovo_contratto = False
        if not self.id:
            nuovo_contratto = True
        super(ContrattoNetAndWork, self).save(*args, **kwargs)
        if nuovo_contratto:
            self.email_contratto_netandwork_inserito()

    def get_url(self):
        url = reverse('admin:contratti_contrattonetandwork_change', args=(self.id,))
        return url

    def email_contratto_netandwork_inserito(self):
        sender = '<EMAIL>'
        # context = dict(
        #     contratto=self,
        #     dettaglio=self.dettagliocontratto_set.all(),
        #     materiale=self.materialecontratto_set.all()
        # )
        # message = render_email_message(
        #     'contratto/nuovo_contratto_inserito.email',
        #     context,
        #     sender,
        #     DESTINATARI_EMAIL_NUOVO_CONTRATTO_NETANDWORK_INSERITO
        # )
        # connection = mail.get_connection()
        # connection.open()
        # connection.send_messages([message])
        # connection.close()


class InstallazioneNetAndWorkManager(models.Manager):

    def get_queryset(self):
        qs = super(InstallazioneNetAndWorkManager, self).get_queryset()
        return qs.filter(fornitore='netandwork', categoria='installazione')


class InstallazioneNetAndWork(ContrattoNetAndWork):
    objects = InstallazioneNetAndWorkManager()

    class Meta:
        proxy = True
        verbose_name = 'installazione net and work'
        verbose_name_plural = 'installazioni net and work'
        app_label = 'contratti'

    def __str__(self):
        return u'Installazione Net and Work per %s (%s)' % (self.azienda, self.provincia or 'N/A')

    def save(self, *args, **kwargs):
        self.fornitore = 'netandwork'
        if not self.tipologia:
            self.tipologia = 'fisso'
        if self.pk:
            vecchia_installazione = InstallazioneNetAndWork.objects.get(pk=self.pk)
            if self.stato_installazione == 'collaudato' and not vecchia_installazione.stato_installazione == 'collaudato':
                self.email_collaudo_eseguito()
        return super(InstallazioneNetAndWork, self).save(*args, **kwargs)

    def get_totale_installazione(self):
        totale_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.totale_vendita:
                totale_installazione += materiale.totale_vendita
        return totale_installazione
    get_totale_installazione.short_description = 'Totale Installazione (euro)'

    def get_costi_installazione(self):
        costi_installazione = DECIMAL_ZERO
        for materiale in self.materialecontratto_set.all():
            if materiale.prodotto:
                costi_installazione += (materiale.prodotto.costo * materiale.quantita)
        return costi_installazione
    get_costi_installazione.short_description = 'Costi Installazione (euro)'

    def get_link_riferimento(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html(u'<a href="%s" target="">Vedi</a>' % url_file)
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html(u'<a href="%s" target="">Vedi</a>' % url_file)
        return ''
    get_link_riferimento.short_description = 'Riferimento'

    def get_link_riferimento_lista(self):
        if self.offerta:
            url_file = self.offerta.get_url()
            return format_html(u'<a href="%s" target="">%s</a>' % (url_file, self.offerta))
        if self.preordine:
            url_file = self.preordine.get_url()
            return format_html(u'<a href="%s" target="">%s</a>' % (url_file, self.preordine))
        return ''
    get_link_riferimento_lista.short_description = 'Rif.'
