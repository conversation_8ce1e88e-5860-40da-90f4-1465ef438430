# encoding: utf-8
from datetime import date
from decimal import Decimal, getcontext, ROUND_HALF_UP

from django.db import models
from django.urls import reverse
from django.utils.html import format_html

from mastergest.contratti.models import TIPI_FORNITORI_CONTRATTI
from mastergest.contratti.models import TIPOLOGIA_CONTRATTI
from mastergest.contratti.models import <PERSON><PERSON>o<PERSON><PERSON><PERSON><PERSON>, Agente
from mastergest.utils.dates import anno_corrente, mese_corrente

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class SchemaProvvigioneStandard(models.Model):
    descrizione = models.CharField(max_length=200)
    data_inizio_validita = models.DateField(default=date.today)
    data_fine_validita = models.DateField()
    fornitore = models.Char<PERSON>ield(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    tipologia = models.CharField(max_length=200, choices=TIPOLOGIA_CONTRATTI)
    attivo = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = 'schema provvigione standard'
        ordering = ['fornitore', 'data_inizio_validita', 'data_fine_validita']
        app_label = 'contratti'

    def __str__(self):
        return '%s %s - %s' % (
            self.fornitore, self.tipologia, self.descrizione
        )

    def get_provvigioni(self):
        return self.dettaglioschemaprovvigionestandard_set.all()

    def get_fasce_provvigioni(self):
        return self.dettagliovaloreschemastandard_set.all()

    def save(self, *args, **kwargs):
        super(SchemaProvvigioneStandard, self).save(*args, **kwargs)
        if self.pk:
            if not self.get_provvigioni():
                for tipo_contratto in TipoContratto.objects.filter(fornitore=self.fornitore, tipologia=self.tipologia, attivo=True):
                    DettaglioSchemaProvvigioneStandard.objects.create(
                        schema_provvigione=self,
                        tipo_contratto=tipo_contratto
                    )


class DettaglioSchemaProvvigioneStandard(models.Model):
    schema_provvigione = models.ForeignKey(
        SchemaProvvigioneStandard, on_delete=models.CASCADE
    )
    tipo_contratto = models.ForeignKey(TipoContratto, on_delete=models.CASCADE)
    provvigione = models.DecimalField(
        max_digits=9, decimal_places=2, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = 'dettagli schema provvigione standard'
        ordering = ['tipo_contratto']
        unique_together = ('schema_provvigione', 'tipo_contratto')
        app_label = 'contratti'

    def __str__(self):
        return '%s - %s' % (self.schema_provvigione, self.tipo_contratto)


class DettaglioValoreSchemaStandard(models.Model):
    schema_provvigione = models.ForeignKey(
        SchemaProvvigioneStandard, on_delete=models.CASCADE
    )
    da_quantita = models.PositiveIntegerField('Da', default=1)
    a_quantita = models.PositiveIntegerField('A', default=1)
    provvigione = models.DecimalField(
        max_digits=9, decimal_places=2, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = 'Fasce Valore Contratto (Standard)'
        ordering = ['da_quantita']
        app_label = 'contratti'

    def __str__(self):
        return '%s - da %s a %s' % (self.schema_provvigione, self.da_quantita, self.a_quantita)


class SchemaProvvigioneAgenteManager(models.Manager):

    def get_queryset(self):
        qs = super(SchemaProvvigioneAgenteManager, self).get_queryset()
        return qs.filter(capo_area=False)


class SchemaProvvigioneAgente(models.Model):
    objects = SchemaProvvigioneAgenteManager()

    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    schema_provvigione = models.ForeignKey(
        SchemaProvvigioneStandard, on_delete=models.CASCADE
    )
    fornitore = models.CharField(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    tipologia = models.CharField(max_length=200, choices=TIPOLOGIA_CONTRATTI)
    percentuale_liquidazione = models.PositiveIntegerField(default=80)
    mesi_liquidazione = models.PositiveIntegerField(default=6)
    data_inizio_validita = models.DateField(default=date.today)
    data_fine_validita = models.DateField()
    percentuale_provvigione_rate = models.PositiveIntegerField(default=0)
    attivo = models.BooleanField(default=True)
    capo_area = models.BooleanField(default=False)
    mesi_senza_rate = models.PositiveIntegerField(default=0, help_text='numero di mesi dalla data di collaudo in cui l\'agente non percepisce rate.')

    class Meta:
        verbose_name_plural = 'schema provvigione agente'
        ordering = ['agente', 'fornitore', 'data_inizio_validita', 'data_fine_validita']
        app_label = 'contratti'

    def __str__(self):
        return 'Schema Provv. %s per %s (dal %s al %s)' % (self.fornitore, self.agente, self.data_inizio_validita, self.data_fine_validita)

    def get_url(self):
        url = reverse('admin:contratti_schemaprovvigioneagente_change', args=(self.id,))
        return url

    def get_link_display(self):
        url_file = self.get_url()
        return format_html('<a href="%s" target="">Vedi</a>' % url_file)
    get_link_display.short_description = 'Schema'
    get_link_display.admin_order_field = 'id'

    def save(self, *args, **kwargs):
        self.fornitore = self.schema_provvigione.fornitore
        self.tipologia = self.schema_provvigione.tipologia
        risultato = super(SchemaProvvigioneAgente, self).save(*args, **kwargs)
        if self.pk:
            if not self.dettaglioschemaprovvigioneagente_set.all() and self.schema_provvigione:
                for dettaglio_provvigione in self.schema_provvigione.get_provvigioni():
                    DettaglioSchemaProvvigioneAgente.objects.create(
                        schema_provvigione=self,
                        tipo_contratto=dettaglio_provvigione.tipo_contratto,
                        provvigione=dettaglio_provvigione.provvigione
                    )
            if not self.dettagliovaloreschemaagente_set.all() and self.schema_provvigione:
                for dettaglio_valore in self.schema_provvigione.get_fasce_provvigioni():
                    DettaglioValoreSchemaAgente.objects.create(
                        schema_provvigione=self,
                        da_quantita=dettaglio_valore.da_quantita,
                        a_quantita=dettaglio_valore.a_quantita,
                        provvigione=dettaglio_valore.provvigione
                    )
        return risultato


class SchemaProvvigioneCapoAreaManager(models.Manager):

    def get_queryset(self):
        qs = super(SchemaProvvigioneCapoAreaManager, self).get_queryset()
        return qs.filter(capo_area=True)


class SchemaProvvigioneCapoArea(SchemaProvvigioneAgente):
    objects = SchemaProvvigioneCapoAreaManager()

    def __str__(self):
        return 'Schema Provv. Capo Area %s per %s (dal %s al %s)' % (self.fornitore, self.agente, self.data_inizio_validita, self.data_fine_validita)

    class Meta:
        proxy = True
        verbose_name = 'schema provvigione capo area'
        verbose_name_plural = 'schemi provvigioni capi area'
        app_label = 'contratti'

    def save(self, *args, **kwargs):
        self.capo_area = True
        return super(SchemaProvvigioneCapoArea, self).save(*args, **kwargs)

    def get_url(self):
        url = reverse('admin:contratti_schemaprovvigionecapoarea_change', args=(self.id,))
        return url


class CollegamentoSchemiProvvigioni(models.Model):
    schema_provvigione_agente = models.ForeignKey(
        SchemaProvvigioneAgente, related_name='schema_provvigione_agente_pk',
        on_delete=models.CASCADE
    )
    schema_provvigione_capo_area = models.ForeignKey(
        SchemaProvvigioneCapoArea,
        related_name='schema_provvigione_capo_area_pk', on_delete=models.CASCADE
    )

    class Meta:
        app_label = 'contratti'
        unique_together = (('schema_provvigione_agente', 'schema_provvigione_capo_area'), )
        verbose_name = 'collegamenti schemi provvigioni'
        verbose_name_plural = 'collegamenti schemi provvigioni'

    def get_link_capoarea(self):
        if self.schema_provvigione_capo_area:
            return self.schema_provvigione_capo_area.get_link_display()
        else:
            return ''
    get_link_capoarea.short_description = 'Vedi'
    get_link_capoarea.admin_order_field = 'id'


class DettaglioSchemaProvvigioneAgente(models.Model):
    schema_provvigione = models.ForeignKey(
        SchemaProvvigioneAgente, on_delete=models.CASCADE
    )
    tipo_contratto = models.ForeignKey(TipoContratto, on_delete=models.CASCADE)
    provvigione = models.DecimalField(
        max_digits=9, decimal_places=2, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = 'dettagli schema provvigione agente'
        ordering = ['tipo_contratto']
        unique_together = ('schema_provvigione', 'tipo_contratto')
        app_label = 'contratti'

    def __str__(self):
        return '%s - %s' % (self.schema_provvigione, self.tipo_contratto)


class DettaglioValoreSchemaAgente(models.Model):
    schema_provvigione = models.ForeignKey(
        SchemaProvvigioneAgente, on_delete=models.CASCADE
    )
    da_quantita = models.PositiveIntegerField('Da', default=1)
    a_quantita = models.PositiveIntegerField('A', default=1)
    provvigione = models.DecimalField(
        max_digits=9, decimal_places=2, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = 'Fasce Valore Contratto (Agente)'
        ordering = ['da_quantita']
        app_label = 'contratti'

    def __str__(self):
        return '%s - da %s a %s' % (self.schema_provvigione, self.da_quantita, self.a_quantita)


class CalcoloProvvigioniMensili(models.Model):
    anno = models.PositiveIntegerField(default=anno_corrente)
    mese = models.PositiveIntegerField(default=mese_corrente)
    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    numero_storni = models.PositiveIntegerField(default=0)
    fornitore = models.CharField(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    totale_provvigioni_agente = models.DecimalField(
        'Tot. Provv. Ag.', max_digits=9, decimal_places=2, default=0
    )
    totale_provvigioni_mastertraining = models.DecimalField(
        'Tot. Provv. MT', max_digits=9, decimal_places=2, default=0
    )

    class Meta:
        managed = False
        db_table = 'calcolo_provvigioni_mensili'
        app_label = 'contratti'

    def __str__(self):
        return '%s - %s/%s - %s' % (self.agente, self.anno, self.mese, self.fornitore)


class CalcoloProvvigioniTotale(models.Model):
    id = models.CharField(max_length=200, primary_key=True)
    anno = models.PositiveIntegerField(default=anno_corrente)
    mese = models.PositiveIntegerField(default=mese_corrente)
    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    numero_storni = models.PositiveIntegerField(default=0)
    numero_contratti = models.PositiveIntegerField(default=0)
    totale_provvigioni_agente = models.DecimalField(
        'Tot. Provv. Ag.', max_digits=9, decimal_places=2, default=0
    )
    totale_provvigioni_mastertraining = models.DecimalField(
        'Tot. Provv. MT', max_digits=9, decimal_places=2, default=0
    )

    class Meta:
        managed = False
        db_table = 'calcolo_provvigioni_totale'
        verbose_name = 'calcolo provvigioni totale'
        verbose_name_plural = 'calcolo provvigioni totale'
        ordering = ['-anno', '-mese', 'agente']
        app_label = 'contratti'

    def __str__(self):
        return '%s - %s/%s' % (self.agente, self.anno, self.mese)

    def totale_provvigioni_agente_display(self):
        if self.totale_provvigioni_agente < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, self.totale_provvigioni_agente))
    totale_provvigioni_agente_display.short_description = 'Tot. Provv. AG'

    def totale_provvigioni_mastertraining_display(self):
        if self.totale_provvigioni_mastertraining < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, self.totale_provvigioni_mastertraining))
    totale_provvigioni_mastertraining_display.short_description = 'Tot. Provv. MT'

    def get_totale_gare_agente(self):
        elenco_riepiloghi = RiepilogoProvvigioni.objects.filter(
            anno=self.anno, mese=self.mese, agente=self.agente
        ).exclude(
            gara_premio=Decimal('0.00')
        )
        totale_gare = Decimal('0.00')
        for riepilogo in elenco_riepiloghi:
            totale_gare += riepilogo.gara_premio
        return totale_gare
    get_totale_gare_agente.short_description = 'Tot. Gare AG'

    def get_totale_gare_agente_display(self):
        totale_gare = self.get_totale_gare_agente()
        if totale_gare < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_gare))
    get_totale_gare_agente_display.short_description = 'Tot. Gare AG'

    def get_totale_liquidabile(self):
        elenco_riepiloghi = RiepilogoProvvigioni.objects.filter(anno=self.anno, mese=self.mese, agente=self.agente)
        totale_liquidabile = Decimal('0.00')
        for riepilogo in elenco_riepiloghi:
            totale_liquidabile += riepilogo.get_totale_liquidabile()
        return totale_liquidabile
    get_totale_liquidabile.short_description = 'Tot. Liquidabile'

    def get_totale_liquidabile_display(self):
        totale_liquidabile = self.get_totale_liquidabile()
        if totale_liquidabile < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_liquidabile))
    get_totale_liquidabile_display.short_description = 'Tot. Liquidabile'

    def get_totale_rimanente(self):
        return (self.totale_provvigioni_agente + self.get_totale_gare_agente()) - self.get_totale_liquidato()
    get_totale_rimanente.short_description = 'Tot. Rimanente'

    def get_totale_rimanente_display(self):
        totale_rimanente = self.get_totale_rimanente()
        if totale_rimanente < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_rimanente))
    get_totale_rimanente_display.short_description = 'Tot. Rimanente'

    def get_totale_liquidato(self):
        elenco_riepiloghi = RiepilogoProvvigioni.objects.filter(anno=self.anno, mese=self.mese, agente=self.agente)
        totale_liquidato = Decimal('0.00')
        for riepilogo in elenco_riepiloghi:
            totale_liquidato += riepilogo.totale_liquidato
        return totale_liquidato
    get_totale_liquidato.short_description = 'Tot. Liquidato'

    def get_totale_non_liquidato(self):
        return self.totale_provvigioni_agente - self.get_totale_liquidabile()

    def get_totale_liquidato_display(self):
        totale_liquidato = self.get_totale_liquidato()
        if totale_liquidato < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_liquidato))
    get_totale_liquidato_display.short_description = 'Tot. Liquidato'

    def get_utile(self):
        return self.totale_provvigioni_mastertraining - self.totale_provvigioni_agente - self.get_totale_gare_agente()
    get_utile.short_description = 'utile'

    def get_utile_display(self):
        utile = self.get_utile()
        if utile < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, utile))
    get_utile_display.short_description = 'Utile'

    def get_anno_mese(self):
        return '%s/%s' % (self.mese, self.anno)


class RiepilogoProvvigioni(models.Model):
    anno = models.PositiveIntegerField(default=anno_corrente)
    mese = models.PositiveIntegerField(default=mese_corrente)
    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    fornitore = models.CharField(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    numero_storni = models.PositiveIntegerField('storni', default=0)
    percentuale_liquidabile = models.PositiveIntegerField('Perc. Liq.', null=True, blank=True)
    totale_liquidato = models.DecimalField(
        'Liq.ato', max_digits=9, decimal_places=2, default=0
    )
    gara_premio = models.DecimalField(
        'Gara AG', max_digits=9, decimal_places=2, default=0
    )

    class Meta:
        verbose_name_plural = 'riepiloghi provvigioni'
        ordering = ['-anno', '-mese', 'agente', 'fornitore']
        app_label = 'contratti'

    def __str__(self):
        return '%s - %s/%s - %s' % (self.agente, self.anno, self.mese, self.fornitore)

    def get_totale_liquidabile(self):
        if self.percentuale_liquidabile:
            return (self.super_totale_agente() * self.percentuale_liquidabile) / 100
        else:
            return Decimal('0.00')
    get_totale_liquidabile.short_description = 'Tot. Liquidabile'

    def get_totale_liquidabile_display(self):
        totale_liquidabile = self.get_totale_liquidabile()
        if totale_liquidabile < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_liquidabile))
    get_totale_liquidabile_display.short_description = 'Tot. Liquidabile'

    def get_totale_rimanente(self):
        return (self.super_totale_agente() - self.totale_liquidato)
    get_totale_rimanente.short_description = 'Rimanenza Tot.'

    def get_totale_rimanente_display(self):
        totale_rimanente = self.get_totale_rimanente()
        if totale_rimanente < Decimal('0.00'):
            css_class = 'rimanente_negativo'
        elif totale_rimanente == Decimal('0.00'):
            css_class = 'rimanente_nullo'
        else:
            if self.totale_liquidato == Decimal('0.00'):
                css_class = 'rimanente_tutto'
            else:
                css_class = 'rimanente_parziale'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_rimanente))
    get_totale_rimanente_display.short_description = 'Rimanenza Tot.'

    def get_nome_mese(self):
        nome_mesi = ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno', 'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']
        if self.mese:
            return nome_mesi[self.mese - 1]
    get_nome_mese.short_description = 'mese'
    get_nome_mese.admin_order_field = 'mese'

    def totale_provvigioni_master(self):
        try:
            provvigione_mensile = CalcoloProvvigioniMensili.objects.get(
                anno=int(self.anno), mese=int(self.mese), agente=self.agente,
                fornitore=self.fornitore
            )
            return provvigione_mensile.totale_provvigioni_mastertraining
        except CalcoloProvvigioniMensili.DoesNotExist:
            return Decimal('0.00')
    totale_provvigioni_master.short_description = 'Provv. MT'

    def totale_provvigioni_master_display(self):
        totale_provvigioni_master = self.totale_provvigioni_master()
        if totale_provvigioni_master < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_provvigioni_master))
    totale_provvigioni_master_display.short_description = 'Provv. MT'

    def totale_provvigioni_agente(self):
        try:
            provvigione_mensili = CalcoloProvvigioniMensili.objects.get(
                agente=self.agente, fornitore=self.fornitore,
                anno=int(self.anno), mese=int(self.mese)
            )
            return provvigione_mensili.totale_provvigioni_agente
        except CalcoloProvvigioniMensili.DoesNotExist:
            return Decimal('0.00')
    totale_provvigioni_agente.short_description = 'Provv. AG'

    def totale_provvigioni_agente_display(self):
        totale_provvigioni_agente = self.totale_provvigioni_agente()
        if totale_provvigioni_agente < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_provvigioni_agente))
    totale_provvigioni_agente_display.short_description = 'Provv. AG'

    def super_totale_agente(self):
        return self.totale_provvigioni_agente() + self.gara_premio
    super_totale_agente.short_description = 'Tot. AG'

    def super_totale_agente_display(self):
        super_totale_agente = self.super_totale_agente()
        if super_totale_agente < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, super_totale_agente))
    super_totale_agente_display.short_description = 'Tot. AG'

    def _get_numero_storni(self):
        try:
            provvigione_mensile = CalcoloProvvigioniMensili.objects.get(
                anno=self.anno, mese=self.mese, agente=self.agente,
                fornitore=self.fornitore
            )
            numero_storni = provvigione_mensile.numero_storni
        except CalcoloProvvigioniMensili.DoesNotExist:
            numero_storni = 0
        return numero_storni
    numero_storni = property(_get_numero_storni, doc='N. Storni')


class ProvvigioneAgente(models.Model):
    id = models.CharField(max_length=200, primary_key=True)
    anno = models.PositiveIntegerField(default=anno_corrente)
    mese = models.PositiveIntegerField(default=mese_corrente)
    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    numero_compensi = models.PositiveIntegerField('Num. Compensi', default=0)
    numero_ricorrenti = models.PositiveIntegerField('Num. Ricorrenti', default=0)
    numero_gettoni = models.PositiveIntegerField('Num. Gettoni', default=0)
    totale = models.DecimalField(
        'Tot. (€)', max_digits=9, decimal_places=2, default=0
    )
    totale_storni = models.DecimalField(
        'Tot. Storni (€)', max_digits=9, decimal_places=2, default=0
    )
    totale_bonifici = models.DecimalField(
        'Tot. Bonifici (€)', max_digits=9, decimal_places=2, default=0
    )

    class Meta:
        managed = False
        db_table = 'provvigioni_agente'
        verbose_name = 'provvigione agente'
        verbose_name_plural = 'provvigioni agente'
        ordering = ['-anno', '-mese', 'agente']
        app_label = 'contratti'

    def __str__(self):
        return '%s - %s/%s' % (self.agente, self.anno, self.mese)

    def get_nome_mese(self):
        nome_mesi = ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno', 'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']
        if self.mese:
            return nome_mesi[self.mese - 1]
    get_nome_mese.short_description = 'mese'
    get_nome_mese.admin_order_field = 'mese'

    def get_totale_maturato(self):
        return self.totale + self.totale_bonifici + self.totale_storni
    get_totale_maturato.short_description = 'Tot. Maturato (€)'
    get_totale_maturato.admin_order_field = 'totale'

    def get_totale_maturato_display(self):
        totale_maturato = self.get_totale_maturato()
        if totale_maturato < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, totale_maturato))
    get_totale_maturato_display.short_description = 'Tot. Maturato (€)'
    get_totale_maturato_display.admin_order_field = 'totale'

    def get_totale_display(self):
        if self.totale < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, self.totale))
    get_totale_display.short_description = 'Tot. (€)'

    def get_totale_bonifici_display(self):
        if self.totale_bonifici < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, self.totale_bonifici))
    get_totale_bonifici_display.short_description = 'Tot. Bonifici (€)'

    def get_totale_storni_display(self):
        if self.totale_storni < Decimal('0.00'):
            css_class = 'totale_negativo'
        else:
            css_class = 'totale_ok'
        return format_html('<div class="%s">%s</span>' % (css_class, self.totale_storni))
    get_totale_storni_display.short_description = 'Tot. Storni (€)'

    def get_anno_mese(self):
        return '%s %s' % (self.get_nome_mese(), self.anno)


class ProvvigioneAgenteFornitore(models.Model):
    id = models.CharField(max_length=200, primary_key=True)
    anno = models.PositiveIntegerField(default=anno_corrente)
    mese = models.PositiveIntegerField(default=mese_corrente)
    agente = models.ForeignKey(Agente, on_delete=models.CASCADE)
    fornitore = models.CharField(max_length=200, choices=TIPI_FORNITORI_CONTRATTI)
    numero_compensi = models.PositiveIntegerField(default=0)
    totale = models.DecimalField(
        'Tot. Provv.', max_digits=9, decimal_places=2, default=0
    )

    class Meta:
        managed = False
        db_table = 'provvigioni_agente_fornitore'
        verbose_name = 'provvigione agente fornitore'
        verbose_name_plural = 'provvigioni agente fornitore'
        ordering = ['-anno', '-mese', 'agente', 'fornitore']
        app_label = 'contratti'

    def __str__(self):
        return '%s %s - %s/%s' % (self.fornitore, self.agente, self.anno, self.mese)

    def get_nome_mese(self):
        nome_mesi = ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno', 'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']
        if self.mese:
            return nome_mesi[self.mese - 1]
    get_nome_mese.short_description = 'mese'
    get_nome_mese.admin_order_field = 'mese'
