import datetime

from django import forms
from django.contrib.admin import widgets
from django.core.exceptions import ValidationError

from suit.widgets import NumberInput, SuitDateWidget

from mastergest.listino.widgets import ProdottoListinoWidget
from mastergest.anagrafe.widgets import SedeWidget
from mastergest.mastervoice.widgets import ProgettoWidget
from mastergest.contratti.widgets import TipoContrattoWidget, ContrattoWidget
from mastergest.contratti.widgets import TipoContrattoMastervoiceWidget
from mastergest.contratti import models
from mastergest.utils.widgets import EuroInput


class ContrattoForm(forms.ModelForm):
    totale_rate_agente = forms.DecimalField(label='Tot. Rate AG', required=False)
    totale_rate_master = forms.DecimalField(label='Tot. Rate MT', required=False)
    get_totale_valore_contratto = forms.DecimalField(label='Tot. Val. Contratto', required=False)
    get_link_riferimento_lista = forms.CharField(required=False)
    get_link_progetto = forms.CharField(required=False)

    def __init__(self, *args, **kwargs):
        super(ContrattoForm, self).__init__(*args, **kwargs)
        if hasattr(self.instance, 'totale_rate_agente'):
            if 'totale_rate_agente' in self.fields:
                self.fields['totale_rate_agente'].initial = getattr(self.instance, 'totale_rate_agente')
        if hasattr(self.instance, 'totale_rate_master'):
            if 'totale_rate_master' in self.fields:
                self.fields['totale_rate_master'].initial = getattr(self.instance, 'totale_rate_master')
        if self.instance.get_totale_valore_contratto():
            if 'get_totale_valore_contratto' in self.fields:
                self.fields['get_totale_valore_contratto'].initial = self.instance.get_totale_valore_contratto()

    class Meta:
        model = models.Contratto
        fields = '__all__'
        widgets = dict(
            progetto=ProgettoWidget(),
            sede=SedeWidget(),
            azienda=forms.TextInput(attrs={'class': 'input-xxlarge'}),
            provincia=forms.TextInput(attrs={'class': 'input-mini'}),
            gara_accelarazione=EuroInput(),
            gara_valore=EuroInput(),
            gettone_quantita=EuroInput(),
            numero_mesi_valore_contratto=NumberInput(attrs={'class': 'input-mini'}),
            valore_contratto=EuroInput(),
            costo_materiale=EuroInput(),
            premio_agente=EuroInput(),
            importo_rata_mensile=EuroInput(),
            importo_rata_mensile_agente=EuroInput(),
            premio_rata_mensile_agente=EuroInput(),
            numero_rate=NumberInput(attrs={'class': 'input-mini'}),
            numero_contratti_fastweb=NumberInput(attrs={'class': 'input-mini'}),
            data_inizio_rate=SuitDateWidget(),
            data_stipula=SuitDateWidget(),
            data_consegna=SuitDateWidget(),
            data_competenza=SuitDateWidget(),
            data_consegna_contratto=SuitDateWidget(),
            data_chiusura_fastweb=SuitDateWidget(),
            data_fatturazione=SuitDateWidget(),
            data_inserimento_pda=SuitDateWidget(),
            data_attivazione=SuitDateWidget(),
            data_fine_prova=SuitDateWidget(),
            note=forms.Textarea(attrs={'rows': 5, 'class': 'input-xxlarge'}),
            note_stato=forms.Textarea(attrs={'rows': 5, 'class': 'input-xxlarge'}),
            note_documentazione=forms.Textarea(attrs={'rows': 5, 'class': 'input-xxlarge'}),
        )


class ContrattoMastervoiceForm(ContrattoForm):
    valore_contratto = forms.DecimalField(label='Num. Linee Open', required=False)


class DettaglioContrattoForm(forms.ModelForm):
    class Meta:
        model = models.DettaglioContratto
        fields = '__all__'
        widgets = dict(
            tipo_contratto=TipoContrattoWidget(),
            quantita=NumberInput(attrs={'class': 'input-mini'}),
            gettone_agente=EuroInput(),
            gettone_mastertraining=EuroInput(),
            storno_agente=EuroInput(),
        )


class DettaglioContrattoWindForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoVodafoneForm(forms.ModelForm):
    pass


class DettaglioContratto3Form(DettaglioContrattoForm):
    pass


class DettaglioContrattoTimForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoFastwebForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoWelcomeForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoNgiForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoMastervoiceForm(DettaglioContrattoForm):
    class Meta:
        model = models.DettaglioContratto
        fields = '__all__'
        widgets = dict(
            tipo_contratto=TipoContrattoWidget(),
            quantita=NumberInput(attrs={'class': 'input-mini'}),
            gettone_agente=EuroInput(),
            gettone_mastertraining=EuroInput(),
            storno_agente=EuroInput(),
        )


class DettaglioContrattoTrenoveForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoKpnquestForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoLineaIpkomForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoTwtForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoBriantelForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoNetAndWorkForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoHalForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoDigitelForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoAcantoForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoMcLinkForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoFastwebResellerForm(DettaglioContrattoForm):
    pass


class DettaglioContrattoOnesimForm(DettaglioContrattoForm):
    pass


class SchemaProvvigioneAgenteForm(forms.ModelForm):
    class Meta:
        model = models.SchemaProvvigioneAgente
        fields = '__all__'
        widgets = dict(
            data_inizio_validita=SuitDateWidget(),
            data_fine_validita=SuitDateWidget(),
            percentuale_provvigione_rate=NumberInput(attrs={'class': 'input-mini'}),
            percentuale_liquidazione=NumberInput(attrs={'class': 'input-mini'}),
            mesi_senza_rate=NumberInput(attrs={'class': 'input-mini'}),
            mesi_liquidazione=NumberInput(attrs={'class': 'input-mini'}),
            schema_provvigione=forms.Select(attrs={'class': 'input-xxlarge'}),
            tipologia=forms.Select(attrs={'class': 'input-medium'}),
            fornitore=forms.Select(attrs={'class': 'input-medium'}),
        )

    def clean_data_fine_validita(self):
        data_fine_validita = self.cleaned_data['data_fine_validita']
        data_inizio_validita = self.cleaned_data['data_inizio_validita']
        fornitore = self.cleaned_data['fornitore']
        agente = self.cleaned_data['agente']
        tipologia = self.cleaned_data['tipologia']
        instance = self.instance
        if data_inizio_validita:
            if data_fine_validita < data_inizio_validita:
                msg = "La data di fine validita' (%s) e' precedente alla data di inizio (%s)." % (data_fine_validita, data_inizio_validita)
                raise ValidationError(msg)
        if fornitore and agente:
            elenco_schemi = models.SchemaProvvigioneAgente.objects.filter(agente=agente, fornitore=fornitore, tipologia=tipologia, capo_area=False)
            if instance.pk:
                elenco_schemi = elenco_schemi.exclude(pk=instance.pk)
            for schema in elenco_schemi:
                if (schema.data_inizio_validita <= data_inizio_validita and schema.data_fine_validita >= data_fine_validita) or \
                   (data_inizio_validita <= schema.data_inizio_validita and data_fine_validita >= schema.data_fine_validita):
                    msg = "La validita' corrente (dal %s al %s) include o e' inclusa nel periodo di validita dello schema (%s)." % (
                        data_inizio_validita, data_fine_validita, schema
                    )
                    raise ValidationError(msg)
                if (schema.data_inizio_validita <= data_fine_validita and schema.data_inizio_validita >= data_inizio_validita) or \
                   (schema.data_fine_validita <= data_fine_validita and schema.data_fine_validita >= data_inizio_validita):
                    msg = "La validita' corrente (dal %s al %s) si sovrappone al periodo di validita dello schema (%s)." % (
                        data_inizio_validita, data_fine_validita, schema
                    )
                    raise ValidationError(msg)
        return data_fine_validita


class SchemaProvvigioneCapoAreaForm(forms.ModelForm):
    class Meta:
        model = models.SchemaProvvigioneCapoArea
        fields = '__all__'
        widgets = dict(
            data_inizio_validita=SuitDateWidget(),
            data_fine_validita=SuitDateWidget(),
            percentuale_provvigione_rate=NumberInput(attrs={'class': 'input-mini'}),
            percentuale_liquidazione=NumberInput(attrs={'class': 'input-mini'}),
            mesi_senza_rate=NumberInput(attrs={'class': 'input-mini'}),
            mesi_liquidazione=NumberInput(attrs={'class': 'input-mini'}),
            schema_provvigione=forms.Select(attrs={'class': 'input-xxlarge'}),
            tipologia=forms.Select(attrs={'class': 'input-medium'}),
            fornitore=forms.Select(attrs={'class': 'input-medium'}),
        )

    def clean_data_fine_validita(self):
        data_fine_validita = self.cleaned_data['data_fine_validita']
        data_inizio_validita = self.cleaned_data['data_inizio_validita']
        fornitore = self.cleaned_data['fornitore']
        agente = self.cleaned_data['agente']
        tipologia = self.cleaned_data['tipologia']
        instance = self.instance
        if data_inizio_validita:
            if data_fine_validita < data_inizio_validita:
                msg = "La data di fine validita' (%s) e' precedente alla data di inizio (%s)." % (data_fine_validita, data_inizio_validita)
                raise ValidationError(msg)
        if fornitore and agente:
            elenco_schemi = models.SchemaProvvigioneCapoArea.objects.filter(agente=agente, fornitore=fornitore, tipologia=tipologia)
            if instance.pk:
                elenco_schemi = elenco_schemi.exclude(pk=instance.pk)
            for schema in elenco_schemi:
                if (schema.data_inizio_validita <= data_inizio_validita and schema.data_fine_validita >= data_fine_validita) or \
                   (data_inizio_validita <= schema.data_inizio_validita and data_fine_validita >= schema.data_fine_validita):
                    msg = "La validita' corrente (dal %s al %s) include o e' inclusa nel periodo di validita dello schema (%s)." % (
                        data_inizio_validita, data_fine_validita, schema
                    )
                    raise ValidationError(msg)
                if (schema.data_inizio_validita <= data_fine_validita and schema.data_inizio_validita >= data_inizio_validita) or \
                   (schema.data_fine_validita <= data_fine_validita and schema.data_fine_validita >= data_inizio_validita):
                    msg = "La validita' corrente (dal %s al %s) si sovrappone al periodo di validita dello schema (%s)." % (
                        data_inizio_validita, data_fine_validita, schema
                    )
                    raise ValidationError(msg)
        return data_fine_validita


class RiepilogoProvvigioniForm(forms.ModelForm):
    totale_provvigioni_master = forms.CharField(label='Prv. MT', required=False)
    totale_provvigioni_agente = forms.CharField(label='Prv. AG', required=False)
    get_nome_mese = forms.CharField(label='Mese', required=False)
    get_totale_liquidabile = forms.CharField(label='Tot. Liquidabile', required=False)
    get_totale_rimanente = forms.CharField(label='Tot. Rimanente', required=False)
    super_totale_agente = forms.CharField(label='Tot. AG.', required=False)

    class Meta:
        model = models.RiepilogoProvvigioni
        fields = '__all__'
        widgets = dict(
            percentuale_liquidabile=forms.TextInput(attrs=dict(size=8, style='text-align:right;')),
            gara_premio=forms.TextInput(attrs=dict(size=8, style='text-align:right;')),
            totale_liquidato=forms.TextInput(attrs=dict(size=8, style='text-align:right;')),
        )


class CalcoloProvvigioniTotaleForm(forms.ModelForm):
    get_totale_liquidabile = forms.CharField(label='Tot. Liquidabile', required=False)
    get_totale_rimanente = forms.CharField(label='Tot. Rimanente', required=False)


class CollegamentoSchemiProvvigioniForm(forms.ModelForm):
    class Meta:
        model = models.CollegamentoSchemiProvvigioni
        fields = '__all__'
        widgets = dict(
            schema_provvigione_capo_area=forms.Select(attrs={'class': 'input-xxlarge'}),
        )


class ProvvigioneAgenteForm(forms.ModelForm):
    get_totale_maturato = forms.CharField(label='Tot. Maturato', required=False)

    class Meta:
        model = models.ProvvigioneAgente
        fields = '__all__'


class PreordineForm(forms.ModelForm):
    class Meta:
        model = models.Preordine
        fields = '__all__'
        widgets = dict(
            progetto=ProgettoWidget(attrs={'class': 'input-xxlarge'}),
            sede_cliente=SedeWidget(),
            azienda=forms.TextInput(attrs={'class': 'input-xxlarge'}),
            provincia=forms.TextInput(attrs={'class': 'input-mini'}),
            data_firma_contratto=SuitDateWidget(),
            data_verifica_tecnica=SuitDateWidget(),
        )


class ProfiloTelefoniaForm(forms.ModelForm):

    class Meta:
        model = models.ProfiloTelefonia
        fields = '__all__'

    def controlla_intervallo(self, soglia_minuti_inferiore, soglia_minuti_superiore, fornitore, sottotipo, id_profilo):
        if soglia_minuti_superiore:
            if fornitore and sottotipo:
                elenco_profili = models.ProfiloTelefonia.objects.filter(sottotipo=sottotipo, fornitore=fornitore, attivo=True)
                if id_profilo:
                    elenco_profili = elenco_profili.exclude(pk=id_profilo)
                for profilo in elenco_profili:
                    if (profilo.soglia_minuti_inferiore <= soglia_minuti_inferiore and profilo.soglia_minuti_superiore >= soglia_minuti_superiore) or \
                       (soglia_minuti_inferiore <= profilo.soglia_minuti_inferiore and soglia_minuti_superiore >= profilo.soglia_minuti_superiore):
                        msg = "L'intervallo corrente (da %s a %s) include o e' incluso nell'intervallo del profilo (%s)." % (
                            soglia_minuti_inferiore, soglia_minuti_superiore, profilo
                        )
                        raise ValidationError(msg)
                    if (profilo.soglia_minuti_inferiore <= soglia_minuti_superiore and profilo.soglia_minuti_inferiore >= soglia_minuti_inferiore) or \
                       (profilo.soglia_minuti_superiore <= soglia_minuti_superiore and profilo.soglia_minuti_superiore >= soglia_minuti_inferiore):
                        msg = "L'intervallo corrente (da %s a %s) si sovrappone all'intervallo del profilo (%s)." % (
                            soglia_minuti_inferiore, soglia_minuti_superiore, profilo
                        )
                        raise ValidationError(msg)

    def clean_soglia_minuti_superiore(self):
        soglia_minuti_inferiore = self.cleaned_data['soglia_minuti_inferiore']
        soglia_minuti_superiore = self.cleaned_data['soglia_minuti_superiore']
        fornitore = self.cleaned_data['fornitore']
        sottotipo = self.cleaned_data['sottotipo']
        instance = self.instance
        if soglia_minuti_superiore:
            if soglia_minuti_superiore < soglia_minuti_inferiore:
                msg = "La soglia inferiore e' piu' grande della soglia superiore."
                raise ValidationError(msg)
            self.controlla_intervallo(soglia_minuti_inferiore, soglia_minuti_superiore, fornitore, sottotipo, instance.pk)
        return soglia_minuti_superiore


class MaterialePreordineForm(forms.ModelForm):
    class Meta:
        model = models.MaterialePreordine
        fields = '__all__'
        widgets = dict(
            prodotto=ProdottoListinoWidget(),
            quantita=NumberInput(attrs={'class': 'input-mini'}),
            prezzo_listino=EuroInput(),
            prezzo_vendita=EuroInput(),
            totale_vendita=EuroInput(),
        )


class MaterialeContrattoForm(forms.ModelForm):
    class Meta:
        model = models.MaterialeContratto
        fields = '__all__'
        widgets = dict(
            prodotto=ProdottoListinoWidget(),
            quantita=NumberInput(attrs={'class': 'input-mini'}),
            quantita_installata=NumberInput(attrs={'class': 'input-mini'}),
            prezzo_listino=EuroInput(),
            prezzo_vendita=EuroInput(),
            totale_vendita=EuroInput(),
        )


class PrintSetupForm(forms.Form):
    datainizio = forms.DateField(initial=datetime.date.today, widget=widgets.AdminDateWidget(), required=False)
    datafine = forms.DateField(initial=datetime.date.today, widget=widgets.AdminDateWidget(), required=False)
    agente = forms.ModelChoiceField(queryset=models.Agente.objects.all(), empty_label="(Agente)", required=False)


class ContrattoMastercomForm(ContrattoForm):
    azienda = forms.CharField(required=False)


class MaterialeContrattoMastercomForm(MaterialeContrattoForm):
    class Meta:
        model = models.MaterialeContratto
        fields = '__all__'
        widgets = dict(
            prodotto=ProdottoListinoWidget(),
            quantita=NumberInput(attrs={'class': 'input-mini'}),
            quantita_installata=NumberInput(attrs={'class': 'input-mini'}),
            prezzo_listino=EuroInput(),
            prezzo_vendita=EuroInput(),
            totale_vendita=EuroInput(),
        )


class CompensoForm(forms.ModelForm):
    get_link_contratto = forms.CharField(required=False)

    class Meta:
        model = models.Compenso
        fields = '__all__'
        widgets = dict(
            contratto=ContrattoWidget(),
            descrizione=forms.TextInput(attrs={'class': 'input-xxlarge'}),
            data=SuitDateWidget(),
            importo=EuroInput()
        )


class DettaglioValoreSchemaAgenteForm(forms.ModelForm):

    class Meta:
        model = models.DettaglioValoreSchemaAgente
        fields = '__all__'
        widgets = dict(
            da_quantita=forms.NumberInput(attrs={'class': 'input-mini'}),
            a_quantita=forms.NumberInput(attrs={'class': 'input-mini'}),
            provvigione=forms.NumberInput(attrs={'class': 'input-small'}),
        )


class DettaglioSchemaProvvigioneAgenteForm(forms.ModelForm):

    class Meta:
        model = models.DettaglioSchemaProvvigioneAgente
        fields = '__all__'
        widgets = dict(
            tipo_contratto=TipoContrattoWidget(),
            provvigione=EuroInput(attrs={'class': 'input-small'}),
        )
