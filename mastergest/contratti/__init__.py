from django.apps import AppConfig
from django.db.models.signals import post_save


default_app_config = 'mastergest.contratti.ContrattiConfig'


class ContrattiConfig(AppConfig):
    name = 'mastergest.contratti'
    verbose_name = '<PERSON><PERSON><PERSON>'

    def ready(self):
        from mastergest.contratti import models
        from mastergest.contratti.callbacks import aggiorna_compensi
        post_save.connect(aggiorna_compensi, sender=models.Contratto)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoAcanto)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoDigitel)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoFastweb)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoFastwebReseller)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoWelcome)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoWind)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoVodafone)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoMastervoice)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoNetAndWork)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoIpkom)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoLineaIpkom)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoTwt)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoBriantel)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoHal)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoMcLink)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoTim)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoNgi)
        post_save.connect(aggiorna_compensi, sender=models.Contratto3)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoIpkom)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoTrenove)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoKpnquest)
        post_save.connect(aggiorna_compensi, sender=models.ContrattoOnesim)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneFastwebReseller)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneIpkom)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneTwt)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneBriantel)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneNetAndWork)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneHal)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneLineaIpkom)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneWelcome)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneNgi)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneKpnquest)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneTrenove)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneFastweb)
        post_save.connect(aggiorna_compensi, sender=models.InstallazioneMastervoice)
