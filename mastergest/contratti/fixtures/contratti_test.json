[{"pk": 1, "model": "contratti.agente", "fields": {"cognome": "<PERSON><PERSON>", "nome": "<PERSON>"}}, {"pk": 2, "model": "contratti.agente", "fields": {"cognome": "<PERSON><PERSON>", "nome": "<PERSON>"}}, {"pk": 3, "model": "contratti.agente", "fields": {"cognome": "Sala", "nome": "<PERSON>"}}, {"pk": 4, "model": "contratti.agente", "fields": {"attivo": true, "sottotipo": "procacciatore", "tipo": "struttura", "nome": "Group", "cognome": "HeadLine", "stato_documenti": "ok", "mastertraining": false, "telefono": "3484080440", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@headlinegroup.it"}}, {"pk": 99, "model": "contratti.agente", "fields": {"cognome": "master", "nome": "training", "mastertraining": true}}, {"pk": 1, "model": "contratti.statocontratto", "fields": {"descrizione": "in lavorazione", "ignora_provvigioni_relative": false}}, {"pk": 2, "model": "contratti.statocontratto", "fields": {"descrizione": "ko", "ignora_provvigioni_relative": true}}, {"model": "canoni.tipologia", "pk": 1, "fields": {"nome": "<PERSON><PERSON><PERSON>"}}, {"pk": 6, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": false, "codice": "25/2", "descrizione": "25 utenti / 2 linee", "fornitore": "fastweb"}}, {"pk": 7, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "25/3", "descrizione": "25 utenti / 3 linee", "fornitore": "fastweb"}}, {"pk": 8, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "25/4", "descrizione": "25 utenti / 4 linee", "fornitore": "fastweb"}}, {"pk": 9, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "101/xx", "descrizione": "101+ utenti / xx linee", "fornitore": "fastweb"}}, {"pk": 1, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "BB", "descrizione": "Opzione BB", "fornitore": "wind"}}, {"pk": 4, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "EVO", "descrizione": "Sim dati INTERNET EVO", "fornitore": "wind"}}, {"pk": 3, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "PLUS", "descrizione": "Sim dati INTERNET PLUS", "fornitore": "wind"}}, {"pk": 2, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "START", "descrizione": "Sim dati INTERNET START", "fornitore": "wind"}}, {"pk": 5, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "time", "descrizione": "SIM VOCE NEW time + conversioni", "fornitore": "wind"}}, {"pk": 2, "model": "contratti.schemaprovvigionestandard", "fields": {"data_inizio_validita": "2012-01-01", "data_fine_validita": "2012-12-31", "descrizione": "Base 2012", "fornitore": "fastweb"}}, {"pk": 3, "model": "contratti.schemaprovvigionestandard", "fields": {"data_inizio_validita": "2012-01-01", "data_fine_validita": "2012-12-31", "descrizione": "Base 2012", "fornitore": "wind"}}, {"pk": 4, "model": "contratti.schemaprovvigionestandard", "fields": {"data_inizio_validita": "2012-01-01", "data_fine_validita": "2012-12-31", "descrizione": "Base 2012", "fornitore": "mastervoice"}}, {"pk": 4, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "120.00", "tipo_contratto": 6, "schema_provvigione": 2}}, {"pk": 5, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "140.00", "tipo_contratto": 7, "schema_provvigione": 2}}, {"pk": 6, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "155.00", "tipo_contratto": 8, "schema_provvigione": 2}}, {"pk": 7, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "1320.00", "tipo_contratto": 6, "schema_provvigione": 3}}, {"pk": 8, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "1400.00", "tipo_contratto": 7, "schema_provvigione": 3}}, {"pk": 9, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "3005.00", "tipo_contratto": 8, "schema_provvigione": 3}}, {"pk": 10, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "320.00", "tipo_contratto": 6, "schema_provvigione": 4}}, {"pk": 11, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "340.00", "tipo_contratto": 7, "schema_provvigione": 4}}, {"pk": 12, "model": "contratti.dettaglioschemaprovvigionestandard", "fields": {"provvigione": "355.00", "tipo_contratto": 8, "schema_provvigione": 4}}, {"pk": 1, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-07-01", "agente": 2, "schema_provvigione": 2, "percentuale_liquidazione": 75, "mesi_liquidazione": 2, "data_fine_validita": "2012-07-20", "fornitore": "fastweb"}}, {"pk": 2, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-07-21", "agente": 2, "schema_provvigione": 2, "data_fine_validita": "2012-07-30", "percentuale_liquidazione": 70, "mesi_liquidazione": 3, "percentuale_provvigione_rate": 80, "fornitore": "fastweb"}}, {"pk": 3, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-01-01", "agente": 99, "schema_provvigione": 2, "percentuale_liquidazione": 100, "mesi_liquidazione": 0, "data_fine_validita": "2012-12-31", "fornitore": "fastweb"}}, {"pk": 4, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-01-01", "agente": 2, "schema_provvigione": 2, "percentuale_liquidazione": 80, "mesi_liquidazione": 3, "data_fine_validita": "2012-06-30", "fornitore": "fastweb"}}, {"pk": 5, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-01-01", "agente": 3, "schema_provvigione": 3, "percentuale_liquidazione": 60, "mesi_liquidazione": 3, "data_fine_validita": "2012-12-31", "fornitore": "wind"}}, {"pk": 6, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-01-01", "agente": 99, "schema_provvigione": 3, "percentuale_liquidazione": 100, "mesi_liquidazione": 0, "data_fine_validita": "2012-12-31", "fornitore": "wind"}}, {"pk": 7, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2012-01-01", "agente": 2, "schema_provvigione": 4, "percentuale_liquidazione": 80, "mesi_liquidazione": 3, "data_fine_validita": "2012-06-30", "fornitore": "mastervoice"}}, {"pk": 8, "model": "contratti.schemaprovvigioneagente", "fields": {"data_inizio_validita": "2014-07-01", "agente": 2, "schema_provvigione": 4, "percentuale_liquidazione": 100, "mesi_liquidazione": 0, "data_fine_validita": "2014-08-01", "fornitore": "mastervoice", "percentuale_provvigione_rate": 50, "mesi_senza_rate": 5}}, {"pk": 1, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "200.00", "tipo_contratto": 6, "schema_provvigione": 1}}, {"pk": 4, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "120.00", "tipo_contratto": 6, "schema_provvigione": 2}}, {"pk": 2, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "220.00", "tipo_contratto": 7, "schema_provvigione": 1}}, {"pk": 5, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "140.00", "tipo_contratto": 7, "schema_provvigione": 2}}, {"pk": 3, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "240.00", "tipo_contratto": 8, "schema_provvigione": 1}}, {"pk": 6, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "155.00", "tipo_contratto": 8, "schema_provvigione": 2}}, {"pk": 7, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "1000.00", "tipo_contratto": 6, "schema_provvigione": 3}}, {"pk": 9, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "350.00", "tipo_contratto": 6, "schema_provvigione": 4}}, {"pk": 10, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "785.00", "tipo_contratto": 7, "schema_provvigione": 4}}, {"pk": 8, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "2000.00", "tipo_contratto": 7, "schema_provvigione": 3}}, {"pk": 11, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "15.00", "tipo_contratto": 6, "schema_provvigione": 6}}, {"pk": 12, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "75.00", "tipo_contratto": 7, "schema_provvigione": 6}}, {"pk": 13, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "40.00", "tipo_contratto": 8, "schema_provvigione": 6}}, {"pk": 14, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "1350.00", "tipo_contratto": 6, "schema_provvigione": 5}}, {"pk": 15, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "1785.00", "tipo_contratto": 7, "schema_provvigione": 5}}, {"pk": 16, "model": "contratti.dettaglioschemaprovvigioneagente", "fields": {"provvigione": "2540.00", "tipo_contratto": 8, "schema_provvigione": 5}}, {"pk": 1, "model": "contratti.dettagliovaloreschemaagente", "fields": {"da_quantita": 1, "schema_provvigione": 1, "provvigione": "100.00", "a_quantita": 1000}}, {"pk": 2, "model": "contratti.dettagliovaloreschemaagente", "fields": {"da_quantita": 1001, "schema_provvigione": 1, "provvigione": "200.00", "a_quantita": 2000}}, {"pk": 3, "model": "contratti.dettagliovaloreschemaagente", "fields": {"da_quantita": 2001, "schema_provvigione": 1, "provvigione": "300.00", "a_quantita": 5000}}]