[{"pk": 135, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "B.TIME", "sottotipo": "dati", "soglia_minuti_inferiore": 1, "descrizione": "B.TIME LARGE", "fornitore": "3italia", "per_analisi": true, "profilo_standard": true, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 2000, "tipo_gara": null}}, {"pk": 136, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "B.TIME", "sottotipo": "dati", "soglia_minuti_inferiore": 0, "descrizione": "B.TIME SMALL", "fornitore": "3italia", "per_analisi": true, "profilo_standard": false, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 6, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/02", "sottotipo": null, "soglia_minuti_inferiore": 0, "descrizione": "25 utenti / 2 linee", "fornitore": "fastweb", "per_analisi": false, "profilo_standard": false, "tipologia": "fisso", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 7, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "025/03", "sottotipo": null, "soglia_minuti_inferiore": 0, "descrizione": "25 utenti / 3 linee", "fornitore": "fastweb", "per_analisi": false, "profilo_standard": false, "tipologia": "fisso", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 110, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "inst", "sottotipo": null, "soglia_minuti_inferiore": 0, "descrizione": "Installazione", "fornitore": "mastervoice", "per_analisi": false, "profilo_standard": false, "tipologia": "fisso", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 103, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "MV01", "sottotipo": null, "soglia_minuti_inferiore": 0, "descrizione": "Centrale MasterVoice/MasterGate", "fornitore": "mastervoice", "per_analisi": false, "profilo_standard": false, "tipologia": "fisso", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 151, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "5255", "sottotipo": "voce", "soglia_minuti_inferiore": 0, "descrizione": "scatto zero", "fornitore": "tim", "per_analisi": true, "profilo_standard": false, "tipologia": "mobile", "costo": "12.00", "tcg": "35.82", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 152, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "sc1", "sottotipo": "voce", "soglia_minuti_inferiore": 0, "descrizione": "scatto uno", "fornitore": "tim", "per_analisi": true, "profilo_standard": true, "tipologia": "mobile", "costo": "14.00", "tcg": "45.82", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 85, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "001_smart_mini", "sottotipo": "voce", "soglia_minuti_inferiore": 0, "descrizione": "Parla 9 RAM e Smart Mini", "fornitore": "vodafone", "per_analisi": true, "profilo_standard": true, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 86, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "002_smart_easy", "sottotipo": "voce", "soglia_minuti_inferiore": 0, "descrizione": "Smart Easy", "fornitore": "vodafone", "per_analisi": true, "profilo_standard": false, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 87, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "003_smart_pro", "sottotipo": "voce", "soglia_minuti_inferiore": 0, "descrizione": "Smart Professional", "fornitore": "vodafone", "per_analisi": true, "profilo_standard": false, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 153, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "11", "sottotipo": "voce", "soglia_minuti_inferiore": 200, "descrizione": "UNLIMITED", "fornitore": "wind", "per_analisi": true, "profilo_standard": true, "tipologia": "mobile", "costo": "111.00", "tcg": "50.00", "soglia_minuti_superiore": 1000, "tipo_gara": null}}, {"pk": 101, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "ADSL", "sottotipo": "dati", "soglia_minuti_inferiore": 0, "descrizione": "ADSL WIND", "fornitore": "wind", "per_analisi": false, "profilo_standard": false, "tipologia": "fisso", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 1, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "010_BB", "sottotipo": "opzione", "soglia_minuti_inferiore": 0, "descrizione": "Opzione BB", "fornitore": "wind", "per_analisi": true, "profilo_standard": false, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 0, "tipo_gara": null}}, {"pk": 2, "model": "contratti.tip<PERSON><PERSON><PERSON><PERSON>", "fields": {"attivo": true, "codice": "030_dati_start", "sottotipo": "dati", "soglia_minuti_inferiore": 0, "descrizione": "Sim dati INTERNET START", "fornitore": "wind", "per_analisi": true, "profilo_standard": true, "tipologia": "mobile", "costo": null, "tcg": "0.00", "soglia_minuti_superiore": 199, "tipo_gara": null}}]