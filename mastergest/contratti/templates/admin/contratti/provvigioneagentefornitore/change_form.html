{% extends "admin/change_form.html" %}
{% load  i18n %}

{% block after_field_sets %}
<fieldset class="module">
    <h2>Provvigioni/Compensi Contratti</h2>
    <table id="result_list" class="table table-striped table-bordered table-hover table-condensed" width='100%'>
        <thead>
        <tr>
            <th width='10%' align='center'>data</th>
            <th width='5%' align='center'>tipo</th>
            <th width='*' align='center'>contratto</th>
            <th width='10%' class='text-right' align='center'>Importo</th>
        </tr>
        </thead>
    <tbody>
        {% for compenso in lista_compensi %}
        <tr>
            <td align='center'>
                {{ compenso.data }}
            </td>

            <td align='center'>
                {{ compenso.tipo }}
            </td>

            <td>
                {% if compenso.contratto %}
                    {% if perms.contratti.ha_privilegi_responsabile or user.is_superuser %}
                        {% if original.fornitore == '3italia' %}
                            <b><a href="/contratti/contratto3/{{ compenso.contratto_id|slugify }}">{{ compenso.contratto }}</a></b>
                        {% else %}
                            <b><a href="/contratti/contratto{{ original.fornitore }}/{{ compenso.contratto_id|slugify }}">{{ compenso.contratto }}</a></b>
                        {% endif %}
                    {% else %}
                        {{ compenso.contratto }}
                    {% endif %}
                {% endif %}
            </td>

            <td align='right' class='text-right totale_negativo'>
                {% if compenso.tipo == 'storno' %}
                    <font color='#ff0000'>{{ compenso.valore }} €</font>
                {% elif compenso.tipo == 'bonifico' %}
                    <font color='#0000ff'>{{ compenso.valore }} €</font>
                {% else %}
                    {{ compenso.valore }} €
                {% endif %}
            </td>
        </tr>
        {% endfor %}

        <tr>
            <td>
            </td>
            <td>
            </td>
            <td>
            </td>
            <td align='right' class='text-right'>
                <b><font color='#0000ff'>TOTALE: {{ original.totale }} €</font></b>
            </td>
        </tr>
    </tbody>
    </table>
</fieldset>
{% endblock %}
