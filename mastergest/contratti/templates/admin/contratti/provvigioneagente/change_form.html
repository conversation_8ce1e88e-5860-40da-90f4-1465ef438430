{% extends "admin/change_form.html" %}
{% load  i18n %}

{% block object-tools-items %}
    <li><a href="{% url 'admin:contratti_provvigioneagente_invito_odt' original.pk %}" class="historylink">{% trans "Download Invito Fatt." %}</a></li>
    {{ block.super}}
{% endblock %}

{% block after_field_sets %}
<fieldset class="module">
    <h2>Provvigioni/Compensi Contratti</h2>
    <table id="result_list" class="table table-striped table-bordered table-hover table-condensed" width='80%'>
        <thead>
        <tr>
            <th width='*' align='left'>Contratto</th>
            <th width='15%' align='center'>Tipo Compenso</th>
            <th width='15%' align='center'>Tipo Contratto</th>
            <th width='10%' align='center' class='text-right'>Importo</th>
        </tr>
        </thead>
    <tbody>
        {% for compenso in lista_compensi %}
        <tr>
            <td>
                {% if compenso.contratto %}
                    {% if perms.contratti.ha_privilegi_responsabile or user.is_superuser %}
                        {% if original.fornitore == '3italia' %}
                            <b><a href="/contratti/contratto3/{{ compenso.contratto_id|slugify }}">{{ compenso.contratto.azienda }}</a></b>
                        {% else %}
                            <b><a href="/contratti/contratto{{ original.fornitore }}/{{ compenso.contratto_id|slugify }}">{{ compenso.contratto.azienda }}</a></b>
                        {% endif %}
                    {% else %}
                        {{ compenso.contratto.azienda }}
                    {% endif %}
                {% endif %}
            </td>
            <td align='center'>
                {{ compenso.tipo }}
            </td>
            <td align='center'>
                {{ compenso.contratto.fornitore }}
            </td>
            <td align='right' class='text-right totale_negativo'>
                {% if compenso.tipo == 'storno' %}
                    <font color='#ff0000'>{{ compenso.valore }} €</font>
                {% elif compenso.tipo == 'bonifico' %}
                    <font color='#0000ff'>{{ compenso.valore }} €</font>
                {% else %}
                    {{ compenso.valore }} €
                {% endif %}
            </td>
        </tr>
        {% endfor %}
        <tr>
            <td align='center'>
                
            </td>
            <td align='center'>
                
            </td>
            <td align='center'>
                <b><font color='#0000ff'>TOTALE:</font></b>
            </td>
            <td align='right' class='text-right'>
                <b><font color='#0000ff'>{{ original.totale }} €</font></b>
            </td>
        </tr>
    </tbody>
    </table>
</fieldset>
{% endblock %}
