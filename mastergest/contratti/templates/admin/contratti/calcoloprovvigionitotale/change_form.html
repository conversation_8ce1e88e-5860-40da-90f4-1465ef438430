{% extends "admin/change_form.html" %}
{% load  i18n %}

{% block object-tools-items %}
    {% if perms.contratti.ha_privilegi_responsabile or user.is_superuser %}
    <li><a href="{% url 'admin:contratti_calcoloprovvigionitotale_invito_odt' original.pk %}" class="historylink">{% trans "Download Invito Fatt." %}</a></li>
    {% endif %}
    {{ block.super}}
{% endblock %}

{% block after_field_sets %}
<fieldset class="module">
    <h2>Contratti</h2>
    <table width='100%'>
        <thead>
        <tr>
            <th width='*'>azienda</th>
            <th width='10%' align='center'>data consegna</th>
            <th width='10%' align='center'>agente</th>
            <th width='10%' align='center'>fornitore</th>
            <th width='10%' align='center'>Tot. provv. Ag.</th>
            <th width='5%' align='center'>stato</th>
            <th width='1%' align='center'>storni</th>
            <th width='1%' align='center'>Tot. prod.</th>
            <th width='1%'>rata</th>
        </tr>
        </thead>
    <tbody>
        {% for contratto in lista_contratti %}
        <tr>
            <td>
                {% if perms.contratti.ha_privilegi_responsabile or user.is_superuser %}
                    {% if contratto.rata %}
                        {% if contratto.fornitore == '3italia' %}
                            <b><a href="/contratti/contratto3/{{ contratto.contratto_master_id|slugify }}">{{ contratto.azienda }}</a></b>
                        {% else %}
                            <b><a href="/contratti/contratto{{ contratto.fornitore }}/{{ contratto.contratto_master_id|slugify }}">{{ contratto.azienda }}</a></b>
                        {% endif %}
                    {% else %}
                        {% if contratto.fornitore == '3italia' %}
                            <b><a href="/contratti/contratto3/{{ contratto.id|slugify }}">{{ contratto.azienda }}</a></b>
                        {% else %}
                            <b><a href="/contratti/contratto{{ contratto.fornitore }}/{{ contratto.id|slugify }}">{{ contratto.azienda }}</a></b>
                        {% endif %}
                    {% endif %}
                {% else %}
                    {{ contratto.azienda }}
                {% endif %}
            </td>

            <td align='center'>
                {{ contratto.data_consegna }}
            </td>

            <td align='center'>
                {{ contratto.agente }}
            </td>

            <td align='center'>
                {{ contratto.fornitore }}
            </td>

            <td align='right'>
                {{ contratto.totale_agente }}
            </td>

            <td align='center'>
                {% if contratto.stato %}
                    {{ contratto.stato }}
                {% endif %}
            </td>

            <td align='center'>
                {{ contratto.numero_storni }}
            </td>

             <td align='center'>
                {{ contratto.totale_prodotti }}
            </td>

            <td>
                {% if contratto.rata %}
                <img src="/static/admin/img/icon-yes.svg" alt="True">
                {% else %}

                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
    </table>
</fieldset>
{% endblock %}
