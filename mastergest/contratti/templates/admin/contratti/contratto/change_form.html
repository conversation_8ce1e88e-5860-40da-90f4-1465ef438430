{% extends "admin/change_form.html" %}
{% load i18n %}

{% block object-tools-items %}
    {% if original.id %}
        <li><a href="{% url 'admin:contratti_contratto_emailnuovocontratto' original.pk %}" class="historylink">{% trans "Invia Email Nuovo Contratto" %}</a></li>
        <li><a href="{% url 'admin:contratti_contratto_generacanone' original.pk %}" class="historylink">{% trans "Genera Canone" %}</a></li>
    {% endif %}
    {{ block.super }}
{% endblock %}

{% block after_field_sets %}
<div class="suit-tab suit-tab-provvigioni">
<fieldset class="module">
    <h2>Provvigioni/Compensi</h2>
    <table width='100%'>
        <thead>
        <tr>
            <th width='10%' align='center'>data</th>
            <th width='5%' align='center'>tipo</th>
            <th width='*' align='left'>agente</th>
            <th width='10%' align='center'>Importo</th>
        </tr>
        </thead>
    <tbody>
        {% for compenso in lista_compensi %}
        <tr>
            <td align='center'>
                <b><a href="/contratti/compenso/{{ compenso.id|slugify }}">{{ compenso.data }}</a></b>
            </td>
            <td align='center'>
                {{ compenso.tipo }}
            </td>
            <td align='left'>
                {{ compenso.agente }}
            </td>
            <td align='right' class='totale_negativo'>
                {% if compenso.tipo == 'storno' %}
                    <font color='#ff0000'>{{ compenso.valore }}</font>
                {% elif compenso.tipo == 'bonifico' %}
                    <font color='#0000ff'>{{ compenso.valore }}</font>
                {% else %}
                    {{ compenso.valore }}
                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
    </table>
</fieldset>
</div>
{% endblock %}
