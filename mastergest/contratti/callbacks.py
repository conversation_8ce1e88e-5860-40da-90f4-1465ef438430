from dateutil.relativedelta import relativedelta
from decimal import Decimal, getcontext, ROUND_HALF_UP
from mastergest.contratti.models import <PERSON><PERSON><PERSON>, Agente
from mastergest.contratti.models import Compenso
from mastergest.contratti.models import CollegamentoSchemiProvvigioni
from mastergest.contratti.models import DettaglioValoreSchemaAgente

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


def calcola_provvigione_valore(contratto, schema_provvigione):
    if contratto and schema_provvigione:
        valore_fascia = contratto.numero_mesi_valore_contratto * contratto.valore_contratto
        if schema_provvigione:
            try:
                fascia_provvigione = schema_provvigione.dettagliovaloreschemaagente_set.get(
                    da_quantita__lte=valore_fascia, a_quantita__gte=valore_fascia
                )
                return fascia_provvigione.provvigione
            except DettaglioValoreSchemaAgente.DoesNotExist:
                pass
    return Decimal('0.00')


def calcola_provvigione_contratto(contratto, agente, schema_provvigione):
    gettone_totale_agente = Decimal('0.00')
    for dettaglio in contratto.dettagliocontratto_set.all():
        provvigione_agente = dettaglio.calcola_gettone_agente(agente, schema_provvigione)
        gettone_totale_agente += ((provvigione_agente * dettaglio.quantita) - dettaglio.storno_agente)
    return gettone_totale_agente


def genera_rate(contratto, agente, schema_provvigione):
    mesi_senza_rate = 0
    percentuale_agente = 0
    if agente.mastertraining:
        percentuale_agente = 100
    else:
        if schema_provvigione:
            percentuale_agente = schema_provvigione.percentuale_provvigione_rate
            mesi_senza_rate = schema_provvigione.mesi_senza_rate
    if contratto.importo_rata_mensile:
        importo_rata_mensile_agente = (percentuale_agente * (contratto.importo_rata_mensile / 100)).quantize(Decimal('0'))
        if contratto.premio_rata_mensile_agente:
            if not agente.mastertraining:
                importo_rata_mensile_agente += contratto.premio_rata_mensile_agente
        if importo_rata_mensile_agente:
            for count in range(0 + mesi_senza_rate, contratto.numero_rate):
                data_rata = contratto.data_inizio_rate + relativedelta(months=count)
                if importo_rata_mensile_agente:
                    rata_agente = Compenso()
                    rata_agente.contratto = contratto
                    rata_agente.data = data_rata
                    rata_agente.agente = agente
                    rata_agente.tipo = 'rata'
                    rata_agente.importo = importo_rata_mensile_agente
                    rata_agente.save()


def genera_provvigione(contratto, agente, schema_provvigione, provvigione):
    if provvigione and agente and contratto:
        if contratto.data_inizio_rate:
            percentuale_liquidazione = 0
            mesi_liquidazione = 0
            if agente.mastertraining:
                percentuale_liquidazione = None
                mesi_liquidazione = None
            else:
                if schema_provvigione:
                    percentuale_liquidazione = schema_provvigione.percentuale_liquidazione
                    mesi_liquidazione = schema_provvigione.mesi_liquidazione
            if percentuale_liquidazione and mesi_liquidazione:
                importo_iniziale = (provvigione * percentuale_liquidazione) / 100
                compenso_agente_iniziale = Compenso()
                compenso_agente_iniziale.contratto = contratto
                compenso_agente_iniziale.data = contratto.data_inizio_rate
                compenso_agente_iniziale.agente = agente
                compenso_agente_iniziale.tipo = 'provvigione'
                compenso_agente_iniziale.importo = importo_iniziale
                compenso_agente_iniziale.save()
                rimanenza = provvigione - importo_iniziale
                compenso_agente_rimanente = Compenso()
                compenso_agente_rimanente.contratto = contratto
                compenso_agente_rimanente.data = contratto.data_inizio_rate + relativedelta(months=mesi_liquidazione)
                compenso_agente_rimanente.agente = agente
                compenso_agente_rimanente.tipo = 'provvigione'
                compenso_agente_rimanente.importo = rimanenza
                compenso_agente_rimanente.save()
            else:
                compenso_agente = Compenso()
                compenso_agente.contratto = contratto
                compenso_agente.data = contratto.data_inizio_rate
                compenso_agente.agente = agente
                compenso_agente.tipo = 'provvigione'
                compenso_agente.importo = provvigione
                compenso_agente.save()


def aggiorna_compensi(sender, **kwargs):
    try:
        contratto = kwargs['instance']
        data_compenso = contratto.data_inizio_rate
        agente_master = Agente.objects.get(mastertraining=True)
        if data_compenso:
            elenco_compensi = Compenso.objects.filter(contratto=contratto)
            if elenco_compensi:
                elenco_compensi.delete()
            if contratto.stato:
                if contratto.stato.ignora_provvigioni_relative:
                    return
            schema_provvigione = contratto.get_schema_provvigione_agente()
            # --------> RATE
            if contratto.numero_rate and contratto.importo_rata_mensile and contratto.data_inizio_rate:
                if schema_provvigione:
                    # RATE AGENTE
                    genera_rate(contratto, contratto.agente, schema_provvigione)
                    # RATE CAPI AREA
                    collegamenti_schemi = CollegamentoSchemiProvvigioni.objects.filter(schema_provvigione_agente=schema_provvigione)
                    if collegamenti_schemi:
                        for collegamento in collegamenti_schemi:
                            genera_rate(contratto, collegamento.schema_provvigione_capo_area.agente, collegamento.schema_provvigione_capo_area)
                # RATE MASTER
                genera_rate(contratto, agente_master, schema_provvigione)
            # --------> COMPENSI FISSI AGENTE
            genera_provvigione(contratto, contratto.agente, schema_provvigione, contratto.totale_agente)
            # --------> COMPENSI FISSI MASTER
            genera_provvigione(contratto, agente_master, schema_provvigione, contratto.totale_mastertraining)
            # --------> COMPENSI FISSI CAPI AREA
            collegamenti_schemi = CollegamentoSchemiProvvigioni.objects.filter(schema_provvigione_agente=schema_provvigione)
            if collegamenti_schemi:
                for collegamento in collegamenti_schemi:
                    schema_collegato = collegamento.schema_provvigione_capo_area
                    gettone_valore = calcola_provvigione_valore(contratto, schema_collegato)
                    provvigione_fissa = calcola_provvigione_contratto(contratto, schema_collegato.agente, schema_collegato)
                    genera_provvigione(contratto, schema_collegato.agente, schema_collegato, gettone_valore + provvigione_fissa)
    except Contratto.DoesNotExist:
        pass
