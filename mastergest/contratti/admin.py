import csv
import xlwt
from xlwt.Utils import rowcol_to_cell
from decimal import Decimal

from django.contrib import admin
from django.contrib import messages
from django.http import HttpResponse
from django.db import models
from django.http import Http404, HttpResponseRedirect
from django.conf.urls import url
from django.contrib.admin.utils import unquote
from django.template import Context
from django.contrib.admin.options import csrf_protect_m
from django.template.defaultfilters import slugify
from django.utils import timezone

from webodt.shortcuts import render_to_response as webodt_render_to_response

from mastergest.anagrafe.models import ProfiloUtente
from mastergest.contratti.models import DettaglioSchemaProvvigioneStandard
from mastergest.contratti.models import DettaglioValoreSchemaStandard
from mastergest.contratti.models import DettaglioValoreSchemaAgente
from mastergest.contratti.models import DettaglioSchemaProvvigioneAgente
from mastergest.contratti.models import DettaglioContratto
from mastergest.contratti.models import <PERSON><PERSON><PERSON>, MaterialePreordine, Agente
from mastergest.contratti.models import MaterialeContratto, PreordineMastervoice
from mastergest.contratti.models import PreordineMastercom, InstallazioneMastercom
from mastergest.contratti.models import InstallazioneMastervoice, Compenso
from mastergest.contratti.models import InstallazioneKpnquest, InstallazioneTrenove
from mastergest.contratti.models import InstallazioneDigitel
from mastergest.contratti.models import InstallazioneAcanto
from mastergest.contratti.models import InstallazioneMcLink
from mastergest.contratti.models import InstallazioneFastwebReseller
from mastergest.contratti.models import SchemaProvvigioneAgente
from mastergest.contratti.models import SchemaProvvigioneCapoArea
from mastergest.contratti.models import CollegamentoSchemiProvvigioni
from mastergest.contratti import forms
from mastergest.attachments.admin import AttachmentInlinesNoAdd, AttachmentInlinesReadOnly
from mastergest.attachments.admin import AttachmentInlinesAddOnly, AttachmentInlinesContrattiAddOnly
from mastergest.attachments.models import Attachment
from mastergest.canoni.admin import CanoneStackedInline
from mastergest.canoni.models import Categoria
from mastergest.utils.widgets import EuroInput
from mastergest.contratti.utils import crea_canone_contratto
from mastergest.utils.admin import MastergestAdmin

SIMBOLO_EURO = '\u20ac'
DECIMAL_ZERO = Decimal('0.00')


# ---------------------------------------------------------
# TIPO CONTRATTO
# ---------------------------------------------------------

class TipoContrattoAdmin(admin.ModelAdmin):
    list_display = ('codice', 'descrizione', 'fornitore', 'tipologia', 'sottotipo', 'attivo', 'per_analisi', 'profilo_standard')
    list_editable = ('attivo', 'per_analisi', 'profilo_standard')
    list_filter = ('fornitore', 'tipologia', 'sottotipo', 'attivo', 'per_analisi', 'profilo_standard')
    search_fields = ('codice', 'descrizione', 'tipologia', 'sottotipo', 'fornitore')


class TipoContrattoMastervoiceAdmin(admin.ModelAdmin):
    list_display = ('codice', 'descrizione', 'sottotipo', 'attivo', 'per_analisi', 'profilo_standard')


# ---------------------------------------------------------
# DETTAGLIO CONTRATTO
# ---------------------------------------------------------


class DettaglioContrattoInline(admin.TabularInline):
    fields = (
        'tipo_contratto', 'quantita', 'gettone_agente', 'storno_agente',
        'totale_agente', 'gettone_mastertraining', 'totale_mastertraining'
    )
    readonly_fields = ('totale_mastertraining', 'totale_agente')
    model = DettaglioContratto
    form = forms.DettaglioContrattoForm
    formfield_overrides = {
        models.DecimalField: {'localize': True},
    }
    suit_classes = 'suit-tab suit-tab-dettaglio'

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoFastwebInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoFastwebForm
    extra = 1
    max_num = 1


class DettaglioContrattoFastwebResellerInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoFastwebResellerForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoFastwebResellerInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoWelcomeInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoWelcomeForm
    extra = 1
    max_num = 1


class DettaglioContrattoNgiInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoNgiForm
    extra = 1
    max_num = 1


class DettaglioContrattoWindInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoWindForm


class DettaglioContrattoVodafoneInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoVodafoneForm


class DettaglioContrattoTimInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoTimForm


class DettaglioContratto3Inline(DettaglioContrattoInline):
    form = forms.DettaglioContratto3Form


class DettaglioContrattoMastervoiceInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoMastervoiceForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoTrenoveInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoTrenoveForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoTrenoveInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoKpnquestInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoKpnquestForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoKpnquestInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoLineaIpkomInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoLineaIpkomForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoLineaIpkomInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoTwtInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoTwtForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoTwtInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoBriantelInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoBriantelForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoBriantelInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoNetAndWorkInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoNetAndWorkForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoNetAndWorkInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoHalInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoHalForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoHalInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoAcantoInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoAcantoForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoAcantoInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoMcLinkInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoMcLinkForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoMcLinkInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoDigitelInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoDigitelForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoDigitelInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field


class DettaglioContrattoOnesimInline(DettaglioContrattoInline):
    form = forms.DettaglioContrattoOnesimForm

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(DettaglioContrattoOnesimInline, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'gettone_agente':
            field.widget = EuroInput()
            field.label = 'Perc. Agente'
        elif db_field.name == 'gettone_mastertraining':
            field.widget = EuroInput()
            field.label = 'Perc. Master'
        elif db_field.name == 'storno_agente':
            field.widget = EuroInput()
        return field

# ---------------------------------------------------------
# MATERIALE CONTRATTO
# ---------------------------------------------------------


class MaterialeContrattoMastercomInline(admin.TabularInline):
    model = MaterialeContratto
    form = forms.MaterialeContrattoMastercomForm
    readonly_fields = ('prezzo_listino', 'totale_vendita')
    suit_classes = 'suit-tab suit-tab-materiale'


class MaterialeContrattoReadOnlyInline(admin.TabularInline):
    model = MaterialeContratto
    readonly_fields = ('prodotto', 'quantita', 'prezzo_vendita', 'prezzo_listino', 'totale_vendita')
    extra = 0
    can_delete = False
    max_num = 0
    suit_classes = 'suit-tab suit-tab-materiale'

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class MaterialeContrattoInline(admin.TabularInline):
    model = MaterialeContratto
    form = forms.MaterialeContrattoForm
    suit_classes = 'suit-tab suit-tab-materiale'
    suit_edit_link = True


# ---------------------------------------------------------
# CONTRATTI
# ---------------------------------------------------------

class ContrattoAdmin(MastergestAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'fornitore', 'categoria',
        'display_data_consegna_contratto', 'data_inizio_rate',
        'get_link_offerta', 'get_elenco_link_allegati', 'get_elenco_canoni',
        'get_elenco_materiale_display'
    )
    list_filter = (
        'agente', 'fornitore', 'categoria', 'stato',
        'ha_canoni_attivi', 'data_inizio_rate',
    )
    search_fields = (
        'azienda', 'agente__cognome', 'note', 'fornitore', 'id',
        'sede__descrizione', 'sede__anagrafica__ragione_sociale',
        'sede__anagrafica__alias',
    )
    list_max_show_all = 10000
    date_hierarchy = 'data_consegna'
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    ('sede', 'provincia',),
                    'gestione_azienda',
                )
            )
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                )
            )
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    ('categoria', 'forma_contratto',),
                    ('tipo_installazione', 'sopralluogo',),
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Dati ADSL', dict(
                classes=('collapse suit-tab suit-tab-testata',),
                fields=(
                    ('account', 'tecnologia',),
                ),
            )
        ),
        (
            'Fatturazione', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('fatturato', 'data_fatturazione', 'pagato'),
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('stato', 'motivazione_stato',),
                ),
            )
        ),
        (
            'Valore contratto', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'valore_contratto', 'numero_mesi_valore_contratto',
                    'get_totale_valore_contratto'
                ),
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    ('data_inizio_rate', 'stato_installazione',),
                ),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=('note', 'note_stato'),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile',
                    'totale_rate_master',
                    ('importo_rata_mensile_agente', 'premio_rata_mensile_agente'),
                    'totale_rate_agente'
                ),
            )
        ),
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente',
        'inserita_pda', 'fatturato', 'totale_prodotti',
        'gettone_totale_mastertraining', 'gettone_totale_agente',
        'gettone_valore_contratto', 'gettone_valore_contratto_master',
        'totale_rate_master', 'totale_rate_agente',
        'get_totale_valore_contratto', 'stato_installazione',
        'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoInline,
        AttachmentInlinesContrattiAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoInline,
        CanoneStackedInline
    ]
    form = forms.ContrattoForm
    actions = ['export_to_csv', 'export_riepilogo', 'export_riepilogo_xls']
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('dettaglio', 'Dettaglio'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('materiale', 'Materiale'),
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/emailnuovocontratto/$',
                self.admin_site.admin_view(self.email_nuovo_contratto),
                name='%s_%s_emailnuovocontratto' % info
            ),
            url(
                r'^(.+)/generacanone/$',
                self.admin_site.admin_view(self.genera_canone),
                name='%s_%s_generacanone' % info
            ),
        ]
        url_patterns += super(ContrattoAdmin, self).get_urls()
        return url_patterns

    def email_nuovo_contratto(self, request, object_id):
        contratto = Contratto.objects.get(pk=unquote(object_id))
        if not contratto:
            raise Http404()
        contratto.email_contratto_inserito()
        msg = "Email per nuovo contratto inviata correttamente!"
        messages.success(request, msg)
        fornitore = contratto.fornitore
        if contratto.fornitore == '3italia':
            fornitore = '3'
        return HttpResponseRedirect('/contratti/contratto%s/%s' % (fornitore, contratto.pk))

    def genera_canone(self, request, object_id):
        contratto = Contratto.objects.get(pk=unquote(object_id))
        if not contratto:
            raise Http404()
        messaggio = crea_canone_contratto(contratto)
        if messaggio.startswith('ERRORE'):
            messages.error(request, messaggio)
        else:
            messages.success(request, messaggio)
        fornitore = contratto.fornitore
        if contratto.fornitore == '3italia':
            fornitore = '3'
        return HttpResponseRedirect('/contratti/contratto%s/%s' % (fornitore, contratto.pk))

    def save_formset(self, request, form, formset, change):
        instances = formset.save()
        for instance in instances:
            if isinstance(instance, Attachment):
                if not instance.creator:
                    instance.creator = request.user
            instance.save()

    def display_data_consegna_contratto(self, obj):
        return obj.data_consegna_contratto.strftime('%d/%m/%Y')
    display_data_consegna_contratto.short_description = 'Cons.'
    display_data_consegna_contratto.admin_order_field = 'data_consegna_contratto'

    def display_data_competenza(self, obj):
        return obj.data_consegna.strftime('%d/%m/%Y')
    display_data_competenza.short_description = 'Comp.'
    display_data_competenza.admin_order_field = 'data_consegna'

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_contratti.csv"'
        writer = csv.writer(response)
        writer.writerow(
            [
                'id',
                'azienda',
                'sede',
                'provincia',
                'agente',
                'fornitore',
                'categoria',
                'tipo installazione',
                'stipula',
                'consegna',
                'competenza',
                'inserimento pda',
                'attivazione',
                'gettone MT',
                'accelerazione',
                'valore',
                'quantita',
                'gettone valore master',
                'costo materiale',
                'totale MT',
                'gettone agente',
                'gettone valore agente',
                'premio agente',
                'totale agente',
                'utile',
                'totale prodotti',
                'numero rate',
                'data inizio rate',
                'importo rata mensile master',
                'totale valore rate master',
                'importo rata mensile agente',
                'totale valore rate agente',
                'valore contratto',
                'num. mesi contratto',
                'totale valore contratto',
                'referente',
                'ruolo_referente',
                'telefono_referente',
                'email_referente',
                'cessato',
                'data_cessazione',
            ]
        )
        for contratto in queryset:
            writer.writerow(
                [
                    contratto.id,
                    contratto.azienda,
                    '%s' % contratto.sede,
                    contratto.provincia,
                    contratto.agente,
                    contratto.fornitore,
                    contratto.categoria,
                    contratto.get_tipo_installazione_display(),
                    contratto.data_stipula,
                    contratto.data_consegna_contratto,
                    contratto.data_consegna,
                    contratto.data_inserimento_pda,
                    contratto.data_attivazione,
                    int(contratto.gettone_totale_mastertraining),
                    int(contratto.gara_accelarazione),
                    int(contratto.gara_valore),
                    int(contratto.gettone_quantita),
                    int(contratto.gettone_valore_contratto_master),
                    int(contratto.costo_materiale),
                    int(contratto.totale_mastertraining),
                    int(contratto.gettone_totale_agente),
                    int(contratto.premio_agente),
                    int(contratto.gettone_valore_contratto),
                    int(contratto.totale_agente),
                    int(contratto.totale_mastertraining - contratto.totale_agente),
                    int(contratto.totale_prodotti),
                    int(contratto.numero_rate),
                    contratto.data_inizio_rate,
                    int(contratto.importo_rata_mensile),
                    int(contratto.totale_rate_master),
                    int(contratto.importo_rata_mensile_agente),
                    int(contratto.totale_rate_agente),
                    int(contratto.valore_contratto),
                    int(contratto.numero_mesi_valore_contratto),
                    int(contratto.get_totale_valore_contratto()),
                    contratto.referente,
                    contratto.ruolo_referente,
                    contratto.telefono_referente,
                    contratto.email_referente,
                    contratto.cessato,
                    contratto.data_cessazione,
                ]
            )
        return response
    export_to_csv.short_description = 'Esporta Contratti'

    def crea_foglio_dettaglio_riepilogo(self, book, queryset, fornitore, elenco_agenti, elenco_mesi):
        LARGHEZZA_COLONNA_AGENTI = 9000
        LARGHEZZA_COLONNE_STANDARD = 3800
        LARGHEZZA_COLONNE_TOTALI = 4300
        # ------------ Bordo marcato
        borders = xlwt.Borders()  # Create Borders
        borders.left = xlwt.Borders.THIN
        borders.right = xlwt.Borders.THIN
        borders.top = xlwt.Borders.THIN
        borders.bottom = xlwt.Borders.THIN
        borders.left_colour = 0x40
        borders.right_colour = 0x40
        borders.top_colour = 0x40
        borders.bottom_colour = 0x40
        # ------------- Allineamento a destra
        alignment = xlwt.Alignment()  # Create Alignment
        alignment.horz = xlwt.Alignment.HORZ_CENTER
        alignment.vert = xlwt.Alignment.VERT_CENTER
        default_style = xlwt.XFStyle()  # Create Style
        default_style.alignment = alignment  # Add Alignment to Style
        default_style.borders = borders
        default_pattern = default_style.pattern
        # ------------- Stile per Valuta
        aligned_right_style = xlwt.XFStyle()
        aligned_right = xlwt.Alignment()
        aligned_right.horz = xlwt.Alignment.HORZ_RIGHT
        aligned_right.vert = xlwt.Alignment.VERT_CENTER
        aligned_right_style.alignment = aligned_right
        aligned_right_style.borders = borders
        aligned_right_style.num_format_str = '[$%s-410] #,##0.00;[RED]-[$%s-410] #,##0.00' % (SIMBOLO_EURO, SIMBOLO_EURO)
        # ------------- Background Grigio
        grey_pattern = xlwt.Pattern()  # Create the Pattern
        grey_pattern.pattern = xlwt.Pattern.SOLID_PATTERN  # May be: NO_PATTERN, SOLID_PATTERN, or 0x00 through 0x12
        grey_pattern.pattern_fore_colour = 22
        # ------------- Background Giallo
        yellow_pattern = xlwt.Pattern()  # Create the Pattern
        yellow_pattern.pattern = xlwt.Pattern.SOLID_PATTERN
        yellow_pattern.pattern_fore_colour = 5
        # ------------- Stile per Valuta Totali
        totali_style = xlwt.XFStyle()
        totali_style.alignment = aligned_right
        totali_style.borders = borders
        totali_style.num_format_str = '[$%s-410] #,##0.00;[RED]-[$%s-410] #,##0.00' % (SIMBOLO_EURO, SIMBOLO_EURO)
        totali_style.pattern = yellow_pattern
        # ------------- Stile Titolo
        title_style = xlwt.XFStyle()
        my_font = xlwt.Font()  # Create Font
        my_font.bold = True  # Set font to Bold
        title_style.font = my_font
        title_style.alignment = alignment
        title_style.borders = borders
        title_style.pattern = grey_pattern
        # -------------------------------------------------------
        sheet = book.add_sheet('dettaglio %s' % fornitore)
        elenco_contratti_mese = dict()
        rate = False
        if fornitore == 'rate':
            rate = True
            fornitore = 'mastervoice'
        for agente in elenco_agenti:
            for mese in elenco_mesi:
                if fornitore == 'totale':
                    elenco_contratti = queryset.filter(
                        data_consegna_contratto__month=mese.month,
                        data_consegna_contratto__year=mese.year,
                        agente=agente
                    )
                else:
                    elenco_contratti = queryset.filter(
                        data_consegna_contratto__month=mese.month,
                        data_consegna_contratto__year=mese.year,
                        agente=agente,
                        fornitore=fornitore
                    )
                totale_mastertraining = 0
                totale_agente = 0
                totale_utile = 0
                totale_fatturato = 0
                totale_rate_agente = 0
                totale_rate_master = 0
                totale_rate_utile = 0
                for contratto in elenco_contratti:
                    totale_mastertraining += int(contratto.totale_mastertraining)
                    totale_agente += int(contratto.totale_agente)
                    totale_utile += int(contratto.totale_mastertraining - contratto.totale_agente)
                    totale_fatturato += int(contratto.totale_mastertraining + contratto.costo_materiale)
                    totale_rate_master += int(contratto.totale_rate_master)
                    totale_rate_agente += int(contratto.totale_rate_agente)
                    totale_rate_utile += int(contratto.totale_rate_master - contratto.totale_rate_agente)
                if totale_mastertraining == 0:
                    totale_mastertraining = ''
                if totale_agente == 0:
                    totale_agente = ''
                if totale_utile == 0:
                    totale_utile = ''
                if totale_fatturato == 0:
                    totale_fatturato = ''
                if totale_rate_agente == 0:
                    totale_rate_agente = ''
                if totale_rate_master == 0:
                    totale_rate_master = ''
                if totale_rate_utile == 0:
                    totale_rate_utile = ''
                elenco_contratti_mese[(agente, mese, 'totale_mastertraining')] = totale_mastertraining
                elenco_contratti_mese[(agente, mese, 'totale_agente')] = totale_agente
                elenco_contratti_mese[(agente, mese, 'totale_utile')] = totale_utile
                elenco_contratti_mese[(agente, mese, 'totale_fatturato')] = totale_fatturato
                elenco_contratti_mese[(agente, mese, 'totale_rate_master')] = totale_rate_master
                elenco_contratti_mese[(agente, mese, 'totale_rate_agente')] = totale_rate_agente
                elenco_contratti_mese[(agente, mese, 'totale_rate_utile')] = totale_rate_utile
        # --------------------- MASTERTRAINING --------------------------------
        # TESTATA
        sheet.col(0).width = LARGHEZZA_COLONNA_AGENTI
        colonna_corrente = 0
        riga_corrente = 0
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE MT / AGENTE', style=title_style)
        colonna_corrente += 1
        for mese in elenco_mesi:
            sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_STANDARD
            sheet.write(riga_corrente, colonna_corrente, '%s - %s' % (mese.year, mese.month), style=title_style)
            colonna_corrente += 1
        sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_STANDARD
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE AGENTE', style=title_style)
        # RIGHE
        colonna_corrente = 0
        riga_corrente += 1
        for agente in elenco_agenti:
            colonna_corrente = 0
            sheet.write(
                riga_corrente,
                colonna_corrente,
                '%s %s' % (agente.cognome, agente.nome),
                style=title_style
            )
            colonna_corrente += 1
            for mese in elenco_mesi:
                nome_totale = 'totale_mastertraining'
                if rate:
                    nome_totale = 'totale_rate_master'
                sheet.write(
                    riga_corrente,
                    colonna_corrente,
                    elenco_contratti_mese[(agente, mese, nome_totale)],
                    style=aligned_right_style
                )
                colonna_corrente += 1
            cella_inizio = rowcol_to_cell(riga_corrente, 1)
            cella_fine = rowcol_to_cell(riga_corrente, colonna_corrente - 1)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
            riga_corrente += 1
        # TOTALI
        colonna_corrente = 0
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE MENSILE', style=title_style)
        colonna_corrente += 1
        for mese in elenco_mesi:
            cella_inizio = rowcol_to_cell(1, colonna_corrente)
            cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
            colonna_corrente += 1
        cella_inizio = rowcol_to_cell(1, colonna_corrente)
        cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
        sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
        # --------------------- AGENTE --------------------------------
        # TESTATA
        colonna_corrente = 0
        riga_corrente += 4
        riga_inizio_agente = riga_corrente + 1
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE AG / AGENTE', style=title_style)
        colonna_corrente += 1
        for mese in elenco_mesi:
            sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_STANDARD
            sheet.write(riga_corrente, colonna_corrente, '%s - %s' % (mese.year, mese.month), style=title_style)
            colonna_corrente += 1
        sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_TOTALI
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE AGENTE', style=title_style)
        # RIGHE
        colonna_corrente = 0
        riga_corrente += 1
        for agente in elenco_agenti:
            colonna_corrente = 0
            sheet.write(riga_corrente, colonna_corrente, '%s %s' % (agente.cognome, agente.nome), style=title_style)
            colonna_corrente += 1
            for mese in elenco_mesi:
                nome_totale = 'totale_agente'
                if rate:
                    nome_totale = 'totale_rate_agente'
                sheet.write(riga_corrente, colonna_corrente, elenco_contratti_mese[(agente, mese, nome_totale)], style=aligned_right_style)
                colonna_corrente += 1
            cella_inizio = rowcol_to_cell(riga_corrente, 1)
            cella_fine = rowcol_to_cell(riga_corrente, colonna_corrente - 1)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
            riga_corrente += 1
        # TOTALI
        colonna_corrente = 0
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE MENSILE', style=title_style)
        colonna_corrente += 1
        for mese in elenco_mesi:
            cella_inizio = rowcol_to_cell(riga_inizio_agente, colonna_corrente)
            cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
            colonna_corrente += 1
        cella_inizio = rowcol_to_cell(riga_inizio_agente, colonna_corrente)
        cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
        sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
        # --------------------- UTILE --------------------------------
        # TESTATA
        colonna_corrente = 0
        riga_corrente += 4
        riga_inizio_utile = riga_corrente + 1
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE UTILE / AGENTE', style=title_style)
        colonna_corrente += 1
        for mese in elenco_mesi:
            sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_STANDARD
            sheet.write(riga_corrente, colonna_corrente, '%s - %s' % (mese.year, mese.month), style=title_style)
            colonna_corrente += 1
        sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_TOTALI
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE AGENTE', style=title_style)
        # RIGHE
        colonna_corrente = 0
        riga_corrente += 1
        for agente in elenco_agenti:
            colonna_corrente = 0
            sheet.write(riga_corrente, colonna_corrente, '%s %s' % (agente.cognome, agente.nome), style=title_style)
            colonna_corrente += 1
            for mese in elenco_mesi:
                nome_totale = 'totale_utile'
                if rate:
                    nome_totale = 'totale_rate_utile'
                sheet.write(riga_corrente, colonna_corrente, elenco_contratti_mese[(agente, mese, nome_totale)], style=aligned_right_style)
                colonna_corrente += 1
            cella_inizio = rowcol_to_cell(riga_corrente, 1)
            cella_fine = rowcol_to_cell(riga_corrente, colonna_corrente - 1)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
            riga_corrente += 1
        # TOTALI
        colonna_corrente = 0
        sheet.write(riga_corrente, colonna_corrente, 'TOTALE MENSILE', style=title_style)
        colonna_corrente += 1
        for mese in elenco_mesi:
            cella_inizio = rowcol_to_cell(riga_inizio_utile, colonna_corrente)
            cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
            colonna_corrente += 1
        cella_inizio = rowcol_to_cell(riga_inizio_utile, colonna_corrente)
        cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
        sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
        if not rate:
            # --------------------- FATTURATO MT --------------------------------
            # TESTATA
            colonna_corrente += 2
            colonna_inizio_fatturato = colonna_corrente
            riga_corrente = 0
            riga_inizio_fatturato = riga_corrente + 1
            sheet.col(colonna_corrente).width = LARGHEZZA_COLONNA_AGENTI
            sheet.write(riga_corrente, colonna_corrente, 'TOTALE FATTURATO MT / AGENTE', style=title_style)
            colonna_corrente += 1
            for mese in elenco_mesi:
                sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_STANDARD
                sheet.write(riga_corrente, colonna_corrente, '%s - %s' % (mese.year, mese.month), style=title_style)
                colonna_corrente += 1
            sheet.col(colonna_corrente).width = LARGHEZZA_COLONNE_TOTALI
            sheet.write(riga_corrente, colonna_corrente, 'TOTALE AGENTE', style=title_style)
            # RIGHE
            colonna_corrente = colonna_inizio_fatturato
            riga_corrente += 1
            for agente in elenco_agenti:
                colonna_corrente = colonna_inizio_fatturato
                sheet.write(riga_corrente, colonna_corrente, '%s %s' % (agente.cognome, agente.nome), style=title_style)
                colonna_corrente += 1
                for mese in elenco_mesi:
                    sheet.write(riga_corrente, colonna_corrente, elenco_contratti_mese[(agente, mese, 'totale_fatturato')], style=aligned_right_style)
                    colonna_corrente += 1
                cella_inizio = rowcol_to_cell(riga_corrente, colonna_inizio_fatturato)
                cella_fine = rowcol_to_cell(riga_corrente, colonna_corrente - 1)
                sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
                riga_corrente += 1
            # TOTALI
            colonna_corrente = colonna_inizio_fatturato
            sheet.write(riga_corrente, colonna_corrente, 'TOTALE MENSILE', style=title_style)
            colonna_corrente += 1
            for mese in elenco_mesi:
                cella_inizio = rowcol_to_cell(riga_inizio_fatturato, colonna_corrente)
                cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
                sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)
                colonna_corrente += 1
            cella_inizio = rowcol_to_cell(riga_inizio_fatturato, colonna_corrente)
            cella_fine = rowcol_to_cell(riga_corrente - 1, colonna_corrente)
            sheet.write(riga_corrente, colonna_corrente, xlwt.Formula('SUM(%s:%s)' % (cella_inizio, cella_fine)), style=totali_style)

    def export_riepilogo_xls(self, request, queryset):
        data_corrente = timezone.now()
        # Creo Foglio di Excel
        book = xlwt.Workbook(encoding='utf8')
        elenco_mesi = queryset.dates('data_consegna_contratto', 'month')
        elenco_agenti = Agente.objects.all()
        self.crea_foglio_dettaglio_riepilogo(book, queryset, 'fastweb', elenco_agenti, elenco_mesi)
        self.crea_foglio_dettaglio_riepilogo(book, queryset, 'wind', elenco_agenti, elenco_mesi)
        self.crea_foglio_dettaglio_riepilogo(book, queryset, 'vodafone', elenco_agenti, elenco_mesi)
        # self.crea_foglio_dettaglio_riepilogo(book, queryset, '3italia', elenco_agenti, elenco_mesi)
        # self.crea_foglio_dettaglio_riepilogo(book, queryset, 'tim', elenco_agenti, elenco_mesi)
        self.crea_foglio_dettaglio_riepilogo(book, queryset, 'mastervoice', elenco_agenti, elenco_mesi)
        self.crea_foglio_dettaglio_riepilogo(book, queryset, 'rate', elenco_agenti, elenco_mesi)
        self.crea_foglio_dettaglio_riepilogo(book, queryset, 'totale', elenco_agenti, elenco_mesi)
        response = HttpResponse(content_type='application/vnd.ms-excel')
        response['Content-Disposition'] = 'attachment; filename="export_riepilogo_%s.csv"' % data_corrente
        book.save(response)
        return response
    export_riepilogo_xls.short_description = 'Esporta Riepilogo Contratti XLS'

    def export_riepilogo(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        data_corrente = timezone.now()
        response['Content-Disposition'] = 'attachment;filename="export_riepilogo_%s.csv"' % data_corrente
        writer = csv.writer(response)
        elenco_mesi = queryset.dates('data_consegna_contratto', 'month')
        elenco_agenti = Agente.objects.all()
        # --------------- MASTERTRAINING ------------------
        # TESTATA
        riga_testata = []
        riga_testata.append('TOTALE MT / AGENTE')
        for mese in elenco_mesi:
            riga_testata.append('%s - %s' % (mese.year, mese.month))
        riga_testata.append('TOTALE RIGA')
        writer.writerow(riga_testata)
        # RIGHE
        for agente in elenco_agenti:
            totale_riga = 0
            riga_agente = []
            riga_agente.append(agente)
            for mese in elenco_mesi:
                elenco_contratti_mese = queryset.filter(
                    data_consegna_contratto__month=mese.month,
                    data_consegna_contratto__year=mese.year,
                    agente=agente
                )
                totale_mastertraining = 0
                for contratto in elenco_contratti_mese:
                    totale_mastertraining += int(contratto.totale_mastertraining)
                totale_riga += totale_mastertraining
                if totale_mastertraining == 0:
                    totale_mastertraining = ''
                riga_agente.append(totale_mastertraining)
            riga_agente.append(totale_riga)
            writer.writerow(riga_agente)
        # TOTALI
        riga_totali = []
        riga_totali.append('TOTALI')
        totale_riga = 0
        for mese in elenco_mesi:
            elenco_contratti_totale = queryset.filter(data_consegna_contratto__month=mese.month, data_consegna_contratto__year=mese.year)
            totale_mastertraining = 0
            for contratto in elenco_contratti_totale:
                totale_mastertraining += int(contratto.totale_mastertraining)
            totale_riga += totale_mastertraining
            if totale_mastertraining == 0:
                totale_mastertraining = ''
            riga_totali.append(totale_mastertraining)
        riga_totali.append(totale_riga)
        writer.writerow(riga_totali)
        writer.writerow('')
        writer.writerow('')
        # --------------- AGENTI ------------------
        # TESTATA
        riga_testata = []
        riga_testata.append('TOTALE AG / AGENTE')
        totale_riga = 0
        for mese in elenco_mesi:
            riga_testata.append('%s - %s' % (mese.year, mese.month))
        riga_testata.append('TOTALE RIGA')
        writer.writerow(riga_testata)
        # RIGHE
        for agente in elenco_agenti:
            totale_riga = 0
            riga_agente = []
            riga_agente.append(agente)
            for mese in elenco_mesi:
                elenco_contratti_mese = queryset.filter(
                    data_consegna_contratto__month=mese.month,
                    data_consegna_contratto__year=mese.year,
                    agente=agente
                )
                totale_agente = 0
                for contratto in elenco_contratti_mese:
                    totale_agente += int(contratto.totale_agente)
                totale_riga += totale_agente
                if totale_agente == 0:
                    totale_agente = ''
                riga_agente.append(totale_agente)
            riga_agente.append(totale_riga)
            writer.writerow(riga_agente)
        # TOTALI
        riga_totali = []
        riga_totali.append('TOTALI')
        totale_riga = 0
        for mese in elenco_mesi:
            elenco_contratti_totale = queryset.filter(data_consegna_contratto__month=mese.month, data_consegna_contratto__year=mese.year)
            totale_agente = 0
            for contratto in elenco_contratti_totale:
                totale_agente += int(contratto.totale_agente)
            totale_riga += totale_agente
            if totale_agente == 0:
                totale_agente = ''
            riga_totali.append(totale_agente)
        riga_totali.append(totale_riga)
        writer.writerow(riga_totali)
        writer.writerow('')
        writer.writerow('')
        # --------------- UTILE ------------------
        # TESTATA
        riga_testata = []
        riga_testata.append('TOTALE UTILE / AGENTE')
        totale_riga = 0
        for mese in elenco_mesi:
            riga_testata.append('%s - %s' % (mese.year, mese.month))
        riga_testata.append('TOTALE RIGA')
        writer.writerow(riga_testata)
        # RIGHE
        for agente in elenco_agenti:
            totale_riga = 0
            riga_agente = []
            riga_agente.append(agente)
            for mese in elenco_mesi:
                elenco_contratti_mese = queryset.filter(
                    data_consegna_contratto__month=mese.month,
                    data_consegna_contratto__year=mese.year,
                    agente=agente
                )
                totale_utile = 0
                for contratto in elenco_contratti_mese:
                    totale_utile += int(contratto.totale_mastertraining - contratto.totale_agente)
                totale_riga += totale_utile
                if totale_utile == 0:
                    totale_utile = ''
                riga_agente.append(totale_utile)
            riga_agente.append(totale_riga)
            writer.writerow(riga_agente)
        # TOTALI
        riga_totali = []
        riga_totali.append('TOTALI')
        totale_riga = 0
        for mese in elenco_mesi:
            elenco_contratti_totale = queryset.filter(data_consegna_contratto__month=mese.month, data_consegna_contratto__year=mese.year)
            totale_utile = 0
            for contratto in elenco_contratti_totale:
                totale_utile += int(contratto.totale_mastertraining - contratto.totale_agente)
            totale_riga += totale_utile
            if totale_utile == 0:
                totale_utile = ''
            riga_totali.append(totale_utile)
        riga_totali.append(totale_riga)
        writer.writerow(riga_totali)
        writer.writerow('')
        writer.writerow('')
        # --------------- RATE MASTER ------------------
        # TESTATA
        riga_testata = []
        riga_testata.append('TOTALE RATE MASTER / AGENTE')
        totale_riga = 0
        for mese in elenco_mesi:
            riga_testata.append('%s - %s' % (mese.year, mese.month))
        riga_testata.append('TOTALE RIGA')
        writer.writerow(riga_testata)
        # RIGHE
        for agente in elenco_agenti:
            totale_riga = 0
            riga_agente = []
            riga_agente.append(agente)
            for mese in elenco_mesi:
                elenco_contratti_mese = queryset.filter(
                    data_consegna_contratto__month=mese.month,
                    data_consegna_contratto__year=mese.year,
                    agente=agente
                )
                totale_rate = 0
                for contratto in elenco_contratti_mese:
                    totale_rate += int(contratto.totale_rate_master)
                totale_riga += totale_rate
                if totale_rate == 0:
                    totale_rate = ''
                riga_agente.append(totale_rate)
            riga_agente.append(totale_riga)
            writer.writerow(riga_agente)
        # TOTALI
        riga_totali = []
        riga_totali.append('TOTALI')
        totale_riga = 0
        for mese in elenco_mesi:
            elenco_contratti_totale = queryset.filter(data_consegna_contratto__month=mese.month, data_consegna_contratto__year=mese.year)
            totale_rate = 0
            for contratto in elenco_contratti_totale:
                totale_rate += int(contratto.totale_rate_master)
            totale_riga += totale_rate
            if totale_rate == 0:
                totale_rate = ''
            riga_totali.append(totale_rate)
        riga_totali.append(totale_riga)
        writer.writerow(riga_totali)
        writer.writerow('')
        writer.writerow('')
        # --------------- RATE AGENTE ------------------
        # TESTATA
        riga_testata = []
        riga_testata.append('TOTALE RATE AGENTE / AGENTE')
        totale_riga = 0
        for mese in elenco_mesi:
            riga_testata.append('%s - %s' % (mese.year, mese.month))
        riga_testata.append('TOTALE RIGA')
        writer.writerow(riga_testata)
        # RIGHE
        for agente in elenco_agenti:
            totale_riga = 0
            riga_agente = []
            riga_agente.append(agente)
            for mese in elenco_mesi:
                elenco_contratti_mese = queryset.filter(
                    data_consegna_contratto__month=mese.month,
                    data_consegna_contratto__year=mese.year,
                    agente=agente
                )
                totale_rate = 0
                for contratto in elenco_contratti_mese:
                    totale_rate += int(contratto.totale_rate_agente)
                totale_riga += totale_rate
                if totale_rate == 0:
                    totale_rate = ''
                riga_agente.append(totale_rate)
            riga_agente.append(totale_riga)
            writer.writerow(riga_agente)
        # TOTALI
        riga_totali = []
        riga_totali.append('TOTALI')
        totale_riga = 0
        for mese in elenco_mesi:
            elenco_contratti_totale = queryset.filter(data_consegna_contratto__month=mese.month, data_consegna_contratto__year=mese.year)
            totale_rate = 0
            for contratto in elenco_contratti_totale:
                totale_rate += int(contratto.totale_rate_agente)
            totale_riga += totale_rate
            if totale_rate == 0:
                totale_rate = ''
            riga_totali.append(totale_rate)
        riga_totali.append(totale_riga)
        writer.writerow(riga_totali)
        writer.writerow('')
        writer.writerow('')
        # --------------- RATE UTILE ------------------
        # TESTATA
        riga_testata = []
        riga_testata.append('TOTALE UTILE RATE / AGENTE')
        totale_riga = 0
        for mese in elenco_mesi:
            riga_testata.append('%s - %s' % (mese.year, mese.month))
        riga_testata.append('TOTALE RIGA')
        writer.writerow(riga_testata)
        # RIGHE
        for agente in elenco_agenti:
            totale_riga = 0
            riga_agente = []
            riga_agente.append(agente)
            for mese in elenco_mesi:
                elenco_contratti_mese = queryset.filter(
                    data_consegna_contratto__month=mese.month,
                    data_consegna_contratto__year=mese.year,
                    agente=agente
                )
                totale_rate = 0
                for contratto in elenco_contratti_mese:
                    totale_rate += int(contratto.totale_rate_master - contratto.totale_rate_agente)
                totale_riga += totale_rate
                if totale_rate == 0:
                    totale_rate = ''
                riga_agente.append(totale_rate)
            riga_agente.append(totale_riga)
            writer.writerow(riga_agente)
        # TOTALI
        riga_totali = []
        riga_totali.append('TOTALI')
        totale_riga = 0
        for mese in elenco_mesi:
            elenco_contratti_totale = queryset.filter(data_consegna_contratto__month=mese.month, data_consegna_contratto__year=mese.year)
            totale_rate = 0
            for contratto in elenco_contratti_totale:
                totale_rate += int(contratto.totale_rate_master - contratto.totale_rate_agente)
            totale_riga += totale_rate
            if totale_rate == 0:
                totale_rate = ''
            riga_totali.append(totale_rate)
        riga_totali.append(totale_riga)
        writer.writerow(riga_totali)
        return response
    export_riepilogo.short_description = 'Esporta Riepilogo Contratti'

    def change_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, unquote(object_id))
        if obj:
            extra_context = extra_context or dict()
            elenco_compensi = Compenso.objects.filter(contratto=obj)
            extra_context['lista_compensi'] = elenco_compensi
        return super(ContrattoAdmin, self).change_view(request, object_id, extra_context=extra_context)


class ContrattoReadOnlyInlineAdmin(admin.TabularInline):
    fields = (
        'get_link_display', 'fornitore', 'agente',
        'get_elenco_materiale_display', 'get_elenco_link_allegati'
    )
    readonly_fields = (
        'agente', 'fornitore', 'get_elenco_materiale_display',
        'get_link_display', 'get_elenco_link_allegati'
    )
    model = Contratto
    extra = 0
    can_delete = False
    max_num = 0
    form = forms.ContrattoForm
    suit_classes = 'suit-tab suit-tab-contratti'

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class ContrattoFastwebAdmin(ContrattoAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto', 'data_consegna',
        'data_inserimento_pda', 'data_fatturazione', 'account', 'tecnologia',
        'get_gettone_totale_mastertraining_display', 'gara_accelarazione',
        'gara_valore', 'gettone_quantita', 'get_totale_mastertraining_display',
        'get_gettone_totale_agente_display', 'premio_agente',
        'get_totale_agente_display', 'pagato', 'stato'
    )
    list_editable = (
        'gara_accelarazione', 'gara_valore', 'gettone_quantita',
        'premio_agente', 'data_inserimento_pda', 'data_fatturazione', 'account'
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'gettone_valore_contratto',
        'gettone_valore_contratto_master', 'gettone_totale_agente',
        'fornitore', 'get_totale_valore_contratto'
    )
    fieldsets = (
        (
            None, {
                'fields': (
                    ('azienda', 'provincia', 'agente', 'fornitore', ),
                    ('progetto', 'account', 'tecnologia', 'project_manager'),
                    ('data_stipula', 'data_consegna_contratto', 'data_consegna', 'inserita_pda', 'data_inserimento_pda'),
                )
            }
        ),
        (
            'Valore contratto', {
                'fields': (
                    ('valore_contratto', 'numero_mesi_valore_contratto', 'get_totale_valore_contratto'),
                )
            }
        ),
        (
            'Provvigioni Mastertraining', {
                'fields': (
                    (
                        'gettone_totale_mastertraining', 'gara_accelarazione',
                        'gara_valore', 'gettone_quantita',
                        'gettone_valore_contratto_master', 'costo_materiale',
                        'totale_mastertraining'
                    ),
                )
            }
        ),
        (
            'Provvigioni Agente', {
                'fields': (
                    ('gettone_totale_agente', 'premio_agente', 'gettone_valore_contratto', 'totale_agente',),
                )
            }
        ),
        (
            'Stato', {
                'fields': (
                    ('fatturato', 'data_fatturazione', 'pagato'),
                    ('stato', 'motivazione_stato'),
                )
            }
        ),
        (
            'Note Stato Contratto', {
                'classes': ('collapse', ),
                'fields': ('note_stato', ),
            }
        ),
        (
            'Note', {
                'classes': ('collapse', ),
                'fields': ('note', ),
            }
        ),
    )
    inlines = [DettaglioContrattoFastwebInline, AttachmentInlinesAddOnly, AttachmentInlinesNoAdd]


class ContrattoFastwebResellerAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_link_preordine',
        'get_elenco_link_allegati', 'get_elenco_canoni',
        'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                )
            )
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                )
            )
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'cessato', 
                    'data_cessazione',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                    'data_inizio_rate',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoFastwebResellerInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoWelcomeAdmin(ContrattoAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto', 'data_consegna',
        'data_inserimento_pda', 'data_fatturazione',
        'account', 'tecnologia', 'get_gettone_totale_mastertraining_display',
        'gara_accelarazione', 'gara_valore', 'gettone_quantita',
        'get_totale_mastertraining_display', 'get_gettone_totale_agente_display',
        'premio_agente', 'get_totale_agente_display', 'pagato', 'stato'
    )
    list_editable = (
        'gara_accelarazione', 'gara_valore', 'gettone_quantita',
        'premio_agente', 'data_inserimento_pda', 'data_fatturazione', 'account'
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente',
        'totale_prodotti', 'gettone_totale_mastertraining',
        'gettone_valore_contratto', 'gettone_valore_contratto_master',
        'gettone_totale_agente', 'fornitore', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate',
    )
    fieldsets = (
        (
            None, dict(
                fields=(
                    ('azienda', 'provincia', 'agente',),
                    ('progetto', 'account', 'tecnologia', 'project_manager'),
                    ('data_stipula', 'data_consegna_contratto', 'data_consegna', 'inserita_pda', 'data_inserimento_pda'),
                )
            )
        ),
        (
            'Valore contratto', dict(
                fields=(
                    ('valore_contratto', 'numero_mesi_valore_contratto', 'get_totale_valore_contratto'),
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                fields=(
                    (
                        'gettone_totale_mastertraining', 'gara_accelarazione',
                        'gara_valore', 'gettone_quantita',
                        'gettone_valore_contratto_master', 'costo_materiale',
                        'totale_mastertraining'
                    ),
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                fields=(
                    ('gettone_totale_agente', 'premio_agente', 'gettone_valore_contratto', 'totale_agente',),
                )
            )
        ),
        (
            'Stato', dict(
                fields=(
                    ('fatturato', 'data_fatturazione', 'pagato'),
                    ('stato', 'motivazione_stato'),
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                fields=(
                    'stato_installazione',
                    'data_inizio_rate',
                ),
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('collapse',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('collapse',),
                fields=('note',),
            )
        ),
    )
    inlines = [DettaglioContrattoWelcomeInline, AttachmentInlinesAddOnly, AttachmentInlinesNoAdd]


class ContrattoNgiAdmin(ContrattoAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto', 'data_consegna', 'data_inserimento_pda', 'data_fatturazione',
        'account', 'tecnologia', 'get_gettone_totale_mastertraining_display', 'gara_accelarazione', 'gara_valore',
        'gettone_quantita', 'get_totale_mastertraining_display', 'get_gettone_totale_agente_display',
        'premio_agente', 'get_totale_agente_display', 'pagato', 'stato'
    )
    list_editable = (
        'gara_accelarazione', 'gara_valore', 'gettone_quantita',
        'premio_agente', 'data_inserimento_pda', 'data_fatturazione', 'account'
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'gettone_valore_contratto',
        'gettone_valore_contratto_master', 'gettone_totale_agente', 'fornitore',
        'get_totale_valore_contratto', 'stato_installazione', 'data_inizio_rate',
    )
    fieldsets = (
        (
            None, dict(
                fields=(
                    ('azienda', 'provincia', 'agente', 'fornitore', ),
                    ('progetto', 'account', 'tecnologia', 'project_manager'),
                    ('data_stipula', 'data_consegna_contratto', 'data_consegna', 'inserita_pda', 'data_inserimento_pda'),
                )
            )
        ),
        (
            'Valore contratto', dict(
                fields=(
                    ('valore_contratto', 'numero_mesi_valore_contratto', 'get_totale_valore_contratto'),
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                fields=(
                    (
                        'gettone_totale_mastertraining', 'gara_accelarazione',
                        'gara_valore', 'gettone_quantita',
                        'gettone_valore_contratto_master', 'costo_materiale',
                        'totale_mastertraining'
                    ),
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                fields=(
                    ('gettone_totale_agente', 'premio_agente', 'gettone_valore_contratto', 'totale_agente',),
                )
            )
        ),
        (
            'Stato', dict(
                fields=(
                    ('fatturato', 'data_fatturazione', 'pagato'),
                    ('stato', 'motivazione_stato'),
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                fields=(
                    'stato_installazione',
                    'data_inizio_rate',
                ),
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('collapse',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('collapse',),
                fields=('note',),
            )
        ),
    )
    inlines = [DettaglioContrattoNgiInline, AttachmentInlinesAddOnly, AttachmentInlinesNoAdd, CanoneStackedInline]


class ContrattoMastervoiceAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta',
        'get_elenco_link_allegati', 'get_elenco_canoni',
        'get_elenco_materiale_display', 'cessato', 'data_cessazione',
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    ('sede', 'provincia',),
                )
            )
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                )
            )
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',                    
                    ('categoria', 'forma_contratto',),
                    ('tipo_installazione', 'sopralluogo',),
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    ('cessato', 'data_cessazione',),
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('collapse suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=('note', ),
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile',
                    'totale_rate_master',
                    ('importo_rata_mensile_agente', 'premio_rata_mensile_agente'),
                    'totale_rate_agente'
                ),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoMastervoiceInline,
        MaterialeContrattoInline,
        AttachmentInlinesContrattiAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline
    ]
    actions = ['export_to_csv', 'export_riepilogo', 'export_riepilogo_xls', 'export_contratti_canoni']
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
    )

    def export_contratti_canoni(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_contratti_mv.csv"'
        writer = csv.writer(response)
        categoria_mastervoice_softwareopen = Categoria.objects.get(nome__iexact='software mastervoice open')
        categoria_mastervoice_open = Categoria.objects.get(nome__iexact='mastervoice open')
        categoria_mastermeet = Categoria.objects.get(nome__iexact='Software Mastervoice MasterMeet')
        categoria_mastermeet_pro = Categoria.objects.get(nome__iexact='Software Mastervoice MasterMeet Pro')
        writer.writerow(
            [
                'id',
                'azienda',
                'provincia',
                'agente',
                'stipula',
                'consegna',
                'competenza',
                'attivazione',
                'data inizio rate',
                'importo rata mensile agente',
                'referente',
                'ruolo_referente',
                'telefono_referente',
                'email_referente',
                'MV open: inizio ultimo canone',
                'MV open: fine ultimo canone',
                'MV open: importo ultimo canone',
                'MV open: numero rinnovi',
                'MV mastermeet: inizio ultimo canone',
                'MV mastermeet: fine ultimo canone',
                'MV mastermeet: importo ultimo canone',
                'MV mastermeet: numero rinnovi',
                'MV mastermeet pro: inizio ultimo canone',
                'MV mastermeet pro: fine ultimo canone',
                'MV mastermeet pro: importo ultimo canone',
                'MV mastermeet pro: numero rinnovi',
            ]
        )
        for contratto in queryset:
            ha_canoni = False
            elenco_canoni_open = contratto.canone_set.filter(
                categoria_canone__in=[categoria_mastervoice_open, categoria_mastervoice_softwareopen]
            ).order_by('-data_inizio')
            open_data_inizio = ''
            open_data_fine = ''
            open_importo_rata = 0
            open_numero_rinnovi = 0
            if elenco_canoni_open:
                ha_canoni = True
                open_numero_rinnovi = elenco_canoni_open.count() - 1
                for canone_open in elenco_canoni_open:
                    if canone_open.get_stato_canone_csv() in ['disdetto', 'attivo']:
                        open_data_inizio = canone_open.data_inizio
                        open_data_fine = canone_open.data_fine
                        open_importo_rata = canone_open.importo_rata
                        break
            elenco_canoni_meet = contratto.canone_set.filter(categoria_canone=categoria_mastermeet).order_by('-data_inizio')
            meet_data_inizio = ''
            meet_data_fine = ''
            meet_importo_rata = 0
            meet_numero_rinnovi = 0
            if elenco_canoni_meet:
                ha_canoni = True
                meet_numero_rinnovi = elenco_canoni_meet.count() - 1
                for canone_meet in elenco_canoni_meet:
                    if canone_meet.get_stato_canone_csv() in ['disdetto', 'attivo']:
                        meet_data_inizio = canone_meet.data_inizio
                        meet_data_fine = canone_meet.data_fine
                        meet_importo_rata = canone_meet.importo_rata
                        break
            elenco_canoni_meetpro = contratto.canone_set.filter(categoria_canone=categoria_mastermeet_pro).order_by('-data_inizio')
            meetpro_data_inizio = ''
            meetpro_data_fine = ''
            meetpro_importo_rata = 0
            meetpro_numero_rinnovi = 0
            if elenco_canoni_meetpro:
                ha_canoni = True
                meetpro_numero_rinnovi = elenco_canoni_meetpro.count() - 1
                for canone_meetpro in elenco_canoni_meetpro:
                    if canone_meetpro.get_stato_canone_csv() in ['disdetto', 'attivo']:
                        meetpro_data_inizio = canone_meetpro.data_inizio
                        meetpro_data_fine = canone_meetpro.data_fine
                        meetpro_importo_rata = canone_meetpro.importo_rata
                        break
            if ha_canoni:
                writer.writerow(
                    [
                        contratto.id,
                        contratto.azienda,
                        contratto.provincia,
                        contratto.agente,
                        contratto.data_stipula,
                        contratto.data_consegna_contratto,
                        contratto.data_consegna,
                        contratto.data_attivazione,
                        contratto.data_inizio_rate,
                        int(contratto.importo_rata_mensile_agente),
                        contratto.referente,
                        contratto.ruolo_referente,
                        contratto.telefono_referente,
                        contratto.email_referente,
                        open_data_inizio,
                        open_data_fine,
                        int(open_importo_rata),
                        open_numero_rinnovi,
                        meet_data_inizio,
                        meet_data_fine,
                        int(meet_importo_rata),
                        meet_numero_rinnovi,
                        meetpro_data_inizio,
                        meetpro_data_fine,
                        int(meetpro_importo_rata),
                        meetpro_numero_rinnovi,
                    ]
                )
        return response
    export_contratti_canoni.short_description = 'Esporta Contratti (con riepilogo canoni)'


class ContrattoIpkomAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'provincia', 'agente', 'categoria', 'data_inizio_rate',
        'data_firma_contratto', 'verifica_tecnica',
        'data_verifica_tecnica', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'categoria',
                    'data_firma_contratto',
                    'tipo_servizio',
                    'verifica_tecnica',
                    'data_verifica_tecnica',
                    'cessato', 'data_cessazione',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                fields=(
                    'stato_installazione',
                    'data_inizio_rate',
                ),
            )
        ),
        (
            'Dati Canone', dict(
                classes=('collapse suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )),
    )
    list_filter = ('agente', 'categoria', 'ha_canoni_attivi', 'cessato')
    inlines = [AttachmentInlinesContrattiAddOnly, AttachmentInlinesNoAdd, MaterialeContrattoInline, CanoneStackedInline]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('materiale', 'Materiale'),
        ('note', 'Note'),
    )


class ContrattoKpnquestAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato', 
                    'data_cessazione',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoKpnquestInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoLineaIpkomAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato', 
                    'data_cessazione',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoLineaIpkomInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoTwtAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display', 'cessato', 'data_cessazione',
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato', 
                    'data_cessazione',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile',
                    'totale_rate_master',
                    'importo_rata_mensile_agente',
                    'totale_rate_agente',
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoTwtInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoBriantelAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato', 
                    'data_cessazione',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile',
                    'totale_rate_master',
                    'importo_rata_mensile_agente',
                    'totale_rate_agente',
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoBriantelInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoNetAndWorkAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato', 
                    'data_cessazione'
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile',
                    'totale_rate_master',
                    'importo_rata_mensile_agente',
                    'totale_rate_agente',
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoNetAndWorkInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoHalAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato',
                    'data_cessazione',
                )
            )
        ),
        (
            'Dati Canone', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('tipologia_canone', 'categoria_canone', ),
                    ('periodicita_canone', 'anticipato_canone', 'con_ratino_canone'),
                    ('numero_rate_canone', 'importo_rata_canone', ),
                    ('numero_rate_rinnovo_canone', 'tipo_rinnovo_canone', ),
                    'descrizione_aggiuntiva_fattura_canone',
                )
            )
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                )
            )
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                )
            )
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                )
            )
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoHalInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoDigitelAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display',
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni', ),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                ))
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni', ),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                ))
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata', ),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni', ),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile',
                    'totale_rate_master',
                    'importo_rata_mensile_agente',
                    'totale_rate_agente',
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato', ),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note', ),
                fields=('note_stato', ),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note', ),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoDigitelInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoAcantoAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_display', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_elenco_link_allegati',
        'get_elenco_canoni', 'get_elenco_materiale_display',
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'progetto',
                    'fornitore',
                    
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                ))
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                ))
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'data_inizio_rate',
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate',
    )
    inlines = [
        DettaglioContrattoAcantoInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoMcLinkAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_filtro_link', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta', 'get_link_preordine',
        'get_elenco_link_allegati', 'get_elenco_canoni',
        'get_elenco_materiale_display', 'cessato', 'data_cessazione',
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    ('progetto', 'get_link_progetto'),
                    'fornitore',
                    
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato',
                    'data_cessazione',
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                ))
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                ))
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate', 'get_link_progetto',
    )
    inlines = [
        DettaglioContrattoMcLinkInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoTrenoveAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_display', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta',
        'get_elenco_link_allegati', 'get_elenco_canoni',
        'get_elenco_materiale_display', 'cessato', 'data_cessazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'progetto',
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                    'cessato', 
                    'data_cessazione'
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                ))
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                ))
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                ))
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = (
        'agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione',
        'ha_canoni_attivi', 'cessato',
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate',
    )
    inlines = [
        DettaglioContrattoTrenoveInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline,
        MaterialeContrattoInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('materiale', 'Materiale'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoOnesimAdmin(ContrattoAdmin):
    list_display = (
        'id', 'get_sede_display', 'agente', 'categoria',
        'display_data_consegna_contratto', 'display_data_competenza',
        'data_inizio_rate', 'get_link_offerta',
        'get_elenco_link_allegati', 'get_elenco_canoni',
        'get_elenco_materiale_display',
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'progetto',
                    'fornitore',
                    'categoria',
                    'forma_contratto',
                    'tipo_installazione',
                    'sopralluogo',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                ))
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                ))
        ),
        (
            'Collaudo/Installazione', dict(
                classes=('suit-tab suit-tab-provvigioni suit-tab-testata',),
                fields=(
                    'stato_installazione',
                    'data_inizio_rate',
                ),
            )
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'numero_rate',
                    'importo_rata_mensile', 'totale_rate_master',
                    'importo_rata_mensile_agente', 'totale_rate_agente'
                ),
            )
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                )
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    form = forms.ContrattoMastervoiceForm
    list_filter = ('agente', 'fornitore', 'stato', 'categoria', 'tipo_installazione')
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'importo_rata_mensile_agente',
        'totale_rate_agente', 'totale_rate_master', 'gettone_totale_agente',
        'fornitore', 'inserita_pda', 'fatturato', 'get_totale_valore_contratto',
        'stato_installazione', 'data_inizio_rate',
    )
    inlines = [
        DettaglioContrattoOnesimInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('dettaglio', 'Dettaglio'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )


class ContrattoMastercomAdmin(ContrattoAdmin):
    list_display = (
        'id', 'sede', 'agente', 'categoria', 'data_consegna_contratto',
        'data_consegna', 'data_fatturazione', 'pagato', 'stato'
    )
    readonly_fields = ('fornitore', )
    list_filter = ('forma_contratto', 'stato', 'ha_canoni_attivi', )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'sede',
                    'agente',
                    'forma_contratto',
                    'tipo_contratto_mastercom',
                    'categoria',
                    'progetto',
                    'project_manager',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                    'data_inizio_rate',
                )),
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-stato',),
                fields=(
                    'fatturato',
                    'data_fatturazione',
                    'pagato',
                    'stato',
                    'motivazione_stato',
                    'documentazione',
                    'note_documentazione',
                ),
            )
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )
        ),
    )
    inlines = [CanoneStackedInline, AttachmentInlinesAddOnly, AttachmentInlinesNoAdd, MaterialeContrattoMastercomInline]
    form = forms.ContrattoMastercomForm

    suit_form_tabs = (
        ('testata', 'Testata'),
        ('materiale', 'Materiale'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
        ('stato', 'Stato')
    )

    def save_model(self, request, obj, form, change):
        if not obj.azienda:
            obj.azienda = obj.sede.anagrafica.ragione_sociale
        if not obj.provincia:
            obj.provincia = obj.sede.anagrafica.get_provincia()
        return super(ContrattoMastercomAdmin, self).save_model(request, obj, form, change)

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/odtassistenza/$',
                self.admin_site.admin_view(self.stampa_assistenza),
                name='%s_%s_odtassistenza' % info
            ),
            url(
                r'^(.+)/odtinstallazione/$',
                self.admin_site.admin_view(self.stampa_installazione),
                name='%s_%s_odtinstallazione' % info
            ),
        ]
        url_patterns += super(ContrattoMastercomAdmin, self).get_urls()
        return url_patterns

    def stampa_assistenza(self, request, object_id):
        contratto = self.get_object(request, unquote(object_id))
        if not contratto:
            raise Http404()
        context = Context(dict(contratto=contratto, sede=contratto.sede))
        ragione_sociale = slugify(contratto.azienda).upper()
        filename = 'contratto_%s_%s.odt' % (contratto.id, ragione_sociale)
        template = 'contratto_assistenza_2013.odt'
        return webodt_render_to_response(
            'contratti/%s' % template,
            context_instance=context,
            filename=filename
        )

    def stampa_installazione(self, request, object_id):
        contratto = self.get_object(request, unquote(object_id))
        if not contratto:
            raise Http404()
        context = Context(dict(contratto=contratto, scuola=contratto.sede.anagrafica))
        ragione_sociale = slugify(contratto.azienda).upper()
        filename = 'installazione_%s_%s.odt' % (contratto.id, ragione_sociale)
        nome_template = 'contratti/documenti_installazione.odt'
        return webodt_render_to_response(
            nome_template,
            context_instance=context,
            filename=filename
        )


class ContrattoTimAdmin(ContrattoAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto',
        'data_consegna', 'data_inserimento_pda', 'data_fatturazione',
        'data_attivazione', 'get_totale_mastertraining_display',
        'get_gettone_totale_agente_display', 'premio_agente',
        'get_totale_agente_display', 'totale_prodotti', 'pagato', 'stato'
    )
    list_editable = (
        'premio_agente', 'data_inserimento_pda', 'data_fatturazione',
        'data_attivazione'
    )
    fieldsets = (
        (
            'Dati Generali', dict(
                classes=('suit-tab', 'suit-tab-testata'),
                fields=(
                    'azienda',
                    'provincia',
                    'agente',
                    'data_stipula',
                    'data_consegna_contratto',
                    'data_consegna',
                ))
        ),
        (
            'Fatturazione', dict(
                classes=('suit-tab', 'suit-tab-testata'),
                fields=(
                    ('fatturato', 'data_fatturazione', 'pagato'),
                ))
        ),
        (
            'PDA', dict(
                classes=('suit-tab', 'suit-tab-testata'),
                fields=(
                    ('inserita_pda', 'data_inserimento_pda'),
                ))
        ),
        (
            'Stato', dict(
                classes=('suit-tab', 'suit-tab-testata'),
                fields=(
                    ('stato', 'motivazione_stato'),
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Provvigioni Mastertraining', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_mastertraining',
                    'costo_materiale',
                    'totale_mastertraining',
                ))
        ),
        (
            'Provvigioni Agente', dict(
                classes=('suit-tab suit-tab-provvigioni',),
                fields=(
                    'gettone_totale_agente',
                    'premio_agente',
                    'totale_agente',
                ))
        ),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab', 'suit-tab-note'),
                fields=('note_stato',),
            )),
        (
            'Note', dict(
                classes=('suit-tab', 'suit-tab-note'),
                fields=('note',),
            )),
    )
    readonly_fields = (
        'totale_mastertraining', 'totale_agente', 'totale_prodotti',
        'gettone_totale_mastertraining', 'gettone_totale_agente', 'fornitore',
        'inserita_pda', 'fatturato'
    )
    inlines = [
        DettaglioContrattoTimInline,
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        CanoneStackedInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('dettaglio', 'Dettaglio'),
        ('provvigioni', 'Provvigioni'),
        ('allegati', 'Allegati'),
        ('canoni', 'Canoni'),
        ('note', 'Note'),
    )


class ContrattoVodafoneAdmin(ContrattoTimAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto',
        'data_consegna', 'data_inserimento_pda', 'data_fatturazione',
        'data_attivazione', 'get_totale_mastertraining_display',
        'get_gettone_totale_agente_display', 'premio_agente',
        'get_totale_agente_display', 'totale_prodotti', 'pagato', 'stato'
    )


class ContrattoWindAdmin(ContrattoTimAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto',
        'data_consegna', 'data_inserimento_pda', 'data_fatturazione',
        'data_attivazione', 'get_totale_mastertraining_display',
        'get_gettone_totale_agente_display', 'premio_agente',
        'get_totale_agente_display', 'totale_prodotti',
        'get_riepilogo_prodotti_gare', 'pagato', 'stato'
    )


class Contratto3Admin(ContrattoTimAdmin):
    list_display = (
        'id', 'azienda', 'agente', 'data_consegna_contratto',
        'data_consegna', 'data_inserimento_pda', 'data_fatturazione',
        'data_attivazione', 'get_totale_mastertraining_display',
        'get_gettone_totale_agente_display', 'premio_agente',
        'get_totale_agente_display', 'totale_prodotti', 'pagato', 'stato'
    )


# ---------------------------------------------------------
# INSTALLAZIONI
# ---------------------------------------------------------


class InstallazioneFastwebAdmin(ContrattoAdmin):
    list_display = (
        'azienda', 'provincia', 'agente', 'account', 'data_inserimento_pda',
        'data_attivazione', 'tipo_data_attivazione',
        'allegato_contratto_mastervoice', 'data_portabilita',
        'tipo_data_portabilita', 'chi_deve_fare', 'cosa_deve_fare',
        'stato_fastweb'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'provincia', 'agente',),
            ('account', 'allegato_contratto_mastervoice'),
            ('data_attivazione', 'tipo_data_attivazione',),
            ('data_portabilita', 'tipo_data_portabilita',),
            ('data_inserimento_pda',),
            ('chi_deve_fare', 'cosa_deve_fare',),
            ('stato_fastweb', 'note',),
        )}),
    )
    list_filter = ('agente', 'stato_fastweb', 'allegato_contratto_mastervoice')
    inlines = [AttachmentInlinesAddOnly, AttachmentInlinesNoAdd]
    readonly_fields = ('azienda', 'agente', 'provincia')
    actions = ['export_to_csv']

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_installazioni_fastweb.csv"'
        writer = csv.writer(response)
        writer.writerow(
            [
                'id',
                'azienda',
                'provincia',
                'agente',
                'account',
                'data inserimento pda',
                'data attivazione',
                'tipo data attivazione',
                'allegato contratto mastervoice',
                'data portabilita',
                'tipo data portabilita',
                'chi deve fare',
                'cosa deve fare',
                'stato_fastweb',
                'note'
            ]
        )
        for contratto in queryset:
            writer.writerow([
                contratto.id,
                contratto.azienda,
                contratto.provincia,
                contratto.agente,
                contratto.account,
                contratto.data_inserimento_pda,
                contratto.data_attivazione,
                contratto.tipo_data_attivazione,
                contratto.allegato_contratto_mastervoice,
                contratto.data_portabilita,
                contratto.tipo_data_portabilita,
                contratto.chi_deve_fare,
                contratto.cosa_deve_fare,
                contratto.stato_fastweb,
                contratto.note,
            ])
        return response
    export_to_csv.short_description = 'Esporta Installazioni Fastweb'


class InstallazioneWelcomeAdmin(ContrattoAdmin):
    list_display = (
        'azienda', 'provincia', 'agente', 'account', 'data_inserimento_pda',
        'data_attivazione', 'tipo_data_attivazione',
        'allegato_contratto_mastervoice', 'data_portabilita',
        'tipo_data_portabilita', 'chi_deve_fare', 'cosa_deve_fare',
        'stato_fastweb'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'provincia', 'agente',),
            ('account', 'allegato_contratto_mastervoice'),
            ('data_attivazione', 'tipo_data_attivazione',),
            ('data_portabilita', 'tipo_data_portabilita',),
            ('data_inserimento_pda',),
            ('chi_deve_fare', 'cosa_deve_fare',),
            ('stato_fastweb', 'note',),
        )}),
    )
    list_filter = ('agente', 'stato_fastweb', 'allegato_contratto_mastervoice')
    inlines = [AttachmentInlinesAddOnly, AttachmentInlinesNoAdd]
    readonly_fields = ('azienda', 'agente', 'provincia')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False


class InstallazioneMastervoiceAdmin(ContrattoAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    fieldsets = (
        (
            'Dati Azienda', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'azienda',
                    'sede',
                    'provincia',
                ))
        ),
        (
            'Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                ))
        ),
        (
            'Riferimenti', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'get_link_riferimento_lista',
                ))
        ),
        (
            'Dati Contratto', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'fornitore',
                    'forma_contratto',
                    'tipo_installazione',
                    'data_fine_prova',
                    'data_stipula',
                    'data_consegna_contratto',
                    'numero_contratti_fastweb',
                    'stato_installazione',
                    'data_inizio_rate',
                    'stato',
                    'motivazione_stato',
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Note Generali', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )),
        (
            'Note Stato Contratto', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note_stato',),
            )),
        (
            'Documentazione', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('documentazione', 'note_documentazione',),
            )),
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'numero_contratti_fastweb', 'stato',
        'motivazione_stato'
    )
    date_hierarchy = 'data_inizio_rate'
    actions = None
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('materiale', 'Materiale'),
        ('allegati', 'Allegati'),
        ('note', 'Note'),
    )

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneMastervoiceAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneMastervoice.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()

    def formfield_for_dbfield(self, db_field, **kwargs):
        from suit.widgets import SuitDateWidget
        field = super(InstallazioneMastervoiceAdmin, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'data_inizio_rate':
            field.widget = SuitDateWidget()
        elif db_field.name == 'data_fine_prova':
            field.widget = SuitDateWidget()
        return field


class InstallazioneIpkomAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneLineaIpkomAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneNetAndWorkAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneTwtAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneBriantelAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneHalAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneNgiAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'get_link_riferimento_lista',
        'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = ('data_inizio_rate', 'stato_installazione', 'data_fine_prova')
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'get_link_riferimento_lista', 'stato', 'motivazione_stato'
    )


class InstallazioneKpnquestAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = (
        'data_inizio_rate', 'stato_installazione', 'data_fine_prova'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'stato', 'motivazione_stato', 'get_link_riferimento_lista'
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneKpnquestAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneKpnquest.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()


class InstallazioneDigitelAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = (
        'data_inizio_rate', 'stato_installazione', 'data_fine_prova'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'stato', 'motivazione_stato', 'get_link_riferimento_lista'
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneDigitelAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneDigitel.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()


class InstallazioneAcantoAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = (
        'data_inizio_rate', 'stato_installazione', 'data_fine_prova'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'stato', 'motivazione_stato', 'get_link_riferimento_lista'
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneAcantoAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneAcanto.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()


class InstallazioneMcLinkAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = (
        'data_inizio_rate', 'stato_installazione', 'data_fine_prova'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'stato', 'motivazione_stato', 'get_link_riferimento_lista'
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneMcLinkAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneMcLink.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()


class InstallazioneFastwebResellerAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione',
        'forma_contratto', 'data_fine_prova', 'data_inizio_rate',
        'stato_installazione')
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione',
        'forma_contratto', 'sopralluogo', 'stato'
    )
    list_editable = (
        'data_inizio_rate', 'stato_installazione', 'data_fine_prova'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna', 'data_stipula', 'fornitore',
        'stato', 'motivazione_stato', 'get_link_riferimento_lista'
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneFastwebResellerAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneFastwebReseller.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()


class InstallazioneTrenoveAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'tipo_installazione', 'forma_contratto',
        'data_fine_prova', 'data_inizio_rate', 'stato_installazione'
    )
    list_filter = (
        'agente', 'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = (
        'data_inizio_rate', 'stato_installazione', 'data_fine_prova'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialeContrattoReadOnlyInline
    ]
    readonly_fields = (
        'azienda', 'agente', 'data_consegna',
        'data_stipula', 'fornitore', 'stato', 'motivazione_stato'
    )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneTrenoveAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneTrenove.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()


class InstallazioneMastervoiceAgentiAdmin(InstallazioneMastervoiceAdmin):
    list_display = (
        'azienda', 'provincia', 'agente', 'tipo_installazione',
        'forma_contratto', 'data_fine_prova', 'data_inizio_rate',
        'data_chiusura_fastweb', 'sopralluogo', 'data_sopralluogo',
        'stato_installazione'
    )
    fieldsets = (
        (
            None, {
                'fields': (
                    ('azienda', 'provincia', 'agente', 'fornitore'),
                    ('sede',),
                    ('referente', 'ruolo_referente', 'telefono_referente', 'email_referente'),
                    ('forma_contratto', 'tipo_installazione', 'data_fine_prova'),
                    ('data_stipula', 'data_consegna_contratto'),
                    ('numero_contratti_fastweb', 'data_chiusura_fastweb'),
                    ('sopralluogo', 'data_sopralluogo'),
                    ('stato_installazione', 'data_inizio_rate'),
                    ('stato', 'motivazione_stato'),
                )
            }
        ),
        (
            'Note Generali', {
                'classes': ('collapse', ),
                'fields': ('note', ),
            }
        ),
        (
            'Note Stato Contratto', {
                'classes': ('collapse', ),
                'fields': ('note_stato', ),
            }
        ),
        (
            'Documentazione', {
                'classes': ('collapse', ),
                'fields': ('documentazione', 'note_documentazione',),
            }
        ),
    )
    list_filter = (
        'stato_installazione', 'tipo_installazione', 'forma_contratto',
        'sopralluogo', 'stato'
    )
    list_editable = ()
    inlines = [AttachmentInlinesReadOnly]
    readonly_fields = (
        'azienda', 'provincia', 'agente', 'fornitore',
        'sede', 'get_link_riferimento_lista',
        'referente', 'ruolo_referente', 'telefono_referente', 'email_referente',
        'forma_contratto', 'tipo_installazione', 'data_fine_prova',
        'data_stipula', 'data_consegna_contratto',
        'numero_contratti_fastweb', 'data_chiusura_fastweb',
        'sopralluogo', 'data_sopralluogo',
        'stato_installazione', 'data_inizio_rate',
        'stato', 'motivazione_stato', 'note', 'note_stato', 'documentazione',
        'note_documentazione'
    )

    def get_queryset(self, request):
        qs = super(InstallazioneMastervoiceAgentiAdmin, self).get_queryset(request)
        if request.user.is_superuser:
            return qs
        else:
            elenco_utenti_subagenti = ProfiloUtente.objects.filter(area_manager=request.user)
            if elenco_utenti_subagenti:
                elenco_subagenti = [profilo.agente for profilo in elenco_utenti_subagenti]
                return qs.filter(agente__in=elenco_subagenti)
            else:
                return qs.filter(agente=request.user.utente_pk.agente)


class InstallazioneMastercomAdmin(ContrattoAdmin):
    list_display = (
        'id', 'sede', 'data_consegna_contratto', 'sopralluogo',
        'data_sopralluogo', 'stato_installazione',
    )
    readonly_fields = (
        'fornitore', 'agente', 'sede', 'data_stipula',
        'data_consegna', 'data_consegna_contratto', 'progetto', 'stato',
        'forma_contratto', 'tipo_contratto_mastercom'
    )
    list_filter = ('forma_contratto', 'stato', 'sopralluogo')
    fieldsets = (
        (
            'Dati installazione', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'sede',
                    'forma_contratto',
                    'tipo_contratto_mastercom',
                    'progetto',
                    'data_stipula',
                    'data_consegna_contratto',
                    'sopralluogo',
                    'data_sopralluogo',
                    'stato_installazione',
                ),
            )
        ),
        (
            'Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ),
            )
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=(
                    'note',
                    'documentazione',
                    'note_documentazione',
                ),
            )
        ),
    )
    inlines = [AttachmentInlinesAddOnly, AttachmentInlinesNoAdd, MaterialeContrattoMastercomInline]
    form = forms.ContrattoMastercomForm
    actions = None
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('materiale', 'Materiale'),
        ('allegati', 'Allegati'),
        ('note', 'Note'),
    )

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generaacquisto/$',
                self.admin_site.admin_view(self.genera_acquisto),
                name='%s_%s_generaacquisto' % info
            ),
        ]
        url_patterns += super(InstallazioneMastercomAdmin, self).get_urls()
        return url_patterns

    def genera_acquisto(self, request, object_id):
        installazione = InstallazioneMastercom.objects.get(pk=unquote(object_id))
        if not installazione:
            raise Http404()
        nuovo_acquisto = installazione.genera_acquisto(request.user)
        if nuovo_acquisto:
            return HttpResponseRedirect('/acquisti/acquisto/%s' % (nuovo_acquisto.pk))
        else:
            raise Http404()

# ---------------------------------------------------------
# PREORDINI
# ---------------------------------------------------------


class MaterialePreordineInline(admin.TabularInline):
    model = MaterialePreordine
    form = forms.MaterialePreordineForm
    readonly_fields = ('prezzo_listino', 'totale_vendita')
    suit_classes = ('suit-tab suit-tab-materiale')


class PreordineAdmin(admin.ModelAdmin):
    list_display = (
        'azienda', 'sede', 'provincia', 'tipo', 'agente',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'get_link_offerta_originale_lista', 'chiuso'
    )
    fieldsets = (
        (
            'Dati azienda', dict(
                classes=('suit-tab suit-tab-testata', ),
                fields=(
                    'progetto',
                    'azienda',
                    'sede',
                    ('citta', 'provincia',),
                    'tipo',
                    'agente',
                    ('offerta', 'get_link_offerta_originale',),
                    'data_firma_contratto',
                    'tipo_servizio',
                    'verifica_tecnica',
                    'data_verifica_tecnica',
                    'chiuso',
                ))
        ),
        (
            'Dati Referente', dict(
                classes=('suit-tab suit-tab-referente',),
                fields=(
                    'referente',
                    'ruolo_referente',
                    'telefono_referente',
                    'email_referente',
                ))
        ),
        (
            'Note', dict(
                classes=('suit-tab suit-tab-note',),
                fields=('note',),
            )),
    )
    readonly_fields = ('offerta', 'get_link_offerta_originale')
    form = forms.PreordineForm
    list_filter = ('tipo', 'verifica_tecnica', 'chiuso')
    search_fields = (
        'azienda', 'sede', 'provincia', 'agente__cognome', 'tipo_servizio'
    )
    inlines = [
        AttachmentInlinesAddOnly,
        AttachmentInlinesNoAdd,
        MaterialePreordineInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('referente', 'Dati Referente'),
        ('allegati', 'Allegati'),
        ('materiale', 'Materiale'),
        ('note', 'Note'),
    )

    def save_formset(self, request, form, formset, change):
        instances = formset.save()
        for instance in instances:
            if isinstance(instance, Attachment):
                if not instance.creator:
                    instance.creator = request.user
            instance.save()


class PreordineFastwebAdmin(PreordineAdmin):
    list_display = (
        'azienda', 'sede', 'provincia', 'agente',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'chiuso'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'sede', 'provincia', 'citta'),
            ('progetto', 'agente',),
            ('data_firma_contratto', 'tipo_servizio'),
            ('verifica_tecnica', 'data_verifica_tecnica', 'chiuso'),
            ('referente', 'ruolo_referente',),
            ('telefono_referente', 'email_referente'),
            'note',
        )}),
    )


class PreordineIpkomAdmin(PreordineAdmin):
    list_display = (
        'azienda', 'sede', 'provincia', 'agente',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'chiuso'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'sede', 'provincia', 'citta'),
            ('progetto', 'agente',),
            ('data_firma_contratto', 'tipo_servizio'),
            ('verifica_tecnica', 'data_verifica_tecnica', 'chiuso'),
            ('referente', 'ruolo_referente',),
            ('telefono_referente', 'email_referente'),
            'note',
        )}),
    )


class PreordineLineaIpkomAdmin(PreordineAdmin):
    list_display = (
        'azienda', 'sede', 'provincia', 'agente',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'chiuso'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'sede', 'provincia', 'citta'),
            ('progetto', 'agente',),
            ('data_firma_contratto', 'tipo_servizio'),
            ('verifica_tecnica', 'data_verifica_tecnica', 'chiuso'),
            ('referente', 'ruolo_referente',),
            ('telefono_referente', 'email_referente'),
            'note',
        )}),
    )


class PreordineMastervoiceAdmin(PreordineAdmin):
    list_display = (
        'azienda', 'sede_cliente', 'provincia', 'agente', 'tipo_installazione',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'get_link_offerta_originale_lista', 'chiuso'
    )
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'azienda',
                    ('sede', 'provincia', 'citta'),
                    'sede_cliente',
                    'agente',
                    'tipo_installazione',
                    'progetto',
                    ('offerta', 'get_link_offerta_originale'),
                    ('data_firma_contratto', 'tipo_servizio'),
                    ('verifica_tecnica', 'data_verifica_tecnica', 'chiuso'),
                    ('referente', 'ruolo_referente',),
                    ('telefono_referente', 'email_referente'),
                    'note',
                )
            }
        ),
    )
    list_filter = ('provincia', 'tipo_installazione', 'agente', 'verifica_tecnica', 'chiuso')

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generacontratto/$',
                self.admin_site.admin_view(self.genera_contratto),
                name='%s_%s_generacontratto' % info
            ),
        ]
        url_patterns += super(PreordineMastervoiceAdmin, self).get_urls()
        return url_patterns

    def genera_contratto(self, request, object_id):
        preordine = PreordineMastervoice.objects.get(pk=unquote(object_id))
        if not preordine:
            raise Http404()
        contratto = preordine.genera_contratto()
        if contratto:
            return HttpResponseRedirect('/contratti/installazionemastervoice/%s' % (contratto.pk))
        else:
            raise Http404()


class PreordineMastercomAdmin(PreordineAdmin):
    list_display = (
        'azienda', 'sede_cliente', 'provincia', 'agente', 'tipo_installazione',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'get_link_offerta_originale_lista', 'chiuso'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'provincia', 'citta'),
            ('sede_cliente', 'agente',),
            ('offerta', 'get_link_offerta_originale'),
            ('data_firma_contratto', 'progetto',),
            ('verifica_tecnica', 'data_verifica_tecnica', 'chiuso'),
            ('referente', 'ruolo_referente', 'telefono_referente', 'email_referente'),
            'note',
        )}),
    )
    list_filter = ('provincia', 'agente', 'verifica_tecnica', 'chiuso')

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/generacontratto/$',
                self.admin_site.admin_view(self.genera_contratto),
                name='%s_%s_generacontratto' % info
            ),
        ]
        url_patterns += super(PreordineMastercomAdmin, self).get_urls()
        return url_patterns

    def genera_contratto(self, request, object_id):
        preordine = PreordineMastercom.objects.get(pk=unquote(object_id))
        if not preordine:
            raise Http404()
        contratto = preordine.genera_contratto()
        if contratto:
            return HttpResponseRedirect('/contratti/contrattomastercom/%s' % (contratto.pk))
        else:
            raise Http404()


class PreordineAgentiAdmin(PreordineAdmin):
    list_display = (
        'azienda', 'tipo', 'sede', 'provincia', 'agente', 'tipo_installazione',
        'data_firma_contratto', 'verifica_tecnica', 'data_verifica_tecnica',
        'note'
    )
    fieldsets = (
        (None, {'fields': (
            ('azienda', 'sede', 'provincia', 'citta'),
            ('agente', 'tipo', 'tipo_installazione'),
            ('data_firma_contratto', 'tipo_servizio'),
            ('verifica_tecnica', 'data_verifica_tecnica'),
            ('referente', 'ruolo_referente',),
            ('telefono_referente', 'email_referente'),
            'note',
        )}),
    )
    list_filter = ('provincia', 'tipo', 'verifica_tecnica')
    readonly_fields = (
        'azienda', 'sede', 'sede_cliente', 'provincia', 'citta',
        'agente', 'tipo_installazione', 'data_firma_contratto', 'tipo_servizio',
        'verifica_tecnica', 'data_verifica_tecnica', 'chiuso', 'referente',
        'ruolo_referente', 'telefono_referente', 'email_referente', 'note',
        'tipo'
    )
    inlines = [AttachmentInlinesReadOnly]
    can_delete = False
    actions = None
    search_fields = (
        'azienda', 'sede', 'provincia', 'agente__cognome',
        'tipo_servizio', 'note'
    )

    def get_queryset(self, request):
        qs = super(PreordineAgentiAdmin, self).get_queryset(request)
        if request.user.is_superuser or \
                request.user.has_perm('offerte.ha_privilegi_responsabile') or \
                request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return qs.filter()
        else:
            elenco_subagenti = ProfiloUtente.objects.filter(area_manager=request.user)
            if elenco_subagenti:
                return qs.filter(agente__in=[ag.agente for ag in elenco_subagenti])
            else:
                return qs.filter(agente=request.user.utente_pk.agente)

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def save_model(self, request, obj, form, change):
        pass


# ---------------------------------------------------------
# PROVVIGIONI
# ---------------------------------------------------------


class CompensoAdmin(MastergestAdmin):
    list_display = (
        'data', 'contratto', 'agente', 'importo', 'tipo', 'descrizione',
        'consolidato'
    )
    list_filter = ('tipo', 'agente', 'consolidato')
    list_editable = ('consolidato', )
    date_hierarchy = 'data'
    readonly_fields = ('get_link_contratto',)
    search_fields = ('contratto__azienda', 'agente__cognome', 'descrizione')
    form = forms.CompensoForm
    actions = ['consolida_compensi', 'export_to_csv']
    fieldsets = (
        (None, {'fields': (
            'data',
            'tipo', 'agente',
            ('contratto', 'get_link_contratto', ),
            'descrizione',
            'importo',
            'consolidato',
        )}),
    )

    def get_actions(self, request):
        actions = super(CompensoAdmin, self).get_actions(request)
        actions.pop('delete_selected', None)
        return actions

    def genera_stampa_estratto_conto(self, compensi, agente, datainizio, datafine):
        saldo = DECIMAL_ZERO
        totale_dare = DECIMAL_ZERO
        totale_avere = DECIMAL_ZERO
        for compenso in compensi:
            saldo += compenso.valore
            compenso.saldo = saldo
            if compenso.tipo in ['storno', 'bonifico']:
                compenso.dare = compenso.importo
                compenso.avere = DECIMAL_ZERO
                totale_dare += compenso.importo
            else:
                compenso.avere = compenso.importo
                compenso.dare = DECIMAL_ZERO
                totale_avere += compenso.importo
        context = Context(
            dict(
                compensi=compensi,
                agente=agente,
                datainizio=datainizio,
                datafine=datafine,
                totale_saldo=saldo,
                totale_avere=totale_avere,
                totale_dare=totale_dare
            )
        )
        filename = 'estratto_conto_%s.odt' % agente.cognome.upper()
        template_estratto_conto = 'estratto_conto.odt'
        return webodt_render_to_response(
            'contratti/%s' % template_estratto_conto,
            context_instance=context,
            filename=filename
        )

    @csrf_protect_m
    def changelist_view(self, request, extra_context=None):
        print_setup_form = forms.PrintSetupForm()
        if request.method == 'POST':
            print_setup_form = forms.PrintSetupForm(request.POST)
            if print_setup_form.is_valid():
                """ start prepare the print """
                datainizio = print_setup_form.cleaned_data['datainizio']
                datafine = print_setup_form.cleaned_data['datafine']
                agente = print_setup_form.cleaned_data['agente']
                if request.POST.get('estrattoconto'):
                    compensi = Compenso.objects.filter(agente=agente, data__gte=datainizio, data__lte=datafine)
                    compensi = compensi.order_by('data', 'tipo', 'contratto')
                    if not compensi.count():
                        messages.warning(
                            request,
                            '[%s] Nessun compenso trovato.' % timezone.now().strftime('%H:%M:%S')
                        )
                    else:
                        return self.genera_stampa_estratto_conto(compensi, agente, datainizio, datafine)
        extra_context = {
            'print_setup_form': print_setup_form,
        }
        return super(CompensoAdmin, self).changelist_view(
            request, extra_context)

    def consolida_compensi(self, request, queryset):
        if queryset:
            for compenso in queryset:
                compenso.consolidato = True
                compenso.save()
            msg = "Consolidati %d compensi." % queryset.count()
            messages.success(request, msg)
    consolida_compensi.short_description = 'Consolida compensi selezionati'

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_compensi.csv"'
        writer = csv.writer(response)
        writer.writerow([
            'data',
            'tipo',
            'agente',
            'contratto',
            'descrizione',
            'importo',
        ])
        for compenso in queryset:
            writer.writerow([
                compenso.data.strftime("%d/%m/%Y"),
                compenso.get_tipo_display(),
                compenso.agente,
                compenso.contratto,
                compenso.descrizione,
                str(compenso.importo).replace('.', ',')
            ])
        return response
    export_to_csv.short_description = 'Esporta Compensi (CSV)'


class CompensoAgenteAdmin(CompensoAdmin):
    list_display = ('display_azienda', 'display_fornitore', 'tipo', 'importo',)
    list_filter = ('tipo', 'contratto__fornitore')
    readonly_fields = (
        'tipo', 'contratto', 'descrizione', 'importo'
    )
    list_editable = ()
    search_fields = ('contratto__azienda', 'descrizione')
    fieldsets = (
        (None, {'fields': (
            'tipo',
            ('contratto', ),
            'importo',
        )}),
    )
    date_hierarchy = None
    actions = None

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def save_model(self, request, obj, form, change):
        pass

    def get_queryset(self, request):
        qs = super(CompensoAgenteAdmin, self).get_queryset(request)
        if request.user.is_superuser or \
            request.user.has_perm('offerte.ha_privilegi_responsabile') or \
                request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return qs.filter()
        else:
            agente = request.user.utente_pk.agente
            return qs.filter(agente=agente)

    def get_list_display(self, request):
        list_display = super(CompensoAgenteAdmin, self).get_list_display(request)
        if request.user.is_superuser:
            return ('contratto', 'agente', 'importo', 'tipo', 'descrizione')
        return list_display

    def get_list_filter(self, request):
        list_filter = super(CompensoAgenteAdmin, self).get_list_filter(request)
        if request.user.is_superuser:
            list_filter = ('tipo', 'agente')
        return list_filter

    def display_azienda(self, obj):
        return obj.contratto.azienda
    display_azienda.short_description = 'Azienda'
    display_azienda.admin_order_field = 'contratto__azienda'

    def display_fornitore(self, obj):
        return obj.contratto.fornitore
    display_fornitore.short_description = 'Fornitore'
    display_fornitore.admin_order_field = 'contratto__fornitore'


class CompensoReadOnlyInline(admin.TabularInline):
    fields = ('data', 'tipo', 'agente', 'importo', 'contratto')
    model = Compenso
    readonly_fields = ('data', 'agente', 'importo', 'tipo', 'contratto')
    extra = 0
    can_delete = False
    max_num = 0
    suit_classes = 'suit-tab suit-tab-provvigioni'

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class RiepilogoProvvigioniAdmin(admin.ModelAdmin):
    list_display = (
        'anno', 'get_nome_mese', 'agente', 'fornitore',
        'totale_provvigioni_master_display', 'totale_provvigioni_agente_display',
        'gara_premio', 'super_totale_agente_display', 'percentuale_liquidabile',
        'get_totale_liquidabile_display', 'totale_liquidato',
        'get_totale_rimanente_display', 'numero_storni',
    )
    list_filter = ('anno', 'mese', 'agente', 'fornitore',)
    fieldsets = (
        (None, {'fields': (
            ('anno', 'mese', 'agente', 'fornitore'),
            ('totale_provvigioni_master',),
            ('totale_provvigioni_agente', 'gara_premio', 'super_totale_agente', 'percentuale_liquidabile', 'get_totale_liquidabile',),
            ('totale_liquidato', 'get_totale_rimanente',),
            ('numero_storni'),
        )}),
    )
    readonly_fields = (
        'totale_provvigioni_agente', 'get_totale_liquidabile',
        'get_totale_rimanente', 'numero_storni', 'totale_provvigioni_master',
        'anno', 'mese', 'agente', 'fornitore', 'super_totale_agente',
        'percentuale_liquidabile', 'totale_liquidato', 'gara_premio'
    )
    search_fields = ('agente__cognome', 'fornitore', 'anno', 'mese')
    actions = ['export_to_csv']
    form = forms.RiepilogoProvvigioniForm

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def get_list_display(self, request, obj=None):
        if request.user.is_superuser or request.user.has_perm('contratti.ha_privilegi_responsabile'):
            self.list_filter = ('anno', 'mese', 'agente', 'fornitore',)
            self.list_editable = ('gara_premio', 'totale_liquidato', 'percentuale_liquidabile')
            return self.list_display
        elif request.user.has_perm('offerte.ha_privilegi_responsabile'):
            self.list_filter = ('anno', 'mese', 'agente', 'fornitore',)
            self.list_editable = ()
            self.actions = ['export_to_csv']
            return self.list_display
        else:
            list_display_agente = (
                'anno', 'mese', 'agente',
                'fornitore', 'totale_provvigioni_agente', 'gara_premio',
                'super_totale_agente', 'percentuale_liquidabile',
                'get_totale_liquidabile', 'totale_liquidato', 'get_totale_rimanente',
                'numero_storni'
            )
            fieldsets = (
                (None, {'fields': (
                    ('anno', 'mese', 'agente', 'fornitore'),
                    ('totale_provvigioni_agente', 'gara_premio', 'super_totale_agente', 'percentuale_liquidabile', 'get_totale_liquidabile',),
                    ('totale_liquidato', 'get_totale_rimanente',),
                    ('numero_storni'),
                )}),
            )
            self.fieldsets = fieldsets
            self.actions = None
            self.list_editable = ()
            self.list_filter = ('anno', 'mese', 'fornitore',)
            return list_display_agente

    def get_queryset(self, request):
        qs = super(RiepilogoProvvigioniAdmin, self).get_queryset(request)
        if request.user.is_superuser or \
            request.user.has_perm('offerte.ha_privilegi_responsabile') or \
                request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return qs.filter()
        return qs.filter(agente=request.user.utente_pk.agente)

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_riepiloghi_provvigioni.csv"'
        writer = csv.writer(response)
        writer.writerow([
            'anno',
            'mese',
            'agente',
            'fornitore',
            'totale provvigioni master',
            'totale provvigioni agente',
            'gara_premio',
            'super_totale_agente',
            'percentuale liquidabile',
            'get_totale liquidabile',
            'totale liquidato',
            'get_totale rimanente',
            'numero storni',
        ])
        for riepilogo in queryset:
            writer.writerow([
                riepilogo.anno,
                riepilogo.mese,
                riepilogo.agente,
                riepilogo.fornitore,
                int(riepilogo.totale_provvigioni_master()),
                int(riepilogo.totale_provvigioni_agente()),
                int(riepilogo.gara_premio),
                int(riepilogo.super_totale_agente()),
                int(riepilogo.percentuale_liquidabile),
                int(riepilogo.get_totale_liquidabile()),
                int(riepilogo.totale_liquidato),
                int(riepilogo.get_totale_rimanente()),
                int(riepilogo.numero_storni),
            ])
        return response
    export_to_csv.short_description = 'Esporta Riepilogo'

    def change_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, unquote(object_id))
        if obj:
            extra_context = extra_context or dict()
            elenco_contratti = Contratto.objects.filter(
                data_consegna__month=obj.mese,
                data_consegna__year=obj.anno,
                agente=obj.agente,
                fornitore=obj.fornitore
            )
            extra_context['lista_contratti'] = elenco_contratti
        return super(RiepilogoProvvigioniAdmin, self).change_view(
            request,
            object_id,
            extra_context=extra_context
        )

    def formfield_for_dbfield(self, db_field, **kwargs):
        field = super(RiepilogoProvvigioniAdmin, self).formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'totale_liquidato':
            field.widget.attrs['size'] = '8'
        if db_field.name == 'gara_premio':
            field.widget.attrs['size'] = '8'
        return field


class CalcoloProvvigioniTotaleAdmin(admin.ModelAdmin):
    list_display = (
        'anno', 'mese', 'agente', 'totale_provvigioni_agente_display',
        'get_totale_gare_agente_display', 'get_totale_liquidabile_display',
        'get_totale_liquidato_display', 'get_totale_rimanente_display',
        'totale_provvigioni_mastertraining_display', 'get_utile_display',
        'numero_contratti', 'numero_storni'
    )
    readonly_fields = (
        'anno', 'mese', 'agente', 'totale_provvigioni_agente',
        'get_totale_gare_agente', 'totale_provvigioni_mastertraining',
        'numero_contratti', 'get_utile', 'get_totale_liquidabile',
        'get_totale_liquidato', 'get_totale_rimanente', 'numero_storni'
    )
    list_filter = ('anno', 'mese', 'agente')
    fieldsets = (
        (None, {'fields': (
            ('anno', 'mese', 'agente',),
            ('totale_provvigioni_mastertraining', 'numero_contratti', 'numero_storni'),
            ('totale_provvigioni_agente', 'get_totale_gare_agente', 'get_totale_liquidabile', 'get_totale_liquidato', 'get_totale_rimanente'),
            ('get_utile',),
        )}),
    )
    form = forms.CalcoloProvvigioniTotaleForm
    actions = ['export_to_csv']
    can_delete = False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^odt/(\w+)/$',
                self.admin_site.admin_view(self.invito_odt),
                name='%s_%s_invito_odt' % info
            ),
        ]
        url_patterns += super(CalcoloProvvigioniTotaleAdmin, self).get_urls()
        return url_patterns

    def invito_odt(self, request, object_id):
        calcolo = self.get_object(request, unquote(object_id))
        if not calcolo:
            raise Http404()
        elenco_contratti = Contratto.objects.filter(
            data_consegna__month=calcolo.mese,
            data_consegna__year=calcolo.anno,
            agente=calcolo.agente
        ).exclude(stato__ignora_provvigioni_relative=True).exclude(totale_agente=Decimal('0.00'))
        elenco_ko = Contratto.objects.filter(
            data_consegna__month=calcolo.mese,
            data_consegna__year=calcolo.anno,
            agente=calcolo.agente,
            stato__ignora_provvigioni_relative=True
        )
        context = Context(dict(elenco_contratti=elenco_contratti, calcolo=calcolo, elenco_ko=elenco_ko))
        filename = 'invito_%s_%s_%s.odt' % (calcolo.anno, calcolo.mese, calcolo.agente.cognome.upper())
        template_invito = 'invito.odt'
        return webodt_render_to_response(
            'contratti/%s' % template_invito,
            context_instance=context,
            filename=filename
        )

    def change_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, unquote(object_id))
        if obj:
            extra_context = extra_context or dict()
            if request.user.is_superuser or \
                request.user.has_perm('offerte.ha_privilegi_responsabile') or \
                    request.user.has_perm('contratti.ha_privilegi_responsabile'):
                elenco_contratti = Contratto.objects.filter(
                    data_consegna__month=obj.mese,
                    data_consegna__year=obj.anno,
                    agente=obj.agente
                )
            else:
                elenco_contratti = Contratto.objects.filter(
                    data_consegna__month=obj.mese,
                    data_consegna__year=obj.anno,
                    agente=obj.agente
                ).exclude(totale_agente=DECIMAL_ZERO)
            extra_context['lista_contratti'] = elenco_contratti
        return super(CalcoloProvvigioniTotaleAdmin, self).change_view(request, object_id, extra_context=extra_context)

    def save_model(self, request, obj, form, change):
        pass

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_calcoli_provvigioni.csv"'
        writer = csv.writer(response)
        writer.writerow([
            'anno',
            'mese',
            'agente',
            'totale provvigioni master',
            'totale provvigioni agente',
            'totale gare agente',
            'totale liquidabile',
            'totale liquidato',
            'totale rimanente',
            'utile',
            'numero storni',
            'numero contratti'
        ])
        for calcolo in queryset:
            writer.writerow([
                calcolo.anno,
                calcolo.mese,
                calcolo.agente,
                int(calcolo.totale_provvigioni_mastertraining),
                int(calcolo.totale_provvigioni_agente),
                int(calcolo.get_totale_gare_agente()),
                int(calcolo.get_totale_liquidabile()),
                int(calcolo.get_totale_liquidato()),
                int(calcolo.get_totale_rimanente()),
                int(calcolo.get_utile()),
                int(calcolo.numero_storni),
                int(calcolo.numero_contratti),
            ])
        return response
    export_to_csv.short_description = 'Esporta Calcoli Provvigioni'

    def get_list_display(self, request, obj=None):
        if request.user.is_superuser or \
                request.user.has_perm('offerte.ha_privilegi_responsabile') or \
                request.user.has_perm('contratti.ha_privilegi_responsabile'):
            list_display = self.list_display
            self.list_filter = ('anno', 'mese', 'agente')
        else:
            self.list_filter = ('anno', 'mese')
            list_display = (
                'anno', 'mese', 'agente', 'totale_provvigioni_agente_display',
                'get_totale_gare_agente_display', 'get_totale_liquidabile_display',
                'get_totale_liquidato_display', 'get_totale_rimanente_display',
                'numero_contratti', 'numero_storni'
            )
            self.actions = None
            self.fieldsets = (
                (None, {'fields': (
                    ('anno', 'mese', 'agente',),
                    ('numero_contratti', 'numero_storni'),
                    ('totale_provvigioni_agente', 'get_totale_gare_agente', 'get_totale_liquidabile', 'get_totale_liquidato', 'get_totale_rimanente'),
                )}),
            )
        return list_display

    def get_queryset(self, request):
        qs = super(CalcoloProvvigioniTotaleAdmin, self).get_queryset(request)
        if request.user.is_superuser or \
            request.user.has_perm('offerte.ha_privilegi_responsabile') or \
                request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return qs.filter()
        else:
            qs = qs.exclude(totale_provvigioni_agente=DECIMAL_ZERO)
            elenco_subagenti = ProfiloUtente.objects.filter(area_manager=request.user)
            if elenco_subagenti:
                return qs.filter(agente__in=[ag.agente for ag in elenco_subagenti])
            else:
                return qs.filter(agente=request.user.utente_pk.agente)


class ProvvigioneAgenteAdmin(admin.ModelAdmin):
    list_display = (
        'anno', 'get_nome_mese', 'agente', 'get_totale_maturato_display',
        'get_totale_bonifici_display', 'get_totale_storni_display',
        'get_totale_display', 'numero_ricorrenti', 'numero_gettoni',
    )
    list_filter = ('anno', 'mese', 'agente',)
    fieldsets = (
        (None, {'fields': (
            ('anno', 'mese', 'agente', 'numero_compensi'),
            ('get_totale_maturato', 'totale_bonifici', 'totale_storni', 'totale',),
        )}),
    )
    readonly_fields = (
        'anno', 'mese', 'agente', 'totale', 'get_totale_maturato',
        'numero_compensi', 'totale_storni', 'totale_bonifici'
    )
    actions = None
    form = forms.ProvvigioneAgenteForm

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^odt/(\w+)/$',
                self.admin_site.admin_view(self.invito_odt),
                name='%s_%s_invito_odt' % info
            ),
        ]
        url_patterns += super(ProvvigioneAgenteAdmin, self).get_urls()
        return url_patterns

    def invito_odt(self, request, object_id):
        riepilogo = self.get_object(request, object_id)
        if not riepilogo:
            raise Http404()
        elenco_compensi_nuovi = []
        elenco_compensi_vecchi = []
        elenco_compensi_mese = Compenso.objects.filter(
            data__month=riepilogo.mese,
            data__year=riepilogo.anno,
            agente=riepilogo.agente,
            consolidato=True
        ).exclude(tipo='storno').exclude(tipo='bonifico')
        for compenso in elenco_compensi_mese:
            if compenso.is_primo():
                elenco_compensi_nuovi.append(compenso)
            else:
                elenco_compensi_vecchi.append(compenso)
        elenco_storni = Compenso.objects.filter(data__month=riepilogo.mese, data__year=riepilogo.anno, agente=riepilogo.agente, tipo='storno')
        elenco_bonifici = Compenso.objects.filter(data__month=riepilogo.mese, data__year=riepilogo.anno, agente=riepilogo.agente, tipo='bonifico')
        context = Context(
            dict(
                elenco_compensi=elenco_compensi_vecchi, riepilogo=riepilogo,
                elenco_storni=elenco_storni, elenco_bonifici=elenco_bonifici,
                elenco_compensi_nuovi=elenco_compensi_nuovi,
            )
        )
        filename = 'invito_%s_%s_%s.odt' % (riepilogo.anno, riepilogo.mese, riepilogo.agente.cognome.upper())
        template_invito = 'invito.odt'
        return webodt_render_to_response('contratti/%s' % template_invito, context_instance=context, filename=filename)

    def change_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, unquote(object_id))
        if obj:
            extra_context = extra_context or dict()
            elenco_compensi = Compenso.objects.filter(
                data__month=obj.mese,
                data__year=obj.anno,
                agente=obj.agente,
                consolidato=True
            ).order_by('contratto__azienda')
            extra_context['lista_compensi'] = elenco_compensi
        return super(ProvvigioneAgenteAdmin, self).change_view(
            request,
            object_id,
            extra_context=extra_context
        )

    def save_model(self, request, obj, form, change):
        pass

    def get_queryset(self, request):
        qs = super(ProvvigioneAgenteAdmin, self).get_queryset(request)
        ora_corrente = timezone.now()
        anno_corrente = ora_corrente.year
        mese_corrente = ora_corrente.month
        if request.user.is_superuser or request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return qs.exclude(anno__gt=anno_corrente).exclude(anno=anno_corrente, mese__gte=mese_corrente)
        if request.user.utente_pk.agente:
            return qs.filter(
                agente=request.user.utente_pk.agente
            ).exclude(anno__gt=anno_corrente).exclude(anno=anno_corrente, mese__gte=mese_corrente)

    def get_list_display(self, request):
        list_display = super(ProvvigioneAgenteAdmin, self).get_list_display(request)
        if request.user.is_superuser or request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return list_display
        if request.user.utente_pk.agente:
            list_display = (
                'anno', 'get_nome_mese', 'get_totale_maturato_display',
                'get_totale_bonifici_display', 'get_totale_storni_display',
                'get_totale_display', 'numero_ricorrenti', 'numero_gettoni',
            )
        return list_display

    def get_list_filter(self, request):
        list_filter = super(ProvvigioneAgenteAdmin, self).get_list_filter(request)
        if request.user.is_superuser or request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return list_filter
        if request.user.utente_pk.agente:
            return ('anno', 'mese', )
        return list_filter


class ProvvigioneAgenteFornitoreAdmin(admin.ModelAdmin):
    list_display = (
        'anno', 'get_nome_mese', 'agente',
        'fornitore', 'totale', 'numero_compensi',
    )
    list_filter = ('anno', 'mese', 'agente', 'fornitore',)
    fieldsets = (
        (None, {'fields': (
            ('anno', 'mese', 'agente', 'fornitore'),
            ('numero_compensi', 'totale',),
        )}),
    )
    readonly_fields = (
        'anno', 'mese', 'agente', 'fornitore', 'totale',
        'numero_compensi'
    )
    search_fields = ('agente__cognome', 'fornitore', 'anno', 'mese')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def change_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, unquote(object_id))
        if obj:
            extra_context = extra_context or dict()
            elenco_compensi = Compenso.objects.filter(
                data__month=obj.mese,
                data__year=obj.anno,
                agente=obj.agente,
                contratto__fornitore=obj.fornitore,
                consolidato=True
            )
            extra_context['lista_compensi'] = elenco_compensi
        return super(ProvvigioneAgenteFornitoreAdmin, self).change_view(request, object_id, extra_context=extra_context)


class SchemaProvvigioneAgenteReadOnlyInline(admin.TabularInline):
    fields = (
        'get_link_display', 'fornitore', 'tipologia', 'schema_provvigione',
        'data_inizio_validita', 'data_fine_validita', 'attivo'
    )
    model = SchemaProvvigioneAgente
    readonly_fields = (
        'get_link_display', 'fornitore', 'tipologia', 'schema_provvigione',
        'data_inizio_validita', 'data_fine_validita', 'attivo'
    )
    extra = 0
    can_delete = False
    max_num = 0
    suit_classes = ('suit-tab suit-tab-schema_provvigione_agente')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class SchemaProvvigioneCapoAreaReadOnlyInline(admin.TabularInline):
    fields = (
        'get_link_display', 'fornitore', 'tipologia', 'schema_provvigione',
        'data_inizio_validita', 'data_fine_validita', 'attivo'
    )
    model = SchemaProvvigioneCapoArea
    readonly_fields = (
        'get_link_display', 'fornitore', 'tipologia', 'schema_provvigione',
        'data_inizio_validita', 'data_fine_validita', 'attivo'
    )
    extra = 0
    can_delete = False
    max_num = 0
    suit_classes = ('suit-tab suit-tab-schema_provvigione_capo_area')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class SubAgenteReadOnlyInline(admin.TabularInline):
    fields = ('cognome', 'nome', 'tipo', 'attivo')
    model = Agente
    readonly_fields = ('cognome', 'nome', 'tipo', 'attivo')
    extra = 0
    can_delete = False
    max_num = 0
    fk_name = 'capo_area'
    verbose_name = 'Subagente'
    verbose_name_plural = 'Subagenti'
    suit_classes = ('suit-tab suit-tab-subagenti')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class AgenteAdmin(admin.ModelAdmin):
    list_display = (
        'cognome', 'nome', 'telefono', 'email', 'tipo',
        'sottotipo', 'stato_documenti', 'capo_area', 'attivo'
    )
    list_filter = ('tipo', 'sottotipo', 'stato_documenti', 'attivo')
    search_fields = ('cognome', 'nome', 'telefono', 'email')
    list_editable = ('attivo', 'capo_area')
    fieldsets = (
        (
            'Dati Anagrafici', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'cognome',
                    'nome',
                    'telefono',
                    'email',
                ))
        ),
        (
            'Tipo Agente', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'tipo',
                    'sottotipo',
                    'capo_area',
                ))
        ),
        (
            'Stato', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'stato_documenti',
                    'attivo',
                ))
        ),
    )
    actions = None
    inlines = [
        SchemaProvvigioneAgenteReadOnlyInline,
        SchemaProvvigioneCapoAreaReadOnlyInline,
        SubAgenteReadOnlyInline,
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('schema_provvigione_agente', 'Schema provv.'),
        ('schema_provvigione_capo_area', 'Schema provv. Capo Area'),
        ('subagenti', 'Subagenti'),
    )

    def get_readonly_fields(self, request, obj=None):
        if request.user.is_superuser or request.user.has_perm('contratti.ha_privilegi_responsabile'):
            return self.readonly_fields
        return (
            'cognome', 'nome', 'telefono', 'email', 'mastertraining',
            'tipo', 'sottotipo', 'stato_documenti'
        )


class StatoContrattoAdmin(admin.ModelAdmin):
    list_display = ('descrizione', 'ignora_provvigioni_relative')


class MotivazioneStatoContrattoAdmin(admin.ModelAdmin):
    list_display = ('descrizione',)


class OpzioneTelefoniaAdmin(TipoContrattoAdmin):
    list_display = ('codice', 'descrizione', 'fornitore', 'tipologia', 'sottotipo', 'costo', 'tcg', 'profilo_tariffario', 'attivo')
    list_editable = ('attivo', 'costo')
    fieldsets = (
        (None, {'fields': (
            ('codice', 'descrizione', 'fornitore'),
            ('tipologia', 'sottotipo', 'attivo'),
            ('costo', 'tcg', 'profilo_tariffario'),
        )}),
    )


class ProfiloTelefoniaAdmin(TipoContrattoAdmin):
    list_display = (
        'codice', 'descrizione', 'fornitore', 'tipologia',
        'sottotipo', 'costo', 'tcg', 'profilo_tariffario',
        'soglia_minuti_inferiore', 'soglia_minuti_superiore', 'attivo'
    )
    list_editable = ('attivo', 'costo')
    fieldsets = (
        (None, {'fields': (
            ('codice', 'descrizione', 'fornitore'),
            ('tipologia', 'sottotipo', 'attivo'),
            ('soglia_minuti_inferiore', 'soglia_minuti_superiore'),
            ('costo', 'tcg', 'profilo_tariffario'),
        )}),
    )
    form = forms.ProfiloTelefoniaForm


class TipoGaraAdmin(admin.ModelAdmin):
    list_display = ('descrizione',)


class DettaglioSchemaProvvigioneStandardInline(admin.TabularInline):
    model = DettaglioSchemaProvvigioneStandard


class DettaglioValoreSchemaStandardInline(admin.TabularInline):
    model = DettaglioValoreSchemaStandard
    extra = 1


class SchemaProvvigioneStandardAdmin(admin.ModelAdmin):
    list_display = (
        'descrizione', 'fornitore', 'data_inizio_validita',
        'data_fine_validita', 'attivo'
    )
    list_filter = ('fornitore', 'attivo')
    fieldsets = (
        (None, {'fields': (
            ('descrizione', 'fornitore', 'tipologia'),
            ('data_inizio_validita', 'data_fine_validita', 'attivo'),
        )}),
    )
    list_editable = ('attivo',)
    inlines = [DettaglioValoreSchemaStandardInline, DettaglioSchemaProvvigioneStandardInline]


class DettaglioSchemaProvvigioneAgenteInline(admin.TabularInline):
    model = DettaglioSchemaProvvigioneAgente
    form = forms.DettaglioSchemaProvvigioneAgenteForm
    suit_classes = 'suit-tab suit-tab-dettaglio'


class DettaglioValoreSchemaAgenteInline(admin.TabularInline):
    model = DettaglioValoreSchemaAgente
    form = forms.DettaglioValoreSchemaAgenteForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-valore'


class CollegamentoSchemiProvvigioniInline(admin.TabularInline):
    model = CollegamentoSchemiProvvigioni
    form = forms.CollegamentoSchemiProvvigioniForm
    fields = ('schema_provvigione_capo_area', 'get_link_capoarea')
    readonly_fields = ('get_link_capoarea', )
    extra = 0
    fk_name = 'schema_provvigione_agente'
    suit_classes = 'suit-tab suit-tab-collegamenti'


class SchemaProvvigioneAgenteAdmin(admin.ModelAdmin):
    form = forms.SchemaProvvigioneAgenteForm
    list_display = (
        'agente', 'fornitore', 'tipologia', 'schema_provvigione',
        'data_inizio_validita', 'data_fine_validita', 'attivo'
    )
    list_filter = ('fornitore', 'agente', 'tipologia', 'attivo')
    fieldsets = (
        (
            'Dati Principali', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'agente',
                    'fornitore',
                    'tipologia',
                    'schema_provvigione',
                ))
        ),
        (
            'Validita\'', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'data_inizio_validita',
                    'data_fine_validita',
                    'attivo',
                ))
        ),
        (
            'Liquidazione', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'percentuale_liquidazione',
                    'mesi_liquidazione',
                ))
        ),
        (
            'Rate', dict(
                classes=('suit-tab suit-tab-testata',),
                fields=(
                    'percentuale_provvigione_rate',
                    'mesi_senza_rate',
                ))
        ),
    )
    list_editable = ('attivo',)
    inlines = [
        CollegamentoSchemiProvvigioniInline,
        DettaglioValoreSchemaAgenteInline,
        DettaglioSchemaProvvigioneAgenteInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('collegamenti', 'Coll. Schemi Provv.'),
        ('valore', 'Fasce Valore'),
        ('dettaglio', 'Dettaglio'),
    )


class SchemaProvvigioneCapoAreaAdmin(SchemaProvvigioneAgenteAdmin):
    list_display = (
        'agente', 'fornitore', 'tipologia', 'schema_provvigione',
        'data_inizio_validita', 'data_fine_validita', 'attivo'
    )
    form = forms.SchemaProvvigioneCapoAreaForm
    inlines = [
        DettaglioValoreSchemaAgenteInline,
        DettaglioSchemaProvvigioneAgenteInline
    ]
    suit_form_tabs = (
        ('testata', 'Testata'),
        ('valore', 'Fasce Valore'),
        ('dettaglio', 'Dettaglio'),
    )


class GestioneContrattoAdmin(admin.ModelAdmin):
    list_display = (
        'get_id_contratto_origine', 'get_categoria_contratto',
        'get_link_contratto_origine',
        'get_elenco_materiale_display', 'get_elenco_link_allegati',
        'get_link_progetto', 'get_elenco_canoni', 'get_link_offerta',
        'get_link_preordine', 'contratto_destinazione',
        'sposta_canoni', 'sposta_materiali', 'sposta_preordini',
        'sposta_offerte', 'sposta_progetto', 'sposta_allegati',
        'elimina_corrente', 'fatto'
    )
    list_filter = ('fatto', )
    raw_id_fields = ('contratto_destinazione',)
    list_editable = (
        'contratto_destinazione', 'sposta_canoni',
        'sposta_materiali', 'sposta_preordini', 'sposta_offerte',
        'sposta_progetto', 'sposta_allegati', 'elimina_corrente', 'fatto'
    )
    search_fields = (
        'contratto_origine__azienda',
        'contratto_origine__sede__anagrafica__alias',
        'contratto_origine__sede__anagrafica__ragione_sociale',
        'contratto_origine__id',
        'contratto_origine__sede__descrizione', 'contratto_origine__fornitore'
    )
