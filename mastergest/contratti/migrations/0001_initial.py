# -*- coding: utf-8 -*-


from django.db import models, migrations
import datetime
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CalcoloProvvigioniMensili',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('anno', models.PositiveIntegerField(default=2017)),
                ('mese', models.PositiveIntegerField(default=6)),
                ('numero_storni', models.PositiveIntegerField(default=0)),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('totale_provvigioni_agente', models.DecimalField(default=0, verbose_name=b'Tot. Provv. Ag.', max_digits=9, decimal_places=2)),
                ('totale_provvigioni_mastertraining', models.DecimalField(default=0, verbose_name=b'Tot. Provv. MT', max_digits=9, decimal_places=2)),
            ],
            options={
                'db_table': 'calcolo_provvigioni_mensili',
                'managed': False,
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='CalcoloProvvigioniTotale',
            fields=[
                ('id', models.CharField(max_length=200, serialize=False, primary_key=True)),
                ('anno', models.PositiveIntegerField(default=2017)),
                ('mese', models.PositiveIntegerField(default=6)),
                ('numero_storni', models.PositiveIntegerField(default=0)),
                ('numero_contratti', models.PositiveIntegerField(default=0)),
                ('totale_provvigioni_agente', models.DecimalField(default=0, verbose_name=b'Tot. Provv. Ag.', max_digits=9, decimal_places=2)),
                ('totale_provvigioni_mastertraining', models.DecimalField(default=0, verbose_name=b'Tot. Provv. MT', max_digits=9, decimal_places=2)),
            ],
            options={
                'ordering': ['-anno', '-mese', 'agente'],
                'verbose_name': 'calcolo provvigioni totale',
                'db_table': 'calcolo_provvigioni_totale',
                'managed': False,
                'verbose_name_plural': 'calcolo provvigioni totale',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ProvvigioneAgente',
            fields=[
                ('id', models.CharField(max_length=200, serialize=False, primary_key=True)),
                ('anno', models.PositiveIntegerField(default=2017)),
                ('mese', models.PositiveIntegerField(default=6)),
                ('numero_compensi', models.PositiveIntegerField(default=0, verbose_name=b'Num. Compensi')),
                ('numero_ricorrenti', models.PositiveIntegerField(default=0, verbose_name=b'Num. Ricorrenti')),
                ('numero_gettoni', models.PositiveIntegerField(default=0, verbose_name=b'Num. Gettoni')),
                ('totale', models.DecimalField(default=0, verbose_name=b'Tot. (\xe2\x82\xac)', max_digits=9, decimal_places=2)),
                ('totale_storni', models.DecimalField(default=0, verbose_name=b'Tot. Storni (\xe2\x82\xac)', max_digits=9, decimal_places=2)),
                ('totale_bonifici', models.DecimalField(default=0, verbose_name=b'Tot. Bonifici (\xe2\x82\xac)', max_digits=9, decimal_places=2)),
            ],
            options={
                'ordering': ['-anno', '-mese', 'agente'],
                'verbose_name': 'provvigione agente',
                'db_table': 'provvigioni_agente',
                'managed': False,
                'verbose_name_plural': 'provvigioni agente',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ProvvigioneAgenteFornitore',
            fields=[
                ('id', models.CharField(max_length=200, serialize=False, primary_key=True)),
                ('anno', models.PositiveIntegerField(default=2017)),
                ('mese', models.PositiveIntegerField(default=6)),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('numero_compensi', models.PositiveIntegerField(default=0)),
                ('totale', models.DecimalField(default=0, verbose_name=b'Tot. Provv.', max_digits=9, decimal_places=2)),
            ],
            options={
                'ordering': ['-anno', '-mese', 'agente', 'fornitore'],
                'verbose_name': 'provvigione agente fornitore',
                'db_table': 'provvigioni_agente_fornitore',
                'managed': False,
                'verbose_name_plural': 'provvigioni agente fornitore',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Agente',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('cognome', models.CharField(max_length=200)),
                ('nome', models.CharField(max_length=200)),
                ('mastertraining', models.BooleanField(default=False)),
                ('telefono', models.CharField(max_length=200, null=True, blank=True)),
                ('email', models.CharField(max_length=200, null=True, blank=True)),
                ('tipo', models.CharField(blank=True, max_length=200, null=True, choices=[(b'struttura', b'struttura'), (b'capo_area', b'capo area'), (b'base', b'base')])),
                ('sottotipo', models.CharField(blank=True, max_length=200, null=True, choices=[(b'distributore', b'distributore'), (b'agente', b'agente'), (b'procacciatore', b'procacciatore'), (b'consulente', b'consulente'), (b'collaboratore', b'collaboratore'), (b'altro', b'altro')])),
                ('stato_documenti', models.CharField(blank=True, max_length=200, null=True, choices=[(b'manca', b'manca'), (b'manca_firma', b'manca firma'), (b'ok', b'ok'), (b'disdetto', b'disdetto'), (b'da_correggere', b'da correggere')])),
                ('attivo', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['cognome', 'nome'],
                'verbose_name_plural': 'agenti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='CollegamentoSchemiProvvigioni',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
            ],
            options={
                'verbose_name': 'collegamenti schemi provvigioni',
                'verbose_name_plural': 'collegamenti schemi provvigioni',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Compenso',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('data', models.DateField(verbose_name=b'data')),
                ('tipo', models.CharField(default=b'provvigione', max_length=200, verbose_name=b'tipo', choices=[(b'provvigione', b'provvigione'), (b'storno', b'storno'), (b'bonifico', b'bonifico'), (b'rata', b'rata'), (b'gara', b'gara')])),
                ('importo', models.DecimalField(default=0, verbose_name=b'importo', max_digits=9, decimal_places=2)),
                ('valore', models.DecimalField(default=0, max_digits=9, decimal_places=2)),
                ('descrizione', models.CharField(max_length=200, null=True, blank=True)),
                ('consolidato', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-data', 'contratto', 'agente', 'tipo'],
                'verbose_name_plural': 'compensi',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Contratto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('azienda', models.CharField(max_length=200)),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('tipologia', models.CharField(max_length=200, choices=[(b'fisso', b'fisso'), (b'mobile', b'mobile')])),
                ('account', models.CharField(max_length=200, null=True, blank=True)),
                ('tecnologia', models.CharField(blank=True, max_length=200, null=True, choices=[(b'ADSL_ULL', b'ADSL_ULL'), (b'ADSL_WHS', b'ADSL_WHS'), (b'CVP_HDSL', b'CVP_HDSL'), (b'FIBRA', b'FIBRA'), (b'SHDSL', b'SHDSL')])),
                ('inserita_pda', models.BooleanField(default=False)),
                ('data_inserimento_pda', models.DateField(null=True, verbose_name=b'inserimento PDA', blank=True)),
                ('data_stipula', models.DateField(null=True, verbose_name=b'stipula', blank=True)),
                ('data_consegna', models.DateField(default=datetime.date(2017, 6, 21), verbose_name=b'competenza')),
                ('data_attivazione', models.DateField(null=True, verbose_name=b'attivazione', blank=True)),
                ('fatturato', models.BooleanField(default=False)),
                ('data_fatturazione', models.DateField(null=True, verbose_name=b'fatturazione', blank=True)),
                ('pagato', models.BooleanField(default=False)),
                ('note', models.TextField(blank=True)),
                ('note_stato', models.TextField(blank=True)),
                ('gettone_totale_agente', models.DecimalField(default=0, verbose_name=b'gettone tot.ag.', max_digits=9, decimal_places=2)),
                ('premio_agente', models.DecimalField(default=0, verbose_name=b'premio ag.', max_digits=9, decimal_places=2)),
                ('totale_agente', models.DecimalField(default=0, verbose_name=b'tot. ag.', max_digits=9, decimal_places=2)),
                ('gettone_totale_mastertraining', models.DecimalField(default=0, verbose_name=b'gettone MT', max_digits=9, decimal_places=2)),
                ('gara_accelarazione', models.DecimalField(default=0, verbose_name=b'gara acc.', max_digits=9, decimal_places=2)),
                ('gara_valore', models.DecimalField(default=0, verbose_name=b'gara val.', max_digits=9, decimal_places=2)),
                ('gettone_quantita', models.DecimalField(default=0, verbose_name=b'gara qta', max_digits=9, decimal_places=2)),
                ('totale_mastertraining', models.DecimalField(default=0, verbose_name=b'tot. MT', max_digits=9, decimal_places=2)),
                ('totale_prodotti', models.PositiveIntegerField(default=0)),
                ('numero_storni', models.PositiveIntegerField(default=0)),
                ('importo_rata_mensile', models.DecimalField(default=0, verbose_name=b'imp. rata mensile', max_digits=9, decimal_places=2)),
                ('importo_rata_mensile_agente', models.DecimalField(default=0, verbose_name=b'imp. rata mensile agente', max_digits=9, decimal_places=2)),
                ('data_inizio_rate', models.DateField(null=True, verbose_name=b'data collaudo/inst.', blank=True)),
                ('costo_materiale', models.DecimalField(default=0, verbose_name=b'costo materiale', max_digits=9, decimal_places=2)),
                ('numero_rate', models.PositiveIntegerField(default=0)),
                ('valore_contratto', models.DecimalField(default=Decimal('0.00'), null=True, verbose_name=b'valore contratto', max_digits=9, decimal_places=2)),
                ('numero_mesi_valore_contratto', models.PositiveIntegerField(default=0, verbose_name=b'Num. mesi valore contratto')),
                ('gettone_valore_contratto', models.DecimalField(default=0, verbose_name=b'gettone valore contratto agente', max_digits=9, decimal_places=2)),
                ('gettone_valore_contratto_master', models.DecimalField(default=0, verbose_name=b'gettone valore contratto master', max_digits=9, decimal_places=2)),
                ('provincia', models.CharField(max_length=50, null=True, verbose_name=b'Prov.', blank=True)),
                ('numero_contratti_fastweb', models.PositiveIntegerField(default=0, verbose_name=b'N. Contr. FW')),
                ('data_chiusura_fastweb', models.DateField(null=True, verbose_name=b'Data Chiusura FW', blank=True)),
                ('documentazione', models.BooleanField(default=True, verbose_name=b'doc. completa')),
                ('note_documentazione', models.TextField(blank=True)),
                ('forma_contratto', models.CharField(blank=True, max_length=200, null=True, verbose_name=b'forma contr.', choices=[(b'comodato', b"Comodato d'uso"), (b'prova', b'Comodato con prova'), (b'vendita', b'Vendita'), (b'rent', b'Rent Strumentale'), (b'open', b'Open'), (b'opentop', b'Opentop')])),
                ('tipo_installazione', models.CharField(blank=True, max_length=200, null=True, verbose_name=b'tipo inst.', choices=[(b'mv', b'Mastervoice'), (b'mg', b'Mastergate'), (b'upgrade_mv', b'upgrade Mastervoice'), (b'accessori', b'accessori'), (b'ipkom', b'numeri IPKOM'), (b'kpnquest', b'KPN')])),
                ('sopralluogo', models.CharField(blank=True, max_length=200, null=True, choices=[(b'si', b'si'), (b'no', b'no'), (b'fatto', b'fatto')])),
                ('data_sopralluogo', models.DateField(null=True, verbose_name=b'data sopralluogo', blank=True)),
                ('data_consegna_contratto', models.DateField(default=datetime.date(2017, 6, 21), verbose_name=b'consegna')),
                ('data_fine_prova', models.DateField(null=True, verbose_name=b'fine prova', blank=True)),
                ('stato_installazione', models.CharField(blank=True, max_length=200, null=True, verbose_name=b'stato inst.', choices=[(b'attesa_linea', b'in attesa linea'), (b'da_installare', b'da installare'), (b'test', b'in test'), (b'bloccato', b'bloccato (vedi note)'), (b'installato', b'installato'), (b'collaudato', b'collaudato')])),
                ('referente', models.CharField(max_length=200, null=True, blank=True)),
                ('ruolo_referente', models.CharField(max_length=200, null=True, blank=True)),
                ('telefono_referente', models.CharField(max_length=200, null=True, blank=True)),
                ('email_referente', models.CharField(max_length=200, null=True, blank=True)),
                ('project_manager', models.CharField(max_length=200, null=True, blank=True)),
                ('tipo_data_attivazione', models.CharField(blank=True, max_length=200, null=True, choices=[(b'presunta', b'presunta'), (b'comunicata', b'comunicata'), (b'reale', b'reale')])),
                ('allegato_contratto_mastervoice', models.BooleanField(default=False, verbose_name=b'All. Contr. MV')),
                ('data_portabilita', models.DateField(null=True, blank=True)),
                ('tipo_data_portabilita', models.CharField(blank=True, max_length=200, null=True, choices=[(b'presunta', b'presunta'), (b'comunicata', b'comunicata'), (b'reale', b'reale')])),
                ('cosa_deve_fare', models.TextField(blank=True)),
                ('stato_fastweb', models.CharField(blank=True, max_length=200, null=True, verbose_name=b'stato FW', choices=[(b'aperto', b'aperto'), (b'chiuso', b'chiuso')])),
                ('data_firma_contratto', models.DateField(null=True, blank=True)),
                ('tipo_servizio', models.CharField(max_length=200, null=True, blank=True)),
                ('verifica_tecnica', models.CharField(blank=True, max_length=200, null=True, choices=[(b'si', b'si'), (b'no', b'no'), (b'fatto', b'fatto')])),
                ('data_verifica_tecnica', models.DateField(null=True, verbose_name=b'data verifica tecnica', blank=True)),
                ('categoria', models.CharField(default=b'installazione', max_length=200, null=True, blank=True, choices=[(b'installazione', b'installazione'), (b'canone', b'canone')])),
                ('tipo_contratto_mastercom', models.CharField(blank=True, max_length=200, null=True, verbose_name=b'tipo contratto', choices=[(b'sistema', b'Sistema Centrale'), (b'rete', b'Rete'), (b'consumabili', b'Consumabili'), (b'accessori', b'Accessori')])),
            ],
            options={
                'ordering': ['-data_stipula', '-data_consegna'],
                'verbose_name_plural': 'contratti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='DettaglioContratto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('quantita', models.PositiveIntegerField(default=1, verbose_name=b'valore/q.ta')),
                ('gettone_agente', models.DecimalField(default=0, max_digits=9, decimal_places=2)),
                ('storno_agente', models.DecimalField(default=0, max_digits=9, decimal_places=2)),
                ('gettone_mastertraining', models.DecimalField(default=0, max_digits=9, decimal_places=2)),
                ('totale_agente', models.DecimalField(default=0, max_digits=9, decimal_places=2)),
                ('totale_mastertraining', models.DecimalField(default=0, max_digits=9, decimal_places=2)),
            ],
            options={
                'ordering': ['contratto', 'tipo_contratto'],
                'verbose_name_plural': 'dettagli contratto',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='DettaglioSchemaProvvigioneAgente',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('provvigione', models.DecimalField(null=True, max_digits=9, decimal_places=2, blank=True)),
            ],
            options={
                'ordering': ['tipo_contratto'],
                'verbose_name_plural': 'dettagli schema provvigione agente',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='DettaglioSchemaProvvigioneStandard',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('provvigione', models.DecimalField(null=True, max_digits=9, decimal_places=2, blank=True)),
            ],
            options={
                'ordering': ['tipo_contratto'],
                'verbose_name_plural': 'dettagli schema provvigione standard',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='DettaglioValoreSchemaAgente',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('da_quantita', models.PositiveIntegerField(default=1, verbose_name=b'Da')),
                ('a_quantita', models.PositiveIntegerField(default=1, verbose_name=b'A')),
                ('provvigione', models.DecimalField(null=True, max_digits=9, decimal_places=2, blank=True)),
            ],
            options={
                'ordering': ['da_quantita'],
                'verbose_name_plural': 'Fasce Valore Contratto (Agente)',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='DettaglioValoreSchemaStandard',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('da_quantita', models.PositiveIntegerField(default=1, verbose_name=b'Da')),
                ('a_quantita', models.PositiveIntegerField(default=1, verbose_name=b'A')),
                ('provvigione', models.DecimalField(null=True, max_digits=9, decimal_places=2, blank=True)),
            ],
            options={
                'ordering': ['da_quantita'],
                'verbose_name_plural': 'Fasce Valore Contratto (Standard)',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='GestioneContratto',
            fields=[
                ('contratto_origine', models.OneToOneField(related_name='contratto_origine_pk', primary_key=True, serialize=False, to='contratti.Contratto', verbose_name=b'contr. da elim.', on_delete=models.deletion.CASCADE)),
                ('sposta_canoni', models.BooleanField(default=True, verbose_name=b'can.')),
                ('sposta_materiali', models.BooleanField(default=True, verbose_name=b'mat.')),
                ('sposta_preordini', models.BooleanField(default=True, verbose_name=b'pre.')),
                ('sposta_offerte', models.BooleanField(default=True, verbose_name=b'off.')),
                ('sposta_progetto', models.BooleanField(default=True, verbose_name=b'prog.')),
                ('sposta_allegati', models.BooleanField(default=True, verbose_name=b'all.')),
                ('elimina_corrente', models.BooleanField(default=True, verbose_name=b'del.')),
                ('fatto', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['contratto_origine__azienda', 'contratto_origine__data_consegna_contratto'],
                'verbose_name_plural': 'gestione contratti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='MaterialeContratto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('quantita', models.PositiveIntegerField(default=1)),
                ('prezzo_listino', models.DecimalField(null=True, verbose_name=b'prezzo listino (euro)', max_digits=9, decimal_places=2, blank=True)),
                ('prezzo_vendita', models.DecimalField(null=True, verbose_name=b'prezzo vendita (euro)', max_digits=9, decimal_places=2, blank=True)),
                ('totale_vendita', models.DecimalField(null=True, verbose_name=b'totale (euro)', max_digits=9, decimal_places=2, blank=True)),
                ('quantita_installata', models.PositiveIntegerField(default=0)),
                ('tipo', models.CharField(blank=True, max_length=200, null=True, choices=[(b'server', b'server MAstercom'), (b'tablet', b'tablet'), (b'firewall', b'Firewall'), (b'totem', b'Mastervoice'), (b'elms', b'elms'), (b'clinic_point', b'Clinic Point'), (b'access_point', b'Access Point'), (b'rete', b'Rete'), (b'elettrico', b'Elettrico'), (b'accessori', b'accessori')])),
            ],
            options={
                'ordering': ('contratto', 'id'),
                'verbose_name_plural': 'materiale contratto',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='MaterialePreordine',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('quantita', models.PositiveIntegerField(default=1)),
                ('prezzo_listino', models.DecimalField(null=True, verbose_name=b'prezzo listino (euro)', max_digits=9, decimal_places=2, blank=True)),
                ('prezzo_vendita', models.DecimalField(null=True, verbose_name=b'prezzo vendita (euro)', max_digits=9, decimal_places=2, blank=True)),
                ('totale_vendita', models.DecimalField(null=True, verbose_name=b'totale (euro)', max_digits=9, decimal_places=2, blank=True)),
            ],
            options={
                'ordering': ('preordine', 'id'),
                'verbose_name_plural': 'materiale preordine',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='MotivazioneStatoContratto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('descrizione', models.CharField(max_length=200)),
            ],
            options={
                'ordering': ['descrizione'],
                'verbose_name_plural': 'motivazioni stato contratto',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Preordine',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('azienda', models.CharField(max_length=200)),
                ('sede', models.CharField(max_length=200, null=True, blank=True)),
                ('provincia', models.CharField(max_length=200)),
                ('citta', models.CharField(max_length=200, null=True, blank=True)),
                ('tipo_installazione', models.CharField(blank=True, max_length=200, null=True, verbose_name=b'tipo inst.', choices=[(b'mv', b'Mastervoice'), (b'mg', b'Mastergate'), (b'upgrade_mv', b'upgrade Mastervoice'), (b'accessori', b'accessori'), (b'ipkom', b'numeri IPKOM'), (b'kpnquest', b'KPN')])),
                ('tipo', models.CharField(max_length=200, choices=[(b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'mastervoice', b'Mastervoice'), (b'ipkom', b'IpKom'), (b'kpnquest', b'KPNQuest'), (b'mastercom', b'MasterCom')])),
                ('referente', models.CharField(max_length=200, null=True, blank=True)),
                ('ruolo_referente', models.CharField(max_length=200, null=True, blank=True)),
                ('telefono_referente', models.CharField(max_length=200, null=True, blank=True)),
                ('email_referente', models.CharField(max_length=200, null=True, blank=True)),
                ('data_firma_contratto', models.DateField(verbose_name=b'data firma contratto')),
                ('verifica_tecnica', models.CharField(blank=True, max_length=200, null=True, choices=[(b'si', b'si'), (b'no', b'no'), (b'fatto', b'fatto')])),
                ('data_verifica_tecnica', models.DateField(null=True, verbose_name=b'data verifica tecnica', blank=True)),
                ('note', models.TextField(blank=True)),
                ('tipo_servizio', models.CharField(max_length=200, null=True, blank=True)),
                ('chiuso', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['data_firma_contratto', 'azienda'],
                'verbose_name_plural': 'preordini',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='RiepilogoProvvigioni',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('anno', models.PositiveIntegerField(default=2017)),
                ('mese', models.PositiveIntegerField(default=6)),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('percentuale_liquidabile', models.PositiveIntegerField(null=True, verbose_name=b'Perc. Liq.', blank=True)),
                ('totale_liquidato', models.DecimalField(default=0, verbose_name=b'Liq.ato', max_digits=9, decimal_places=2)),
                ('gara_premio', models.DecimalField(default=0, verbose_name=b'Gara AG', max_digits=9, decimal_places=2)),
            ],
            options={
                'ordering': ['-anno', '-mese', 'agente', 'fornitore'],
                'verbose_name_plural': 'riepiloghi provvigioni',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='SchemaProvvigioneAgente',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('tipologia', models.CharField(max_length=200, choices=[(b'fisso', b'fisso'), (b'mobile', b'mobile')])),
                ('percentuale_liquidazione', models.PositiveIntegerField(default=80)),
                ('mesi_liquidazione', models.PositiveIntegerField(default=6)),
                ('data_inizio_validita', models.DateField(default=datetime.date(2017, 6, 21))),
                ('data_fine_validita', models.DateField()),
                ('percentuale_provvigione_rate', models.PositiveIntegerField(default=0)),
                ('attivo', models.BooleanField(default=True)),
                ('capo_area', models.BooleanField(default=False)),
                ('mesi_senza_rate', models.PositiveIntegerField(default=0, help_text=b"numero di mesi dalla data di collaudo in cui l'agente non percepisce rate.")),
            ],
            options={
                'ordering': ['agente', 'fornitore', 'data_inizio_validita', 'data_fine_validita'],
                'verbose_name_plural': 'schema provvigione agente',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='SchemaProvvigioneStandard',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('descrizione', models.CharField(max_length=200)),
                ('data_inizio_validita', models.DateField(default=datetime.date(2017, 6, 21))),
                ('data_fine_validita', models.DateField()),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('tipologia', models.CharField(max_length=200, choices=[(b'fisso', b'fisso'), (b'mobile', b'mobile')])),
                ('attivo', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['fornitore', 'data_inizio_validita', 'data_fine_validita'],
                'verbose_name_plural': 'schema provvigione standard',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='StatoContratto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('descrizione', models.CharField(max_length=200)),
                ('ignora_provvigioni_relative', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['descrizione'],
                'verbose_name_plural': 'stati contratto',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='TipoContratto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('codice', models.CharField(max_length=200)),
                ('descrizione', models.CharField(max_length=200)),
                ('fornitore', models.CharField(max_length=200, choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')])),
                ('tipologia', models.CharField(max_length=200, choices=[(b'fisso', b'fisso'), (b'mobile', b'mobile')])),
                ('sottotipo', models.CharField(blank=True, max_length=200, null=True, choices=[(b'voce', b'voce'), (b'dati', b'dati'), (b'opzione', b'opzione')])),
                ('attivo', models.BooleanField(default=True)),
                ('costo', models.DecimalField(null=True, verbose_name=b'costo al bimestre (euro)', max_digits=9, decimal_places=2, blank=True)),
                ('tcg', models.DecimalField(default=Decimal('25.82'), verbose_name=b'TCG', max_digits=9, decimal_places=2)),
                ('per_analisi', models.BooleanField(default=True)),
                ('profilo_standard', models.BooleanField(default=False)),
                ('soglia_minuti_inferiore', models.PositiveIntegerField(default=0)),
                ('soglia_minuti_superiore', models.PositiveIntegerField(default=0)),
            ],
            options={
                'ordering': ['fornitore', 'tipologia', 'codice', 'descrizione'],
                'verbose_name_plural': 'tipi contratti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='TipoGara',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('descrizione', models.CharField(max_length=200)),
            ],
            options={
                'verbose_name_plural': 'tipi gare',
            },
            bases=(models.Model,),
        ),
    ]
