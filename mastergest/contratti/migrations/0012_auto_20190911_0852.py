# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-09-11 06:52
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0011_auto_20190806_0955'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContrattoLineaIpkom',
            fields=[
            ],
            options={
                'verbose_name': 'contratto linea ipkom',
                'proxy': True,
                'verbose_name_plural': 'contratti linee ipkom',
                'indexes': [],
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='PreordineLineaIpkom',
            fields=[
            ],
            options={
                'verbose_name': 'preordine linea ipkom',
                'proxy': True,
                'verbose_name_plural': 'preordini linee ipkom',
                'indexes': [],
            },
            bases=('contratti.preordine',),
        ),
        migrations.Alter<PERSON>ield(
            model_name='contratto',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='tipo',
            field=models.CharField(choices=[(b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'mastervoice', b'Mastervoice'), (b'lineaipkom', b'Linea IpKom'), (b'ipkom', b'IpKom'), (b'kpnquest', b'KPNQuest'), (b'mastercom', b'MasterCom')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.CreateModel(
            name='InstallazioneLineaIpkom',
            fields=[
            ],
            options={
                'verbose_name': 'installazione linea ipkom',
                'proxy': True,
                'verbose_name_plural': 'installazioni linee ipkom',
                'indexes': [],
            },
            bases=('contratti.contrattolineaipkom',),
        ),
    ]
