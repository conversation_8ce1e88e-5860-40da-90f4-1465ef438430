# Generated by Django 2.2.28 on 2024-05-21 09:09

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0028_contratto_gestione_azienda'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContrattoMagister',
            fields=[
            ],
            options={
                'verbose_name_plural': 'Contratti Magister',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoMastertraining',
            fields=[
            ],
            options={
                'verbose_name_plural': 'Contratti Mastertraining',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.contratto',),
        ),
    ]
