# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0006_contratto_premio_rata_mensile_agente'),
    ]

    operations = [
        migrations.AddField(
            model_name='contratto',
            name='descrizione_aggiuntiva_fattura_canone',
            field=models.CharField(max_length=200, null=True, verbose_name=b'descr. agg. fattura', blank=True),
        ),
        migrations.AddField(
            model_name='contratto',
            name='numero_rate_canone',
            field=models.PositiveIntegerField(default=1, verbose_name=b'rate'),
        ),
        migrations.AddField(
            model_name='contratto',
            name='periodicita_canone',
            field=models.CharField(default=360, max_length=200, choices=[(b'30', b'mensile'), (b'60', b'2 mesi'), (b'90', b'3 mesi'), (b'120', b'4 mesi'), (b'150', b'5 mesi'), (b'180', b'semestrale'), (b'270', b'9 mesi'), (b'360', b'annuale'), (b'720', b'biannuale'), (b'1080', b'triannuale')]),
        ),
        migrations.AddField(
            model_name='contratto',
            name='tipo_rinnovo_canone',
            field=models.CharField(default=b'manuale', max_length=200, choices=[(b'tacito', b'tacito'), (b'manuale', b'manuale')]),
        ),
    ]
