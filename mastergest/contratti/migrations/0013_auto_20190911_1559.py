# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-09-11 13:59
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0012_auto_20190911_0852'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContrattoTwt',
            fields=[
            ],
            options={
                'verbose_name': 'contratto twt',
                'proxy': True,
                'verbose_name_plural': 'contratti twt',
                'indexes': [],
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='PreordineTwt',
            fields=[
            ],
            options={
                'verbose_name': 'preordine twt',
                'proxy': True,
                'verbose_name_plural': 'preordini twt',
                'indexes': [],
            },
            bases=('contratti.preordine',),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.Char<PERSON>ield(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'txt', b'Twt'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'txt', b'Twt'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'txt', b'Twt'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'txt', b'Twt'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[(b'acanto', b'Acantho'), (b'digitel', b'Digitel'), (b'fastweb', b'FastWeb'), (b'fastweb_reseller', b'FastWeb Reseller'), (b'3italia', b'H3G'), (b'kpnquest', b'KPNQuest'), (b'lineaipkom', b'Linea Ipkom'), (b'ipkom', b'Ipkom'), (b'mastercom', b'Mastercom'), (b'mastervoice', b'Mastervoice'), (b'mclink', b'MC Link'), (b'ngi', b'NGI'), (b'onesim', b'OneSim'), (b'tim', b'Tim'), (b'trenove', b'Trenove'), (b'txt', b'Twt'), (b'vodafone', b'Vodafone'), (b'welcome', b'Welcome'), (b'wind', b'Wind')], max_length=200),
        ),
        migrations.CreateModel(
            name='InstallazioneTwt',
            fields=[
            ],
            options={
                'verbose_name': 'installazione twt',
                'proxy': True,
                'verbose_name_plural': 'installazioni twt',
                'indexes': [],
            },
            bases=('contratti.contrattotwt',),
        ),
    ]
