# Generated by Django 2.2.28 on 2024-05-24 07:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0029_contrattomagister_contrattomastertraining'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('magister', 'Magister'), ('mclink', 'MC <PERSON>'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipologia',
            field=models.CharField(blank=True, choices=[('fisso', 'fisso'), ('mobile', 'mobile')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('magister', 'Magister'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('magister', 'Magister'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('magister', 'Magister'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('magister', 'Magister'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
    ]
