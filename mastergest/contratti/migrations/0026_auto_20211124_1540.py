# Generated by Django 2.2.12 on 2021-11-24 14:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0025_auto_20210928_1629'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContrattoBriantel',
            fields=[
            ],
            options={
                'verbose_name': 'contratto briantel',
                'verbose_name_plural': 'contratti briantel',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='PreordineBriantel',
            fields=[
            ],
            options={
                'verbose_name': 'preordine briantel',
                'verbose_name_plural': 'preordini briantel',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.preordine',),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('briantel', 'Briantel'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.CreateModel(
            name='InstallazioneBriantel',
            fields=[
            ],
            options={
                'verbose_name': 'installazione briantel',
                'verbose_name_plural': 'installazioni briantel',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.contrattobriantel',),
        ),
    ]
