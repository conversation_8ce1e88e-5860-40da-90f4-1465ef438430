# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-11 09:02
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0015_auto_20191008_0939'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
    ]
