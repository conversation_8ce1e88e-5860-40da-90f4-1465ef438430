# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-11-06 10:08
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0016_auto_20191011_1102'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContrattoHal',
            fields=[
            ],
            options={
                'verbose_name': 'contratto hal',
                'verbose_name_plural': 'contratti hal',
                'proxy': True,
                'indexes': [],
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='PreordineHal',
            fields=[
            ],
            options={
                'verbose_name': 'preordine hal',
                'verbose_name_plural': 'preordini hal',
                'proxy': True,
                'indexes': [],
            },
            bases=('contratti.preordine',),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.Char<PERSON>ield(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.CreateModel(
            name='InstallazioneHal',
            fields=[
            ],
            options={
                'verbose_name': 'installazione hal',
                'verbose_name_plural': 'installazioni hal',
                'proxy': True,
                'indexes': [],
            },
            bases=('contratti.contrattohal',),
        ),
    ]
