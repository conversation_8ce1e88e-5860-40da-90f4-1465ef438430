# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('anagrafe', '0002_auto_20170621_1605'),
        ('offerte', '0001_initial'),
        ('mastervoice', '0001_initial'),
        ('contratti', '0001_initial'),
        ('listino', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='tipocontratto',
            name='profilo_tariffario',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='offerte.ProfiloTariffario', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='tipocontratto',
            name='tipo_gara',
            field=models.ForeignKey(blank=True, to='contratti.TipoGara', null=True, on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='schemaprovvigioneagente',
            name='agente',
            field=models.ForeignKey(to='contratti.Agente', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='schemaprovvigioneagente',
            name='schema_provvigione',
            field=models.ForeignKey(to='contratti.SchemaProvvigioneStandard', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='riepilogoprovvigioni',
            name='agente',
            field=models.ForeignKey(to='contratti.Agente', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='preordine',
            name='agente',
            field=models.ForeignKey(to='contratti.Agente', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='preordine',
            name='offerta',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='offerte.Offerta', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='preordine',
            name='progetto',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='mastervoice.Progetto', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='preordine',
            name='sede_cliente',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='anagrafe.Sede', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='materialepreordine',
            name='preordine',
            field=models.ForeignKey(to='contratti.Preordine', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='materialepreordine',
            name='prodotto',
            field=models.ForeignKey(to='listino.Prodotto', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='materialecontratto',
            name='contratto',
            field=models.ForeignKey(to='contratti.Contratto', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='materialecontratto',
            name='prodotto',
            field=models.ForeignKey(to='listino.Prodotto', on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='gestionecontratto',
            name='contratto_destinazione',
            field=models.ForeignKey(related_name='contratto_destinazione_pk', verbose_name=b'contr. che rim.', blank=True, to='contratti.Contratto', null=True, on_delete=models.deletion.CASCADE),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='dettagliovaloreschemastandard',
            name='schema_provvigione',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.SchemaProvvigioneStandard'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='dettagliovaloreschemaagente',
            name='schema_provvigione',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.SchemaProvvigioneAgente'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='dettaglioschemaprovvigionestandard',
            name='schema_provvigione',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.SchemaProvvigioneStandard'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='dettaglioschemaprovvigionestandard',
            name='tipo_contratto',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.TipoContratto'),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name='dettaglioschemaprovvigionestandard',
            unique_together=set([('schema_provvigione', 'tipo_contratto')]),
        ),
        migrations.AddField(
            model_name='dettaglioschemaprovvigioneagente',
            name='schema_provvigione',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.SchemaProvvigioneAgente'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='dettaglioschemaprovvigioneagente',
            name='tipo_contratto',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.TipoContratto'),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name='dettaglioschemaprovvigioneagente',
            unique_together=set([('schema_provvigione', 'tipo_contratto')]),
        ),
        migrations.AddField(
            model_name='dettagliocontratto',
            name='contratto',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.Contratto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='dettagliocontratto',
            name='tipo_contratto',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.TipoContratto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='agente',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.Agente'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='chi_deve_fare',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to=settings.AUTH_USER_MODEL, null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='contratto_master',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='contratti.Contratto', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='motivazione_stato',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='contratti.MotivazioneStatoContratto', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='offerta',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='offerte.Offerta', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='preordine',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='contratti.Preordine', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='progetto',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='mastervoice.Progetto', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='sede',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='anagrafe.Sede', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='contratto',
            name='stato',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='contratti.StatoContratto', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='compenso',
            name='agente',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, to='contratti.Agente'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='compenso',
            name='contratto',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, blank=True, to='contratti.Contratto', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='collegamentoschemiprovvigioni',
            name='schema_provvigione_agente',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='schema_provvigione_agente_pk', to='contratti.SchemaProvvigioneAgente'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='agente',
            name='capo_area',
            field=models.ForeignKey(related_name='capo_area_pk', on_delete=django.db.models.deletion.SET_NULL, blank=True, to='contratti.Agente', null=True),
            preserve_default=True,
        ),
        migrations.CreateModel(
            name='CompensoAgente',
            fields=[
            ],
            options={
                'ordering': ('contratto__azienda',),
                'verbose_name': 'compenso da maturare',
                'proxy': True,
                'verbose_name_plural': 'compensi da maturare',
            },
            bases=('contratti.compenso',),
        ),
        migrations.CreateModel(
            name='Contratto3',
            fields=[
            ],
            options={
                'verbose_name': 'contratto H3g Italia',
                'proxy': True,
                'verbose_name_plural': 'contratti H3g Italia',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoAcanto',
            fields=[
            ],
            options={
                'verbose_name': 'contratto Acantho',
                'proxy': True,
                'verbose_name_plural': 'contratti Acantho',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoDigitel',
            fields=[
            ],
            options={
                'verbose_name': 'contratto Digitel',
                'proxy': True,
                'verbose_name_plural': 'contratti Digitel',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoFastweb',
            fields=[
            ],
            options={
                'verbose_name': 'contratto fastweb',
                'proxy': True,
                'verbose_name_plural': 'contratti fastweb',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoFastwebReseller',
            fields=[
            ],
            options={
                'verbose_name': 'contratto fastweb reseller',
                'proxy': True,
                'verbose_name_plural': 'contratti fastweb reseller',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoIpkom',
            fields=[
            ],
            options={
                'verbose_name': 'contratto ipkom',
                'proxy': True,
                'verbose_name_plural': 'contratti ipkom',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoKpnquest',
            fields=[
            ],
            options={
                'verbose_name': 'contratto KpnQuest',
                'proxy': True,
                'verbose_name_plural': 'contratti KpnQuest',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoMastercom',
            fields=[
            ],
            options={
                'verbose_name': "Buono d'ordine mastercom",
                'proxy': True,
                'verbose_name_plural': "Buoni d'ordine mastercom",
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoMastervoice',
            fields=[
            ],
            options={
                'verbose_name': 'contratto mastervoice',
                'proxy': True,
                'verbose_name_plural': 'contratti mastervoice',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoMcLink',
            fields=[
            ],
            options={
                'verbose_name': 'contratto McLink',
                'proxy': True,
                'verbose_name_plural': 'contratti McLink',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoNgi',
            fields=[
            ],
            options={
                'verbose_name': 'contratto NGI',
                'proxy': True,
                'verbose_name_plural': 'contratti NGI',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoOnesim',
            fields=[
            ],
            options={
                'verbose_name': 'contratto onesim',
                'proxy': True,
                'verbose_name_plural': 'contratti onesim',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoTim',
            fields=[
            ],
            options={
                'verbose_name': 'contratto Tim',
                'proxy': True,
                'verbose_name_plural': 'contratti Tim',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoTrenove',
            fields=[
            ],
            options={
                'verbose_name': 'contratto Trenove',
                'proxy': True,
                'verbose_name_plural': 'contratti Trenove',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoVodafone',
            fields=[
            ],
            options={
                'verbose_name': 'contratto vodafone',
                'proxy': True,
                'verbose_name_plural': 'contratti vodafone',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoWelcome',
            fields=[
            ],
            options={
                'verbose_name': 'contratto Welcome',
                'proxy': True,
                'verbose_name_plural': 'contratti Welcome',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='ContrattoWind',
            fields=[
            ],
            options={
                'verbose_name': 'contratto wind',
                'proxy': True,
                'verbose_name_plural': 'contratti wind',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='InstallazioneAcanto',
            fields=[
            ],
            options={
                'verbose_name': 'installazione acantho',
                'proxy': True,
                'verbose_name_plural': 'installazioni acantho',
            },
            bases=('contratti.contrattoacanto',),
        ),
        migrations.CreateModel(
            name='InstallazioneDigitel',
            fields=[
            ],
            options={
                'verbose_name': 'installazione digitel',
                'proxy': True,
                'verbose_name_plural': 'installazioni digitel',
            },
            bases=('contratti.contrattodigitel',),
        ),
        migrations.CreateModel(
            name='InstallazioneFastweb',
            fields=[
            ],
            options={
                'verbose_name': 'installazione fastweb',
                'proxy': True,
                'verbose_name_plural': 'installazioni fastweb',
            },
            bases=('contratti.contrattofastweb',),
        ),
        migrations.CreateModel(
            name='InstallazioneFastwebReseller',
            fields=[
            ],
            options={
                'verbose_name': 'installazione fastweb reseller',
                'proxy': True,
                'verbose_name_plural': 'installazioni fastweb reseller',
            },
            bases=('contratti.contrattofastwebreseller',),
        ),
        migrations.CreateModel(
            name='InstallazioneIpkom',
            fields=[
            ],
            options={
                'verbose_name': 'installazione ipkom',
                'proxy': True,
                'verbose_name_plural': 'installazioni ipkom',
            },
            bases=('contratti.contrattoipkom',),
        ),
        migrations.CreateModel(
            name='InstallazioneKpnquest',
            fields=[
            ],
            options={
                'verbose_name': 'installazione kpnquest',
                'proxy': True,
                'verbose_name_plural': 'installazioni kpnquest',
            },
            bases=('contratti.contrattokpnquest',),
        ),
        migrations.CreateModel(
            name='InstallazioneMastercom',
            fields=[
            ],
            options={
                'verbose_name': 'installazione mastercom',
                'proxy': True,
                'verbose_name_plural': 'installazioni mastercom',
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='InstallazioneMastervoice',
            fields=[
            ],
            options={
                'verbose_name': 'installazione mastervoice',
                'proxy': True,
                'verbose_name_plural': 'installazioni mastervoice',
            },
            bases=('contratti.contrattomastervoice',),
        ),
        migrations.CreateModel(
            name='InstallazioneMastervoiceAgenti',
            fields=[
            ],
            options={
                'verbose_name': 'situazione installazioni',
                'proxy': True,
                'verbose_name_plural': 'situazione installazioni',
            },
            bases=('contratti.installazionemastervoice',),
        ),
        migrations.CreateModel(
            name='InstallazioneMcLink',
            fields=[
            ],
            options={
                'verbose_name': 'installazione mclink',
                'proxy': True,
                'verbose_name_plural': 'installazioni mclink',
            },
            bases=('contratti.contrattomclink',),
        ),
        migrations.CreateModel(
            name='InstallazioneNgi',
            fields=[
            ],
            options={
                'verbose_name': 'installazione NGI',
                'proxy': True,
                'verbose_name_plural': 'installazioni NGI',
            },
            bases=('contratti.contrattongi',),
        ),
        migrations.CreateModel(
            name='InstallazioneTrenove',
            fields=[
            ],
            options={
                'verbose_name': 'installazione Trenove',
                'proxy': True,
                'verbose_name_plural': 'installazioni Trenove',
            },
            bases=('contratti.contrattotrenove',),
        ),
        migrations.CreateModel(
            name='InstallazioneWelcome',
            fields=[
            ],
            options={
                'verbose_name': 'installazione welcome',
                'proxy': True,
                'verbose_name_plural': 'installazioni welcome',
            },
            bases=('contratti.contrattowelcome',),
        ),
        migrations.CreateModel(
            name='OpzioneTelefonia',
            fields=[
            ],
            options={
                'verbose_name': 'opzione telefonia',
                'proxy': True,
                'verbose_name_plural': 'opzioni telefonia',
            },
            bases=('contratti.tipocontratto',),
        ),
        migrations.CreateModel(
            name='PreordineAgenti',
            fields=[
            ],
            options={
                'verbose_name': 'situazione preordine',
                'proxy': True,
                'verbose_name_plural': 'situazione preordini',
            },
            bases=('contratti.preordine',),
        ),
        migrations.CreateModel(
            name='PreordineFastweb',
            fields=[
            ],
            options={
                'verbose_name': 'preordine fastweb',
                'proxy': True,
                'verbose_name_plural': 'preordini fastweb',
            },
            bases=('contratti.preordine',),
        ),
        migrations.CreateModel(
            name='PreordineIpkom',
            fields=[
            ],
            options={
                'verbose_name': 'preordine ipkom',
                'proxy': True,
                'verbose_name_plural': 'preordini ipkom',
            },
            bases=('contratti.preordine',),
        ),
        migrations.CreateModel(
            name='PreordineMastercom',
            fields=[
            ],
            options={
                'verbose_name': 'preordine mastercom',
                'proxy': True,
                'verbose_name_plural': 'preordini mastercom',
            },
            bases=('contratti.preordine',),
        ),
        migrations.CreateModel(
            name='PreordineMastervoice',
            fields=[
            ],
            options={
                'verbose_name': 'preordine mastervoice',
                'proxy': True,
                'verbose_name_plural': 'preordini mastervoice',
            },
            bases=('contratti.preordine',),
        ),
        migrations.CreateModel(
            name='ProfiloTelefonia',
            fields=[
            ],
            options={
                'verbose_name': 'profilo standard',
                'proxy': True,
                'verbose_name_plural': 'profili standard',
            },
            bases=('contratti.tipocontratto',),
        ),
        migrations.CreateModel(
            name='SchemaProvvigioneCapoArea',
            fields=[
            ],
            options={
                'verbose_name': 'schema provvigione capo area',
                'proxy': True,
                'verbose_name_plural': 'schemi provvigioni capi area',
            },
            bases=('contratti.schemaprovvigioneagente',),
        ),
        migrations.AddField(
            model_name='collegamentoschemiprovvigioni',
            name='schema_provvigione_capo_area',
            field=models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='schema_provvigione_capo_area_pk', to='contratti.SchemaProvvigioneCapoArea'),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name='collegamentoschemiprovvigioni',
            unique_together=set([('schema_provvigione_agente', 'schema_provvigione_capo_area')]),
        ),
    ]
