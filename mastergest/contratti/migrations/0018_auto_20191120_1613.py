# Generated by Django 2.2.7 on 2019-11-20 15:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0017_auto_20191106_1108'),
    ]

    operations = [
        migrations.AlterField(
            model_name='compenso',
            name='agente',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='contratti.Agente'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='agente',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='contratti.Agente'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='categoria_canone',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='canoni.Categoria'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='chi_deve_fare',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='motivazione_stato',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='contratti.MotivazioneStatoContratto'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='stato',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='contratti.StatoContratto'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipologia_canone',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='canoni.Tipologia'),
        ),
        migrations.AlterField(
            model_name='dettagliocontratto',
            name='tipo_contratto',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='contratti.TipoContratto'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='contratto_destinazione',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='contratto_destinazione_pk', to='contratti.Contratto', verbose_name='contr. che rim.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='contratto_origine',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, primary_key=True, related_name='contratto_origine_pk', serialize=False, to='contratti.Contratto', verbose_name='contr. da elim.'),
        ),
        migrations.AlterField(
            model_name='materialecontratto',
            name='prodotto',
            field=models.ForeignKey(limit_choices_to={'a_listino': True}, on_delete=django.db.models.deletion.PROTECT, to='listino.Prodotto'),
        ),
        migrations.AlterField(
            model_name='materialepreordine',
            name='prodotto',
            field=models.ForeignKey(limit_choices_to={'a_listino': True, 'attivo': True}, on_delete=django.db.models.deletion.CASCADE, to='listino.Prodotto'),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='tipo_gara',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='contratti.TipoGara'),
        ),
    ]
