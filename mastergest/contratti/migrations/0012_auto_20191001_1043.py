# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-01 08:43
from __future__ import unicode_literals

import datetime
from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0011_auto_20190806_0955'),
    ]

    operations = [
        migrations.AlterField(
            model_name='agente',
            name='sottotipo',
            field=models.CharField(blank=True, choices=[('distributore', 'distributore'), ('agente', 'agente'), ('procacciatore', 'procacciatore'), ('consulente', 'consulente'), ('collaboratore', 'collaboratore'), ('altro', 'altro')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='agente',
            name='stato_documenti',
            field=models.CharField(blank=True, choices=[('manca', 'manca'), ('manca_firma', 'manca firma'), ('ok', 'ok'), ('disdetto', 'disdetto'), ('da_correggere', 'da correggere')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='agente',
            name='tipo',
            field=models.CharField(blank=True, choices=[('struttura', 'struttura'), ('capo_area', 'capo area'), ('base', 'base')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='compenso',
            name='data',
            field=models.DateField(verbose_name='data'),
        ),
        migrations.AlterField(
            model_name='compenso',
            name='importo',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='importo'),
        ),
        migrations.AlterField(
            model_name='compenso',
            name='tipo',
            field=models.CharField(choices=[('provvigione', 'provvigione'), ('storno', 'storno'), ('bonifico', 'bonifico'), ('rata', 'rata'), ('gara', 'gara')], default='provvigione', max_length=200, verbose_name='tipo'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='allegato_contratto_mastervoice',
            field=models.BooleanField(default=False, verbose_name='All. Contr. MV'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='anticipato_canone',
            field=models.BooleanField(default=False, verbose_name='anticipato'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='categoria',
            field=models.CharField(blank=True, choices=[('installazione', 'installazione'), ('canone', 'canone')], default='installazione', max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='con_ratino_canone',
            field=models.BooleanField(default=False, verbose_name='con ratino'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='costo_materiale',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='costo materiale'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_attivazione',
            field=models.DateField(blank=True, null=True, verbose_name='attivazione'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_chiusura_fastweb',
            field=models.DateField(blank=True, null=True, verbose_name='Data Chiusura FW'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_consegna',
            field=models.DateField(default=datetime.date.today, verbose_name='competenza'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_consegna_contratto',
            field=models.DateField(default=datetime.date.today, verbose_name='consegna'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_fatturazione',
            field=models.DateField(blank=True, null=True, verbose_name='fatturazione'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_fine_prova',
            field=models.DateField(blank=True, null=True, verbose_name='fine prova'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_inizio_rate',
            field=models.DateField(blank=True, null=True, verbose_name='data collaudo/inst.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_inserimento_pda',
            field=models.DateField(blank=True, null=True, verbose_name='inserimento PDA'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_sopralluogo',
            field=models.DateField(blank=True, null=True, verbose_name='data sopralluogo'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_stipula',
            field=models.DateField(blank=True, null=True, verbose_name='stipula'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='data_verifica_tecnica',
            field=models.DateField(blank=True, null=True, verbose_name='data verifica tecnica'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='descrizione_aggiuntiva_fattura_canone',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='descr. agg. fattura'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='documentazione',
            field=models.BooleanField(default=True, verbose_name='doc. completa'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='forma_contratto',
            field=models.CharField(blank=True, choices=[('comodato', "Comodato d'uso"), ('prova', 'Comodato con prova'), ('vendita', 'Vendita'), ('rent', 'Rent Strumentale'), ('open', 'Open'), ('opentop', 'Opentop')], max_length=200, null=True, verbose_name='forma contr.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gara_accelarazione',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gara acc.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gara_valore',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gara val.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gettone_quantita',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gara qta'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gettone_totale_agente',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gettone tot.ag.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gettone_totale_mastertraining',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gettone MT'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gettone_valore_contratto',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gettone valore contratto agente'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='gettone_valore_contratto_master',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='gettone valore contratto master'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='importo_rata_canone',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='importo rata'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='importo_rata_mensile',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='imp. rata mensile'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='importo_rata_mensile_agente',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='imp. rata mensile agente'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='numero_contratti_fastweb',
            field=models.PositiveIntegerField(default=0, verbose_name='N. Contr. FW'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='numero_mesi_valore_contratto',
            field=models.PositiveIntegerField(default=0, verbose_name='Num. mesi valore contratto'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='numero_rate_canone',
            field=models.PositiveIntegerField(default=1, verbose_name='num. rate'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='numero_rate_rinnovo_canone',
            field=models.PositiveIntegerField(default=1, verbose_name='rate rinnovo'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='periodicita_canone',
            field=models.CharField(choices=[('30', 'mensile'), ('60', '2 mesi'), ('90', '3 mesi'), ('120', '4 mesi'), ('150', '5 mesi'), ('180', 'semestrale'), ('270', '9 mesi'), ('360', 'annuale'), ('720', 'biannuale'), ('1080', 'triannuale')], default=360, max_length=200),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='premio_agente',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='premio ag.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='premio_rata_mensile_agente',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='premio rata mensile ag.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='provincia',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Prov.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='sopralluogo',
            field=models.CharField(blank=True, choices=[('si', 'si'), ('no', 'no'), ('fatto', 'fatto')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='stato_fastweb',
            field=models.CharField(blank=True, choices=[('aperto', 'aperto'), ('chiuso', 'chiuso')], max_length=200, null=True, verbose_name='stato FW'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='stato_installazione',
            field=models.CharField(blank=True, choices=[('attesa_linea', 'in attesa linea'), ('da_installare', 'da installare'), ('test', 'in test'), ('bloccato', 'bloccato (vedi note)'), ('installato', 'installato'), ('collaudato', 'collaudato')], max_length=200, null=True, verbose_name='stato inst.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tecnologia',
            field=models.CharField(blank=True, choices=[('ADSL_ULL', 'ADSL_ULL'), ('ADSL_WHS', 'ADSL_WHS'), ('CVP_HDSL', 'CVP_HDSL'), ('FIBRA', 'FIBRA'), ('SHDSL', 'SHDSL')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipo_contratto_mastercom',
            field=models.CharField(blank=True, choices=[('sistema', 'Sistema Centrale'), ('rete', 'Rete'), ('consumabili', 'Consumabili'), ('accessori', 'Accessori')], max_length=200, null=True, verbose_name='tipo contratto'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipo_data_attivazione',
            field=models.CharField(blank=True, choices=[('presunta', 'presunta'), ('comunicata', 'comunicata'), ('reale', 'reale')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipo_data_portabilita',
            field=models.CharField(blank=True, choices=[('presunta', 'presunta'), ('comunicata', 'comunicata'), ('reale', 'reale')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipo_installazione',
            field=models.CharField(blank=True, choices=[('mv', 'Mastervoice'), ('mg', 'Mastergate'), ('upgrade_mv', 'upgrade Mastervoice'), ('accessori', 'accessori'), ('ipkom', 'numeri IPKOM'), ('kpnquest', 'KPN')], max_length=200, null=True, verbose_name='tipo inst.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipo_rinnovo_canone',
            field=models.CharField(choices=[('tacito', 'tacito'), ('manuale', 'manuale')], default='manuale', max_length=200),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipologia',
            field=models.CharField(choices=[('fisso', 'fisso'), ('mobile', 'mobile')], max_length=200),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='totale_agente',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='tot. ag.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='totale_mastertraining',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='tot. MT'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='valore_contratto',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=9, null=True, verbose_name='valore contratto'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='verifica_tecnica',
            field=models.CharField(blank=True, choices=[('si', 'si'), ('no', 'no'), ('fatto', 'fatto')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='dettagliocontratto',
            name='quantita',
            field=models.PositiveIntegerField(default=1, verbose_name='valore/q.ta'),
        ),
        migrations.AlterField(
            model_name='dettagliovaloreschemaagente',
            name='a_quantita',
            field=models.PositiveIntegerField(default=1, verbose_name='A'),
        ),
        migrations.AlterField(
            model_name='dettagliovaloreschemaagente',
            name='da_quantita',
            field=models.PositiveIntegerField(default=1, verbose_name='Da'),
        ),
        migrations.AlterField(
            model_name='dettagliovaloreschemastandard',
            name='a_quantita',
            field=models.PositiveIntegerField(default=1, verbose_name='A'),
        ),
        migrations.AlterField(
            model_name='dettagliovaloreschemastandard',
            name='da_quantita',
            field=models.PositiveIntegerField(default=1, verbose_name='Da'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='contratto_destinazione',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='contratto_destinazione_pk', to='contratti.Contratto', verbose_name='contr. che rim.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='contratto_origine',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='contratto_origine_pk', serialize=False, to='contratti.Contratto', verbose_name='contr. da elim.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='elimina_corrente',
            field=models.BooleanField(default=True, verbose_name='del.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='sposta_allegati',
            field=models.BooleanField(default=True, verbose_name='all.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='sposta_canoni',
            field=models.BooleanField(default=True, verbose_name='can.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='sposta_materiali',
            field=models.BooleanField(default=True, verbose_name='mat.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='sposta_offerte',
            field=models.BooleanField(default=True, verbose_name='off.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='sposta_preordini',
            field=models.BooleanField(default=True, verbose_name='pre.'),
        ),
        migrations.AlterField(
            model_name='gestionecontratto',
            name='sposta_progetto',
            field=models.BooleanField(default=True, verbose_name='prog.'),
        ),
        migrations.AlterField(
            model_name='materialecontratto',
            name='prezzo_listino',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='prezzo listino (euro)'),
        ),
        migrations.AlterField(
            model_name='materialecontratto',
            name='prezzo_vendita',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='prezzo vendita (euro)'),
        ),
        migrations.AlterField(
            model_name='materialecontratto',
            name='tipo',
            field=models.CharField(blank=True, choices=[('server', 'server MAstercom'), ('tablet', 'tablet'), ('firewall', 'Firewall'), ('totem', 'Mastervoice'), ('elms', 'elms'), ('clinic_point', 'Clinic Point'), ('access_point', 'Access Point'), ('rete', 'Rete'), ('elettrico', 'Elettrico'), ('accessori', 'accessori')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='materialecontratto',
            name='totale_vendita',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='totale (euro)'),
        ),
        migrations.AlterField(
            model_name='materialepreordine',
            name='prezzo_listino',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='prezzo listino (euro)'),
        ),
        migrations.AlterField(
            model_name='materialepreordine',
            name='prezzo_vendita',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='prezzo vendita (euro)'),
        ),
        migrations.AlterField(
            model_name='materialepreordine',
            name='totale_vendita',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='totale (euro)'),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='data_firma_contratto',
            field=models.DateField(verbose_name='data firma contratto'),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='data_verifica_tecnica',
            field=models.DateField(blank=True, null=True, verbose_name='data verifica tecnica'),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='tipo',
            field=models.CharField(choices=[('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('mastervoice', 'Mastervoice'), ('ipkom', 'IpKom'), ('kpnquest', 'KPNQuest'), ('mastercom', 'MasterCom')], max_length=200),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='tipo_installazione',
            field=models.CharField(blank=True, choices=[('mv', 'Mastervoice'), ('mg', 'Mastergate'), ('upgrade_mv', 'upgrade Mastervoice'), ('accessori', 'accessori'), ('ipkom', 'numeri IPKOM'), ('kpnquest', 'KPN')], max_length=200, null=True, verbose_name='tipo inst.'),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='verifica_tecnica',
            field=models.CharField(blank=True, choices=[('si', 'si'), ('no', 'no'), ('fatto', 'fatto')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='gara_premio',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='Gara AG'),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='percentuale_liquidabile',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Perc. Liq.'),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='totale_liquidato',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=9, verbose_name='Liq.ato'),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='mesi_senza_rate',
            field=models.PositiveIntegerField(default=0, help_text="numero di mesi dalla data di collaudo in cui l'agente non percepisce rate."),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='tipologia',
            field=models.CharField(choices=[('fisso', 'fisso'), ('mobile', 'mobile')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='tipologia',
            field=models.CharField(choices=[('fisso', 'fisso'), ('mobile', 'mobile')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='costo',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='costo al bimestre (euro)'),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='sottotipo',
            field=models.CharField(blank=True, choices=[('voce', 'voce'), ('dati', 'dati'), ('opzione', 'opzione')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='tcg',
            field=models.DecimalField(decimal_places=2, default=Decimal('25.82'), max_digits=9, verbose_name='TCG'),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='tipologia',
            field=models.CharField(choices=[('fisso', 'fisso'), ('mobile', 'mobile')], max_length=200),
        ),
    ]
