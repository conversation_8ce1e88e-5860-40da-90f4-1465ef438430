# Generated by Django 2.2.9 on 2020-03-09 09:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0021_auto_20191218_1011'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContrattoNetAndWork',
            fields=[
            ],
            options={
                'verbose_name': 'contratto net and work',
                'verbose_name_plural': 'contratti net and work',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.contratto',),
        ),
        migrations.CreateModel(
            name='PreordineNetAndWork',
            fields=[
            ],
            options={
                'verbose_name': 'preordine net and work',
                'verbose_name_plural': 'preordini net and work',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.preordine',),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='riepilogoprovvigioni',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigioneagente',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='schemaprovvigionestandard',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.AlterField(
            model_name='tipocontratto',
            name='fornitore',
            field=models.CharField(choices=[('acanto', 'Acantho'), ('digitel', 'Digitel'), ('fastweb', 'FastWeb'), ('fastweb_reseller', 'FastWeb Reseller'), ('3italia', 'H3G'), ('kpnquest', 'KPNQuest'), ('lineaipkom', 'Linea Ipkom'), ('hal', 'Hal'), ('ipkom', 'Ipkom'), ('mastercom', 'Mastercom'), ('mastervoice', 'Mastervoice'), ('mclink', 'MC Link'), ('ngi', 'NGI'), ('netandwork', 'Net And work'), ('onesim', 'OneSim'), ('tim', 'Tim'), ('trenove', 'Trenove'), ('twt', 'Twt'), ('vodafone', 'Vodafone'), ('welcome', 'Welcome'), ('wind', 'Wind')], max_length=200),
        ),
        migrations.CreateModel(
            name='InstallazioneNetAndWork',
            fields=[
            ],
            options={
                'verbose_name': 'installazione net and work',
                'verbose_name_plural': 'installazioni net and work',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('contratti.contrattonetandwork',),
        ),
    ]
