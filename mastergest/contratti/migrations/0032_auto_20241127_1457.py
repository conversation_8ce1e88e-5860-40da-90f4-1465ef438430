# Generated by Django 2.2.28 on 2024-11-27 13:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contratti', '0031_auto_20241127_1453'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contratto',
            name='forma_contratto',
            field=models.CharField(blank=True, choices=[('vendita', 'Vendita'), ('open', '<PERSON><PERSON><PERSON>'), ('', '-------------------------'), ('comodato', "Comodato d'uso (NON USARE)"), ('prova', 'Comodato con prova (NON USARE)'), ('rent', '<PERSON>t Strumentale (NON USARE)'), ('opentop', 'Opentop (NON USARE)')], max_length=200, null=True, verbose_name='forma contr.'),
        ),
        migrations.AlterField(
            model_name='contratto',
            name='tipo_installazione',
            field=models.Char<PERSON>ield(blank=True, choices=[('mv', 'Mastervoice Open'), ('mv_pac', 'Mastervoice PaC'), ('mv_assistente_virtuale', 'Mastervoice Assistente Virtuale'), ('upgrade_mv', 'upgrade Mastervoice'), ('accessori', 'accessori'), ('', '---------------------------'), ('mg', 'Mastergate (NON USARE)'), ('ipkom', 'numeri IPKOM (NON USARE)'), ('kpnquest', 'KPN (NON USARE)')], max_length=200, null=True, verbose_name='tipo inst.'),
        ),
        migrations.AlterField(
            model_name='preordine',
            name='tipo_installazione',
            field=models.CharField(blank=True, choices=[('mv', 'Mastervoice Open'), ('mv_pac', 'Mastervoice PaC'), ('mv_assistente_virtuale', 'Mastervoice Assistente Virtuale'), ('upgrade_mv', 'upgrade Mastervoice'), ('accessori', 'accessori'), ('', '---------------------------'), ('mg', 'Mastergate (NON USARE)'), ('ipkom', 'numeri IPKOM (NON USARE)'), ('kpnquest', 'KPN (NON USARE)')], max_length=200, null=True, verbose_name='tipo inst.'),
        ),
    ]
