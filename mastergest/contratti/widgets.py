from mastergest.utils.widgets import BaseAutocompleteWidget, LargeAutocompleteWidget
from mastergest.contratti.models import Tipo<PERSON><PERSON><PERSON><PERSON>, Contratto
from mastergest.contratti.models import TipoContrattoMastervoice


class TipoContrattoWidget(BaseAutocompleteWidget):
    model = TipoContratto

    def __init__(self, rel=None, admin_site=None, *attrs, **kwargs):
        kwargs['attrs'] = kwargs.get('attrs', dict())
        kwargs['attrs']['style'] = 'width:300px'
        super(TipoContrattoWidget, self).__init__(rel, admin_site, *attrs, **kwargs)


class TipoContrattoMastervoiceWidget(TipoContrattoWidget):
    model = TipoContrattoMastervoice


class TipoContrattoFastwebWidget(TipoContrattoWidget):
    source_name = 'contratti_fastweb'


class TipoContrattoWelcomeWidget(TipoContrattoWidget):
    source_name = 'contratti_welcome'


class TipoContrattoNgiWidget(TipoContrattoWidget):
    source_name = 'contratti_ngi'


class TipoContrattoWindWidget(TipoContrattoWidget):
    source_name = 'contratti_wind'


class TipoContrattoBriantelWidget(TipoContrattoWidget):
    source_name = 'contratti_briantel'


class TipoContrattoVodafoneWidget(TipoContrattoWidget):
    source_name = 'contratti_vodafone'


class TipoContratto3Widget(TipoContrattoWidget):
    source_name = 'contratti_3'


class TipoContrattoTimWidget(TipoContrattoWidget):
    source_name = 'contratti_tim'


class TipoContrattoTrenoveWidget(TipoContrattoWidget):
    source_name = 'contratti_trenove'


class TipoContrattoKpnquestWidget(TipoContrattoWidget):
    source_name = 'contratti_kpnquest'


class TipoContrattoLineaIpkomWidget(TipoContrattoWidget):
    source_name = 'contratti_lineaipkom'


class TipoContrattoTwtWidget(TipoContrattoWidget):
    source_name = 'contratti_twt'


class TipoContrattoNetAndWorkWidget(TipoContrattoWidget):
    source_name = 'contratti_netandwork'


class TipoContrattoHalWidget(TipoContrattoWidget):
    source_name = 'contratti_hal'


class TipoContrattoDigitelWidget(TipoContrattoWidget):
    source_name = 'contratti_digitel'


class TipoContrattoAcantoWidget(TipoContrattoWidget):
    source_name = 'contratti_acanto'


class TipoContrattoMcLinkWidget(TipoContrattoWidget):
    source_name = 'contratti_mclink'


class TipoContrattoFastwebResellerWidget(TipoContrattoWidget):
    source_name = 'contratti_fastwebreseller'


class TipoContrattoOnesimWidget(TipoContrattoWidget):
    source_name = 'contratti_onesim'


class ContrattoWidget(LargeAutocompleteWidget):
    model = Contratto
