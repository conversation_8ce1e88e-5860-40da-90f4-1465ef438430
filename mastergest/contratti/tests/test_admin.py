from django.test import TestCase
from django.urls import reverse
from django.db import connection

from . import factories

from mastergest.anagrafe.tests.factories import UserFactory
from mastergest.contratti.models import ProvvigioneAgente
from mastergest.canoni.tests.factories import TipologiaFactory


class TestContrattoAdmin(TestCase):
    fixtures = [
        'auth_test',
        'anagrafe_test.json',
        'contratti_test'
    ]

    def setUp(self):
        TipologiaFactory()
        self.client.login(username='tester', password='tester')

    def test_base(self):
        response = self.client.get('/contratti/contratto/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattomastervoice/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattofastweb/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattovodafone/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattomastercom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contratto3/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattotim/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattowind/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattoipkom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattolineaipkom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattotwt/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattobriantel/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattohal/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattotrenove/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattodigitel/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattoacanto/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattomclink/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattokpnquest/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattoonesim/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionemastervoice/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazioneipkom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionelineaipkom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionedigitel/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazioneacanto/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionemclink/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionetwt/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionebriantel/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionehal/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionengi/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionefastweb/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionemastervoiceagenti/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordine/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinemastervoice/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinemastercom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinefastweb/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordineipkom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinelineaipkom/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/profilotelefonia/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/opzionetelefonia/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordineagenti/')
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        response = self.client.get('/contratti/contratto/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattomastervoice/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattomastercom/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattofastweb/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattovodafone/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contratto3/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattotim/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattowind/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattoipkom/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattolineaipkom/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattotwt/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattobriantel/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattohal/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattotrenove/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattokpnquest/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/contrattoonesim/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/installazionemastervoice/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/installazioneipkom/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/installazionehal/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/installazionelineaipkom/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/installazionengi/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/installazionemastervoiceagenti/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/installazionefastweb/add/')
        self.assertEqual(response.status_code, 403)
        response = self.client.get('/contratti/preordine/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinemastervoice/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinemastercom/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordinefastweb/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordineipkom/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/profilotelefonia/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/opzionetelefonia/add/')
        self.assertEqual(response.status_code, 200)
        response = self.client.get('/contratti/preordineagenti/add/')
        self.assertEqual(response.status_code, 403)


class TestContrattoBaseAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contratto_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contratto_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto = factories.ContrattoFactory()
        url = reverse('admin:contratti_contratto_change', args=(contratto.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto = factories.ContrattoFactory()
        url = reverse('admin:contratti_contratto_delete', args=(contratto.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoMastervoiceAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattomastervoice_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattomastervoice_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_mastervoice = factories.ContrattoMastervoiceFactory()
        url = reverse('admin:contratti_contrattomastervoice_change', args=(contratto_mastervoice.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_mastervoice = factories.ContrattoMastervoiceFactory()
        url = reverse('admin:contratti_contrattomastervoice_delete', args=(contratto_mastervoice.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoKpnQuestAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattokpnquest_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattokpnquest_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_kpn = factories.ContrattoKpnQuestFactory()
        url = reverse('admin:contratti_contrattokpnquest_change', args=(contratto_kpn.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_kpn = factories.ContrattoKpnQuestFactory()
        url = reverse('admin:contratti_contrattokpnquest_delete', args=(contratto_kpn.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoTwtAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattotwt_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattotwt_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_twt = factories.ContrattoTwtFactory()
        url = reverse('admin:contratti_contrattotwt_change', args=(contratto_twt.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_twt = factories.ContrattoTwtFactory()
        url = reverse('admin:contratti_contrattotwt_delete', args=(contratto_twt.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoBriantelAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattobriantel_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattobriantel_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_twt = factories.ContrattoBriantelFactory()
        url = reverse('admin:contratti_contrattobriantel_change', args=(contratto_twt.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_twt = factories.ContrattoBriantelFactory()
        url = reverse('admin:contratti_contrattobriantel_delete', args=(contratto_twt.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoNetAndWorkAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattonetandwork_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattonetandwork_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_netandwork = factories.ContrattoNetAndWorkFactory()
        url = reverse('admin:contratti_contrattonetandwork_change', args=(contratto_netandwork.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_netandwork = factories.ContrattoNetAndWorkFactory()
        url = reverse('admin:contratti_contrattonetandwork_delete', args=(contratto_netandwork.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoHalAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattohal_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattohal_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_hal = factories.ContrattoHalFactory()
        url = reverse('admin:contratti_contrattohal_change', args=(contratto_hal.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_hal = factories.ContrattoHalFactory()
        url = reverse('admin:contratti_contrattohal_delete', args=(contratto_hal.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestContrattoLineaIpkomAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        TipologiaFactory(id=1)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_contrattolineaipkom_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_contrattolineaipkom_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        contratto_linea_ipkom = factories.ContrattoLineaIpkomFactory()
        url = reverse('admin:contratti_contrattolineaipkom_change', args=(contratto_linea_ipkom.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        contratto_linea_ipkom = factories.ContrattoLineaIpkomFactory()
        url = reverse('admin:contratti_contrattolineaipkom_delete', args=(contratto_linea_ipkom.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TestProvvigioneAgenteAdmin(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        self.compenso = factories.CompensoFactory(consolidato=True)
        self.user = UserFactory(username='test')
        self.agente_master = factories.AgenteFactory(mastertraining=True)
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:contratti_provvigioneagente_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:contratti_provvigioneagente_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_detail(self):
        provvigione = ProvvigioneAgente.objects.all()[0]
        url = reverse('admin:contratti_provvigioneagente_change', args=(provvigione.pk.replace('_', '_5F'),))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        provvigione = ProvvigioneAgente.objects.all()[0]
        url = reverse('admin:contratti_provvigioneagente_delete', args=(provvigione.pk.replace('_', '_5F'),))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)
