import factory
from datetime import date
from decimal import Decimal

from .. import models

from mastergest.anagrafe.tests.factories import SedeFactory


CREATE_VIEW_PROVVIGIONI_AGENTE = """
    CREATE VIEW provvigioni_agente AS
        SELECT
            count(*) AS numero_compensi,
            sum(contratti_compenso.valore) AS totale,
            contratti_compenso.agente_id,
            cast(strftime('%m', contratti_compenso.data) as integer) as mese,
            cast(strftime('%Y', contratti_compenso.data) as integer) as anno,
            sum(CASE WHEN contratti_compenso.tipo='bonifico' THEN contratti_compenso.importo ELSE 0 END) AS totale_bonifici,
            sum(CASE WHEN contratti_compenso.tipo='storno' THEN contratti_compenso.importo ELSE 0 END) AS totale_storni,
            sum(CASE WHEN contratti_compenso.tipo='rata' THEN 1 ELSE 0 END) AS numero_ricorrenti,
            sum(CASE WHEN contratti_compenso.tipo <> 'rata' THEN 1 ELSE 0 END) AS numero_gettoni,
            (
                contratti_compenso.agente_id || '_' ||
                cast(strftime('%Y', contratti_compenso.data) as integer) || '_' ||
                cast(strftime('%m', contratti_compenso.data) as integer)
            ) AS id
        FROM
            contratti_compenso
        WHERE
            contratti_compenso.consolidato=1
        GROUP BY
            contratti_compenso.agente_id,
            strftime('%m', contratti_compenso.data),
            strftime('%Y', contratti_compenso.data)
        ORDER BY
            strftime('%Y', contratti_compenso.data) DESC,
            strftime('%m', contratti_compenso.data) DESC;
"""


class AgenteFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.Agente

    cognome = factory.Sequence(lambda n: 'agente%s' % n)
    nome = 'Mario'
    mastertraining = False


class ContrattoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.Contratto

    azienda = factory.Sequence(lambda n: 'Azienda N.%s' % n)
    agente = factory.SubFactory(AgenteFactory)
    sede = factory.SubFactory(SedeFactory)
    fornitore = 'mastervoice'
    tipologia = 'fisso'


class ContrattoMastervoiceFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoMastervoice

    fornitore = 'mastervoice'


class ContrattoKpnQuestFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoKpnquest

    fornitore = 'kpnquest'


class ContrattoLineaIpkomFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoLineaIpkom

    fornitore = 'lineaipkom'


class ContrattoTwtFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoTwt

    fornitore = 'twt'


class ContrattoBriantelFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoBriantel

    fornitore = 'briantel'


class ContrattoNetAndWorkFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoNetAndWork
    fornitore = 'netandwork'


class ContrattoHalFactory(ContrattoFactory):
    class Meta:
        model = models.ContrattoHal

    fornitore = 'hal'


class CompensoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.Compenso

    agente = factory.SubFactory(AgenteFactory)
    data = date(2016, 9, 1)
    descrizione = factory.Sequence(lambda n: 'Compenso N.%s' % n)
    importo = Decimal('15.00')
    tipo = 'provvigione'
