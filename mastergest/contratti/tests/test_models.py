from datetime import date
from decimal import Decimal
from unittest import skip

from django.db import connection
from django.test import TestCase
from django.contrib.auth import get_user_model

from mastergest.contratti.models import <PERSON><PERSON><PERSON>, DettaglioContratto
from mastergest.contratti.models import StatoContratto, Compenso, GestioneContratto
from mastergest.contratti.models import <PERSON><PERSON>, TipoContratto
from mastergest.contratti.models import CollegamentoSchemiProvvigioni
from mastergest.contratti.models import ProvvigioneAgente
from mastergest.contratti.models import SchemaProvvigioneCapoArea, DettaglioValoreSchemaAgente
from mastergest.offerte.models import Offerta, Preordine
from mastergest.mastervoice.models import Progetto
from mastergest.anagrafe.models import Anagrafica

from . import factories
from mastergest.anagrafe.tests.factories import UserFactory

user_model = get_user_model()

CREATE_VIEW_CALCOLO_PROVVIGIONI = "create view calcolo_provvigioni_mensili as select agente_id, count(*) as numero_contratti, sum(totale_mastertraining) as totale_provvigioni_mastertraining, sum(totale_agente) as totale_provvigioni_agente, sum(numero_storni) as numero_storni, cast(strftime('%m', data_inizio_rate) as integer) as mese, cast(strftime('%Y', data_inizio_rate) as integer) as anno, fornitore, (agente_id || '_' || fornitore || '_' || cast(strftime('%Y', data_inizio_rate) as integer) || '_' || cast(strftime('%m', data_inizio_rate) as integer)) as id from contratti_contratto left join contratti_statocontratto on contratti_contratto.stato_id=contratti_statocontratto.id where stato_id is null or ignora_provvigioni_relative is null or ignora_provvigioni_relative = 0 group by agente_id,cast(strftime('%m', data_inizio_rate) as integer) , cast(strftime('%Y', data_inizio_rate) as integer), fornitore"

@skip("da rivedere")
class TestContrattoModel(TestCase):
    fixtures = ['canoni_initial', 'contratti_test']

    def test_get_provvigioni_senza_data(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2012, month=7, day=19),
            data_inizio_rate=date(year=1970, month=1, day=1)
        )
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_inizio_rate=date(year=2009, month=7, day=19),
            data_stipula=date(year=2012, month=7, day=19)
        )
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)

    def test_get_provvigioni_senza_schema_associato_esistente(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        tipo_contratto = TipoContratto.objects.get(pk=9)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)

    def test_get_provvigioni_senza_schema_associato_per_data(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        tipo_contratto = TipoContratto.objects.get(pk=9)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2010, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)

    def test_get_provvigioni(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=20)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=20))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=20))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=20))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=25)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('140.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=10, day=25))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('42.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('42.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=25))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('98.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('98.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=25))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        tipo_contratto = TipoContratto.objects.get(pk=6)
        contratto_fastweb = Contratto.objects.create(
            agente=agente, fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('200.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=20)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('200.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=20))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=20))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=20))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=25)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('120.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=10, day=25))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('36.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('36.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=25))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('84.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('84.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=25))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)

    def test_get_provvigioni_quantita(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=3)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('6000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('660.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 3)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('495.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('495.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('6000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('6000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio.quantita = 10
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('20000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('2200.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 10)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('550.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('550.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1650.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1650.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('20000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('20000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio.quantita = 1
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 1)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)

    def test_get_provvigioni_multidettaglio(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        tipo_contratto = TipoContratto.objects.get(pk=6)
        dettaglio_1 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=1)
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('200.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 1)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        dettaglio_2 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=3)
        dettaglio_2.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('860.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=8)
        dettaglio_3 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=5)
        dettaglio_3.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('2060.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 9)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('515.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('515.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1545.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1545.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)

    def test_get_provvigioni_elimina_dettaglio(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        tipo_contratto = TipoContratto.objects.get(pk=6)
        dettaglio_1 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=1)
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('200.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 1)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        dettaglio_2 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=3)
        dettaglio_2.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('860.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=8)
        dettaglio_3 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=5)
        dettaglio_3.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('2060.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 9)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('515.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('515.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1545.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1545.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio_1.delete()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('6000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('1860.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 8)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('465.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('465.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1395.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1395.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('6000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('6000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)

    def test_get_provvigioni_modifica_quantita(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        tipo_contratto = TipoContratto.objects.get(pk=6)
        dettaglio_1 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=1)
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('200.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 1)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        dettaglio_2 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=3)
        dettaglio_2.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('860.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=8)
        dettaglio_3 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=5)
        dettaglio_3.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('2060.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 9)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('515.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('515.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1545.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1545.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio_1.quantita = 5
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('11000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('2860.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 13)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('715.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('715.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('2145.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('2145.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('11000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('11000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)

    def test_get_provvigioni_con_storno(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        tipo_contratto = TipoContratto.objects.get(pk=6)
        dettaglio_1 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=1)
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('1000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('200.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 1)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('150.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        dettaglio_2 = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto, quantita=3)
        dettaglio_2.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('860.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('215.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('645.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio_2.storno_agente = Decimal('100.00')
        dettaglio_2.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('760.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('190.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('190.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('570.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('570.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio_2.storno_agente = Decimal('1000.00')
        dettaglio_2.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('-140.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('-35.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('-35.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('-105.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('-105.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio_1.storno_agente = Decimal('160.00')
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('-300.00'))
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('-75.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('-75.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('-225.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('-225.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio_1.gettone_agente = Decimal('500.00')
        dettaglio_1.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('7000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 1)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('7000.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(contratto_fastweb.totale_prodotti, 4)

    def test_get_provvigioni_no_sovrascrivere(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio.gettone_mastertraining = Decimal('10.00')
        dettaglio.gettone_agente = Decimal('20.00')
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('10.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('20.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('5.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('5.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('15.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('15.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('10.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('10.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        dettaglio.gettone_mastertraining = Decimal('0.00')
        dettaglio.gettone_agente = Decimal('0.00')
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.data_inizio_rate = date(year=2010, month=1, day=19)
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 0)
        tipo_contratto = TipoContratto.objects.get(pk=6)
        dettaglio.tipo_contratto = tipo_contratto
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 2)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2010, month=1, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('220.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('220.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2010, month=1, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)

    def test_get_totali_provvigioni(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.premio_agente = Decimal('1000.00')
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('1220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('305.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('305.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('915.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('915.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.gara_valore = Decimal('90.00')
        contratto_fastweb.gara_accelarazione = Decimal('900.00')
        contratto_fastweb.gettone_quantita = Decimal('9.00')
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('2999.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('1220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('305.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('305.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('915.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('915.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2999.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2999.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.premio_agente = Decimal('0.00')
        contratto_fastweb.gara_valore = Decimal('0.00')
        contratto_fastweb.gara_accelarazione = Decimal('0.00')
        contratto_fastweb.gettone_quantita = Decimal('0.00')
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.costo_materiale = Decimal('1200.00')
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('800.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('800.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('800.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)

    def test_get_provvigione_valore_contratto(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.valore_contratto = Decimal('100.00')
        contratto_fastweb.numero_mesi_valore_contratto = 12
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('105.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('105.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('315.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('315.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)

    def test_get_percentuale_liquidazione_agente(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        percentuale = contratto_fastweb.get_percentuale_liquidabile_agente()
        self.assertEqual(percentuale, 75)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=29)
        )
        percentuale = contratto_fastweb.get_percentuale_liquidabile_agente()
        self.assertEqual(percentuale, 70)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=8, day=29)
        )
        percentuale = contratto_fastweb.get_percentuale_liquidabile_agente()
        self.assertEqual(percentuale, 0)
        agente = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=4, day=19)
        )
        percentuale = contratto_fastweb.get_percentuale_liquidabile_agente()
        self.assertEqual(percentuale, 100)

    def test_genera_rate(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.numero_rate = 12
        contratto_fastweb.save()
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.data_inizio_rate = date(year=2012, month=7, day=19)
        contratto_fastweb.importo_rata_mensile = Decimal('100.00')
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.importo_rata_mensile_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 15)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2013, month=6, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2013, month=5, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2013, month=4, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2013, month=3, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2013, month=2, day=19))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[5].data, date(year=2013, month=1, day=19))
        self.assertEqual(elenco_compensi[5].agente, agente_master)
        self.assertEqual(elenco_compensi[5].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[6].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[7].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi[7].agente, agente_master)
        self.assertEqual(elenco_compensi[7].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[7].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[7].tipo, 'rata')
        self.assertEqual(elenco_compensi[7].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[8].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi[8].agente, agente_master)
        self.assertEqual(elenco_compensi[8].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].tipo, 'rata')
        self.assertEqual(elenco_compensi[8].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[9].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[9].agente, agente)
        self.assertEqual(elenco_compensi[9].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[9].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[9].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[9].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[10].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[10].agente, agente_master)
        self.assertEqual(elenco_compensi[10].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].tipo, 'rata')
        self.assertEqual(elenco_compensi[10].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[11].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi[11].agente, agente_master)
        self.assertEqual(elenco_compensi[11].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[11].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[11].tipo, 'rata')
        self.assertEqual(elenco_compensi[11].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[12].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[12].agente, agente)
        self.assertEqual(elenco_compensi[12].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[12].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[12].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[12].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[13].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[13].agente, agente_master)
        self.assertEqual(elenco_compensi[13].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[13].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[13].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[14].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[14].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[14].agente, agente_master)
        self.assertEqual(elenco_compensi[14].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].tipo, 'rata')
        self.assertEqual(elenco_compensi[14].contratto, contratto_fastweb)
        contratto_fastweb.fornitore = 'mastervoice'
        contratto_fastweb.save()
        self.assertEqual(Compenso.objects.count(), 14)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2013, month=6, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2013, month=5, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2013, month=4, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2013, month=3, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2013, month=2, day=19))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[5].data, date(year=2013, month=1, day=19))
        self.assertEqual(elenco_compensi[5].agente, agente_master)
        self.assertEqual(elenco_compensi[5].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[6].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[7].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi[7].agente, agente_master)
        self.assertEqual(elenco_compensi[7].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[7].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[7].tipo, 'rata')
        self.assertEqual(elenco_compensi[7].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[8].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi[8].agente, agente_master)
        self.assertEqual(elenco_compensi[8].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].tipo, 'rata')
        self.assertEqual(elenco_compensi[8].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[9].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[9].agente, agente_master)
        self.assertEqual(elenco_compensi[9].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[9].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[9].tipo, 'rata')
        self.assertEqual(elenco_compensi[9].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[10].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi[10].agente, agente_master)
        self.assertEqual(elenco_compensi[10].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].tipo, 'rata')
        self.assertEqual(elenco_compensi[10].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[11].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[11].agente, agente)
        self.assertEqual(elenco_compensi[11].importo, Decimal('220.00'))
        self.assertEqual(elenco_compensi[11].valore, Decimal('220.00'))
        self.assertEqual(elenco_compensi[11].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[11].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[12].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[12].agente, agente_master)
        self.assertEqual(elenco_compensi[12].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[12].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[12].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[12].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[13].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[13].agente, agente_master)
        self.assertEqual(elenco_compensi[13].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[13].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[13].tipo, 'rata')
        self.assertEqual(elenco_compensi[13].contratto, contratto_fastweb)
        contratto_fastweb.importo_rata_mensile = Decimal('944.00')
        contratto_fastweb.data_inizio_rate = date(year=2012, month=7, day=25)
        contratto_fastweb.numero_rate = 4
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 4)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=10, day=25))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('944.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('944.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=9, day=25))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('944.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('944.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=8, day=25))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('944.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('944.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=7, day=25))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('944.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('944.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        contratto_fastweb.importo_rata_mensile = Decimal('947.00')
        contratto_fastweb.data_inizio_rate = date(year=2012, month=7, day=25)
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.importo_rata_mensile_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 4)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=10, day=25))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('947.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('947.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=9, day=25))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('947.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('947.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=8, day=25))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('947.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('947.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=7, day=25))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('947.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('947.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        contratto_fastweb.numero_rate = 0
        contratto_fastweb.data_inizio_rate = date(year=2012, month=7, day=25)
        contratto_fastweb.save()
        self.assertEqual(Compenso.objects.count(), 0)

    def test_genera_rate_subito(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2006, month=1, day=19),
            numero_rate=5,
            data_inizio_rate=date(year=2012, month=8, day=19),
            importo_rata_mensile=Decimal('100.00')
        )
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)

    def test_elimina_contratto(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2006, month=1, day=19),
            numero_rate=5,
            data_inizio_rate=date(year=2012, month=8, day=19),
            importo_rata_mensile=Decimal('100.00')
        )
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)
        contratto_fastweb_2 = Contratto.objects.create(
            agente=agente,
            fornitore='wind',
            data_stipula=date(year=2006, month=1, day=19),
            numero_rate=2,
            data_inizio_rate=date(year=2013, month=8, day=19),
            importo_rata_mensile=Decimal('350.00')
        )
        self.assertEqual(contratto_fastweb_2.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb_2.gettone_totale_agente, Decimal('0.00'))
        self.assertEqual(Compenso.objects.count(), 7)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2013, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('350.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('350.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi[1].data, date(year=2013, month=8, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('350.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('350.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[5].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[5].agente, agente_master)
        self.assertEqual(elenco_compensi[5].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[6].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_fastweb)
        contratto_fastweb.delete()
        self.assertEqual(Compenso.objects.count(), 2)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2013, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('350.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('350.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi[1].data, date(year=2013, month=8, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('350.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('350.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb_2)
        contratto_fastweb_2.delete()
        self.assertEqual(Compenso.objects.count(), 0)
    
    @skip("da rivedere")
    def test_provvigioni_contratto_ko(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=3)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        # contratto wind1
        contratto_1 = Contratto.objects.create(
            agente=agente,
            fornitore='wind',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=9, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_1, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_1.gettone_totale_mastertraining, Decimal('75.00'))
        self.assertEqual(contratto_1.gettone_totale_agente, Decimal('1785.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('714.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('714.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_1)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1071.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1071.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_1)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('75.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('75.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_1)
        contratto_1.stato = StatoContratto.objects.get(pk=1)
        contratto_1.save()
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('714.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('714.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_1)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1071.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1071.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_1)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('75.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('75.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_1)
        contratto_1.stato = StatoContratto.objects.get(pk=2)
        contratto_1.save()
        self.assertEqual(Compenso.objects.count(), 0)

    def test_genera_rate_mesi_senza(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_mastervoice = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2014, month=7, day=30),
            numero_rate=12,
            data_inizio_rate=date(year=2014, month=8, day=1)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_mastervoice, tipo_contratto=tipo_contratto)
        dettaglio.save()
        contratto_mastervoice.importo_rata_mensile = Decimal('100.00')
        contratto_mastervoice.fornitore = 'mastervoice'
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 19)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=10, day=1))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=11, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2014, month=12, day=1))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[5].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[5].agente, agente)
        self.assertEqual(elenco_compensi[5].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[6].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[7].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[7].agente, agente)
        self.assertEqual(elenco_compensi[7].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[7].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[7].tipo, 'rata')
        self.assertEqual(elenco_compensi[7].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[8].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[8].agente, agente_master)
        self.assertEqual(elenco_compensi[8].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].tipo, 'rata')
        self.assertEqual(elenco_compensi[8].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[9].data, date(year=2015, month=3, day=1))
        self.assertEqual(elenco_compensi[9].agente, agente)
        self.assertEqual(elenco_compensi[9].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[9].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[9].tipo, 'rata')
        self.assertEqual(elenco_compensi[9].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[10].data, date(year=2015, month=3, day=1))
        self.assertEqual(elenco_compensi[10].agente, agente_master)
        self.assertEqual(elenco_compensi[10].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].tipo, 'rata')
        self.assertEqual(elenco_compensi[10].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[11].data, date(year=2015, month=4, day=1))
        self.assertEqual(elenco_compensi[11].agente, agente)
        self.assertEqual(elenco_compensi[11].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[11].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[11].tipo, 'rata')
        self.assertEqual(elenco_compensi[11].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[12].data, date(year=2015, month=4, day=1))
        self.assertEqual(elenco_compensi[12].agente, agente_master)
        self.assertEqual(elenco_compensi[12].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[12].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[12].tipo, 'rata')
        self.assertEqual(elenco_compensi[12].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[13].data, date(year=2015, month=5, day=1))
        self.assertEqual(elenco_compensi[13].agente, agente)
        self.assertEqual(elenco_compensi[13].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[13].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[13].tipo, 'rata')
        self.assertEqual(elenco_compensi[13].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[14].data, date(year=2015, month=5, day=1))
        self.assertEqual(elenco_compensi[14].agente, agente_master)
        self.assertEqual(elenco_compensi[14].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].tipo, 'rata')
        self.assertEqual(elenco_compensi[14].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[15].data, date(year=2015, month=6, day=1))
        self.assertEqual(elenco_compensi[15].agente, agente)
        self.assertEqual(elenco_compensi[15].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[15].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[15].tipo, 'rata')
        self.assertEqual(elenco_compensi[15].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[16].data, date(year=2015, month=6, day=1))
        self.assertEqual(elenco_compensi[16].agente, agente_master)
        self.assertEqual(elenco_compensi[16].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[16].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[16].tipo, 'rata')
        self.assertEqual(elenco_compensi[16].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[17].data, date(year=2015, month=7, day=1))
        self.assertEqual(elenco_compensi[17].agente, agente)
        self.assertEqual(elenco_compensi[17].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[17].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[17].tipo, 'rata')
        self.assertEqual(elenco_compensi[17].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[18].data, date(year=2015, month=7, day=1))
        self.assertEqual(elenco_compensi[18].agente, agente_master)
        self.assertEqual(elenco_compensi[18].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[18].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[18].tipo, 'rata')
        self.assertEqual(elenco_compensi[18].contratto, contratto_mastervoice)

    def test_genera_rate_mesi_senza_maggiore_num_rate(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_mastervoice = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2014, month=7, day=30),
            numero_rate=3,
            data_inizio_rate=date(year=2014, month=8, day=1)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_mastervoice, tipo_contratto=tipo_contratto)
        dettaglio.save()
        contratto_mastervoice.importo_rata_mensile = Decimal('100.00')
        contratto_mastervoice.fornitore = 'mastervoice'
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=10, day=1))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        contratto_mastervoice.numero_rate = 6
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 7)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=10, day=1))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=11, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2014, month=12, day=1))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[5].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[5].agente, agente)
        self.assertEqual(elenco_compensi[5].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[6].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_mastervoice)
        contratto_mastervoice.numero_rate = 5
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=10, day=1))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=11, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2014, month=12, day=1))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)

    def test_genera_provvigioni_con_solo_capo_area(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_mastervoice = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2014, month=7, day=30),
            numero_rate=12,
            data_inizio_rate=date(year=2014, month=8, day=1)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_mastervoice, tipo_contratto=tipo_contratto)
        dettaglio.save()
        contratto_mastervoice.importo_rata_mensile = Decimal('100.00')
        contratto_mastervoice.fornitore = 'mastervoice'
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 19)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=10, day=1))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=11, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2014, month=12, day=1))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[5].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[5].agente, agente)
        self.assertEqual(elenco_compensi[5].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[6].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[7].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[7].agente, agente)
        self.assertEqual(elenco_compensi[7].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[7].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[7].tipo, 'rata')
        self.assertEqual(elenco_compensi[7].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[8].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[8].agente, agente_master)
        self.assertEqual(elenco_compensi[8].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].tipo, 'rata')
        self.assertEqual(elenco_compensi[8].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[9].data, date(year=2015, month=3, day=1))
        self.assertEqual(elenco_compensi[9].agente, agente)
        self.assertEqual(elenco_compensi[9].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[9].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[9].tipo, 'rata')
        self.assertEqual(elenco_compensi[9].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[10].data, date(year=2015, month=3, day=1))
        self.assertEqual(elenco_compensi[10].agente, agente_master)
        self.assertEqual(elenco_compensi[10].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].tipo, 'rata')
        self.assertEqual(elenco_compensi[10].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[11].data, date(year=2015, month=4, day=1))
        self.assertEqual(elenco_compensi[11].agente, agente)
        self.assertEqual(elenco_compensi[11].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[11].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[11].tipo, 'rata')
        self.assertEqual(elenco_compensi[11].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[12].data, date(year=2015, month=4, day=1))
        self.assertEqual(elenco_compensi[12].agente, agente_master)
        self.assertEqual(elenco_compensi[12].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[12].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[12].tipo, 'rata')
        self.assertEqual(elenco_compensi[12].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[13].data, date(year=2015, month=5, day=1))
        self.assertEqual(elenco_compensi[13].agente, agente)
        self.assertEqual(elenco_compensi[13].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[13].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[13].tipo, 'rata')
        self.assertEqual(elenco_compensi[13].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[14].data, date(year=2015, month=5, day=1))
        self.assertEqual(elenco_compensi[14].agente, agente_master)
        self.assertEqual(elenco_compensi[14].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].tipo, 'rata')
        self.assertEqual(elenco_compensi[14].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[15].data, date(year=2015, month=6, day=1))
        self.assertEqual(elenco_compensi[15].agente, agente)
        self.assertEqual(elenco_compensi[15].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[15].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[15].tipo, 'rata')
        self.assertEqual(elenco_compensi[15].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[16].data, date(year=2015, month=6, day=1))
        self.assertEqual(elenco_compensi[16].agente, agente_master)
        self.assertEqual(elenco_compensi[16].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[16].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[16].tipo, 'rata')
        self.assertEqual(elenco_compensi[16].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[17].data, date(year=2015, month=7, day=1))
        self.assertEqual(elenco_compensi[17].agente, agente)
        self.assertEqual(elenco_compensi[17].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[17].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[17].tipo, 'rata')
        self.assertEqual(elenco_compensi[17].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[18].data, date(year=2015, month=7, day=1))
        self.assertEqual(elenco_compensi[18].agente, agente_master)
        self.assertEqual(elenco_compensi[18].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[18].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[18].tipo, 'rata')
        self.assertEqual(elenco_compensi[18].contratto, contratto_mastervoice)
        capo_area = Agente.objects.get(pk=4)
        schema_capo_area = SchemaProvvigioneCapoArea.objects.create(
            agente=capo_area,
            schema_provvigione_id=3,
            data_inizio_validita=date(year=2014, month=1, day=1),
            data_fine_validita=date(year=2014, month=12, day=31),
        )
        schema_provvigione = contratto_mastervoice.get_schema_provvigione_agente()
        CollegamentoSchemiProvvigioni.objects.create(schema_provvigione_agente=schema_provvigione, schema_provvigione_capo_area=schema_capo_area)
        contratto_mastervoice.numero_rate = 0
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 2)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, capo_area)
        self.assertEqual(elenco_compensi[0].importo, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[1].agente, capo_area)
        self.assertEqual(elenco_compensi[1].importo, Decimal('280.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('280.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)

    def test_genera_rate_con_capo_area_rate(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_mastervoice = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2014, month=7, day=30),
            numero_rate=12,
            data_inizio_rate=date(year=2014, month=8, day=1)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_mastervoice, tipo_contratto=tipo_contratto)
        dettaglio.save()
        contratto_mastervoice.importo_rata_mensile = Decimal('100.00')
        contratto_mastervoice.fornitore = 'mastervoice'
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 19)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=10, day=1))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=11, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2014, month=12, day=1))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[5].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[5].agente, agente)
        self.assertEqual(elenco_compensi[5].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'rata')
        self.assertEqual(elenco_compensi[5].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[6].data, date(year=2015, month=1, day=1))
        self.assertEqual(elenco_compensi[6].agente, agente_master)
        self.assertEqual(elenco_compensi[6].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[6].tipo, 'rata')
        self.assertEqual(elenco_compensi[6].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[7].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[7].agente, agente)
        self.assertEqual(elenco_compensi[7].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[7].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[7].tipo, 'rata')
        self.assertEqual(elenco_compensi[7].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[8].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[8].agente, agente_master)
        self.assertEqual(elenco_compensi[8].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[8].tipo, 'rata')
        self.assertEqual(elenco_compensi[8].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[9].data, date(year=2015, month=3, day=1))
        self.assertEqual(elenco_compensi[9].agente, agente)
        self.assertEqual(elenco_compensi[9].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[9].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[9].tipo, 'rata')
        self.assertEqual(elenco_compensi[9].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[10].data, date(year=2015, month=3, day=1))
        self.assertEqual(elenco_compensi[10].agente, agente_master)
        self.assertEqual(elenco_compensi[10].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[10].tipo, 'rata')
        self.assertEqual(elenco_compensi[10].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[11].data, date(year=2015, month=4, day=1))
        self.assertEqual(elenco_compensi[11].agente, agente)
        self.assertEqual(elenco_compensi[11].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[11].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[11].tipo, 'rata')
        self.assertEqual(elenco_compensi[11].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[12].data, date(year=2015, month=4, day=1))
        self.assertEqual(elenco_compensi[12].agente, agente_master)
        self.assertEqual(elenco_compensi[12].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[12].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[12].tipo, 'rata')
        self.assertEqual(elenco_compensi[12].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[13].data, date(year=2015, month=5, day=1))
        self.assertEqual(elenco_compensi[13].agente, agente)
        self.assertEqual(elenco_compensi[13].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[13].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[13].tipo, 'rata')
        self.assertEqual(elenco_compensi[13].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[14].data, date(year=2015, month=5, day=1))
        self.assertEqual(elenco_compensi[14].agente, agente_master)
        self.assertEqual(elenco_compensi[14].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[14].tipo, 'rata')
        self.assertEqual(elenco_compensi[14].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[15].data, date(year=2015, month=6, day=1))
        self.assertEqual(elenco_compensi[15].agente, agente)
        self.assertEqual(elenco_compensi[15].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[15].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[15].tipo, 'rata')
        self.assertEqual(elenco_compensi[15].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[16].data, date(year=2015, month=6, day=1))
        self.assertEqual(elenco_compensi[16].agente, agente_master)
        self.assertEqual(elenco_compensi[16].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[16].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[16].tipo, 'rata')
        self.assertEqual(elenco_compensi[16].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[17].data, date(year=2015, month=7, day=1))
        self.assertEqual(elenco_compensi[17].agente, agente)
        self.assertEqual(elenco_compensi[17].importo, Decimal('50.00'))
        self.assertEqual(elenco_compensi[17].valore, Decimal('50.00'))
        self.assertEqual(elenco_compensi[17].tipo, 'rata')
        self.assertEqual(elenco_compensi[17].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[18].data, date(year=2015, month=7, day=1))
        self.assertEqual(elenco_compensi[18].agente, agente_master)
        self.assertEqual(elenco_compensi[18].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[18].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[18].tipo, 'rata')
        self.assertEqual(elenco_compensi[18].contratto, contratto_mastervoice)
        capo_area = Agente.objects.get(pk=4)
        schema_capo_area = SchemaProvvigioneCapoArea.objects.create(
            agente=capo_area,
            schema_provvigione_id=3,
            data_inizio_validita=date(year=2014, month=1, day=1),
            data_fine_validita=date(year=2014, month=12, day=31),
            percentuale_provvigione_rate=10
        )
        schema_provvigione = contratto_mastervoice.get_schema_provvigione_agente()
        CollegamentoSchemiProvvigioni.objects.create(schema_provvigione_agente=schema_provvigione, schema_provvigione_capo_area=schema_capo_area)
        contratto_mastervoice.numero_rate = 2
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 6)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, capo_area)
        self.assertEqual(elenco_compensi[0].importo, Decimal('10.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('10.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'rata')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[2].agente, capo_area)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[3].agente, capo_area)
        self.assertEqual(elenco_compensi[3].importo, Decimal('10.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('10.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[4].agente, agente_master)
        self.assertEqual(elenco_compensi[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'rata')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[5].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[5].agente, capo_area)
        self.assertEqual(elenco_compensi[5].importo, Decimal('280.00'))
        self.assertEqual(elenco_compensi[5].valore, Decimal('280.00'))
        self.assertEqual(elenco_compensi[5].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[5].contratto, contratto_mastervoice)
        schema_capo_area.mesi_senza_rate = 1
        schema_capo_area.save()
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[1].agente, capo_area)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[2].agente, capo_area)
        self.assertEqual(elenco_compensi[2].importo, Decimal('10.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('10.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[4].agente, capo_area)
        self.assertEqual(elenco_compensi[4].importo, Decimal('280.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('280.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)
        contratto_mastervoice.valore_contratto = 50
        contratto_mastervoice.save()
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'rata')
        self.assertEqual(elenco_compensi[0].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[1].data, date(year=2014, month=8, day=1))
        self.assertEqual(elenco_compensi[1].agente, capo_area)
        self.assertEqual(elenco_compensi[1].importo, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[2].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[2].agente, capo_area)
        self.assertEqual(elenco_compensi[2].importo, Decimal('10.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('10.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'rata')
        self.assertEqual(elenco_compensi[2].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[3].data, date(year=2014, month=9, day=1))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'rata')
        self.assertEqual(elenco_compensi[3].contratto, contratto_mastervoice)
        self.assertEqual(elenco_compensi[4].data, date(year=2015, month=2, day=1))
        self.assertEqual(elenco_compensi[4].agente, capo_area)
        self.assertEqual(elenco_compensi[4].importo, Decimal('280.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('280.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[4].contratto, contratto_mastervoice)

    def test_get_provvigione_valore_contratto_capo_area(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(contratto_fastweb.totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        contratto_fastweb.valore_contratto = Decimal('100.00')
        contratto_fastweb.numero_mesi_valore_contratto = 12
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('105.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('105.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('315.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('315.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        capo_area = Agente.objects.get(pk=4)
        schema_capo_area = SchemaProvvigioneCapoArea.objects.create(
            agente=capo_area,
            schema_provvigione_id=3,
            data_inizio_validita=date(year=2014, month=1, day=1),
            data_fine_validita=date(year=2014, month=12, day=31),
            percentuale_provvigione_rate=10
        )
        schema_provvigione = contratto_fastweb.get_schema_provvigione_agente()
        CollegamentoSchemiProvvigioni.objects.create(schema_provvigione_agente=schema_provvigione, schema_provvigione_capo_area=schema_capo_area)
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('315.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('315.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, capo_area)
        self.assertEqual(elenco_compensi[2].importo, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('1120.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente)
        self.assertEqual(elenco_compensi[3].importo, Decimal('105.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('105.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2013, month=1, day=19))
        self.assertEqual(elenco_compensi[4].agente, capo_area)
        self.assertEqual(elenco_compensi[4].importo, Decimal('280.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('280.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)
        DettaglioValoreSchemaAgente.objects.create(
            da_quantita=1,
            a_quantita=500,
            schema_provvigione=schema_capo_area,
            provvigione=Decimal('1.00')
        )
        DettaglioValoreSchemaAgente.objects.create(
            da_quantita=501,
            a_quantita=1199,
            schema_provvigione=schema_capo_area,
            provvigione=Decimal('100.00')
        )
        DettaglioValoreSchemaAgente.objects.create(
            da_quantita=1200,
            a_quantita=1201,
            schema_provvigione=schema_capo_area,
            provvigione=Decimal('10000.00')
        )
        contratto_fastweb.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 5)
        elenco_compensi = Compenso.objects.all().order_by('data')
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('315.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('315.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente_master)
        self.assertEqual(elenco_compensi[1].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, capo_area)
        self.assertEqual(elenco_compensi[2].importo, Decimal('9120.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('9120.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente)
        self.assertEqual(elenco_compensi[3].importo, Decimal('105.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('105.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[4].data, date(year=2013, month=1, day=19))
        self.assertEqual(elenco_compensi[4].agente, capo_area)
        self.assertEqual(elenco_compensi[4].importo, Decimal('2280.00'))
        self.assertEqual(elenco_compensi[4].valore, Decimal('2280.00'))
        self.assertEqual(elenco_compensi[4].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[4].contratto, contratto_fastweb)

@skip("da rivedere")
class TestCompensoModel(TestCase):
    fixtures = ['contratti_test']

    def test_inserisci_compenso(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        compenso = Compenso.objects.create(data=date(year=1970, month=1, day=1), agente=agente)
        self.assertEqual(compenso.valore, Decimal('0.00'))
        self.assertEqual(compenso.tipo, 'provvigione')
        compenso.tipo = 'rata'
        compenso.save()
        self.assertEqual(compenso.valore, Decimal('0.00'))
        compenso.importo = Decimal('100.00')
        compenso.save()
        self.assertEqual(compenso.valore, Decimal('100.00'))
        compenso.tipo = 'storno'
        compenso.save()
        self.assertEqual(compenso.valore, Decimal('-100.00'))
        compenso.tipo = 'gara'
        compenso.save()
        self.assertEqual(compenso.valore, Decimal('100.00'))
        compenso.tipo = 'bonifico'
        compenso.save()
        self.assertEqual(compenso.valore, Decimal('-100.00'))

    def test_inserisci_compenso_rata_consolidato(self):
        self.assertEqual(Compenso.objects.count(), 0)
        compenso = factories.CompensoFactory(tipo='rata')
        self.assertEqual(compenso.consolidato, True)
        compenso.consolidato = False
        compenso.save()
        self.assertEqual(compenso.consolidato, False)

    def test_inserisci_compenso_rata_master(self):
        self.assertEqual(Compenso.objects.count(), 0)
        agente = factories.AgenteFactory(mastertraining=True)
        compenso = factories.CompensoFactory(agente=agente)
        self.assertEqual(compenso.consolidato, True)
        compenso.consolidato = False
        compenso.save()
        self.assertEqual(compenso.consolidato, True)

    def test_inserisci_compenso_e_contratto(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.create(data=date(year=2012, month=5, day=31), agente=agente_master, tipo='bonifico', importo=Decimal('1990.00'))
        self.assertEqual(Compenso.objects.count(), 4)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=5, day=31))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('1990.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('-1990.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'bonifico')
        self.assertEqual(elenco_compensi[3].contratto, None)
        contratto_fastweb.delete()
        self.assertEqual(Compenso.objects.count(), 1)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=5, day=31))
        self.assertEqual(elenco_compensi[0].agente, agente_master)
        self.assertEqual(elenco_compensi[0].importo, Decimal('1990.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('-1990.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'bonifico')
        self.assertEqual(elenco_compensi[0].contratto, None)

    def test_inserisci_contratto_e_compenso_relativo(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb',
            data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente_master)
        self.assertEqual(elenco_compensi[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        Compenso.objects.create(
            data=date(year=2012, month=12, day=31),
            agente=agente, tipo='bonifico',
            importo=Decimal('1990.00'),
            contratto=contratto_fastweb
        )
        self.assertEqual(Compenso.objects.count(), 4)
        elenco_compensi = Compenso.objects.all()
        self.assertEqual(elenco_compensi[0].data, date(year=2012, month=12, day=31))
        self.assertEqual(elenco_compensi[0].agente, agente)
        self.assertEqual(elenco_compensi[0].importo, Decimal('1990.00'))
        self.assertEqual(elenco_compensi[0].valore, Decimal('-1990.00'))
        self.assertEqual(elenco_compensi[0].tipo, 'bonifico')
        self.assertEqual(elenco_compensi[0].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[1].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi[1].agente, agente)
        self.assertEqual(elenco_compensi[1].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi[1].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[1].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[2].agente, agente)
        self.assertEqual(elenco_compensi[2].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi[2].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[2].contratto, contratto_fastweb)
        self.assertEqual(elenco_compensi[3].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi[3].agente, agente_master)
        self.assertEqual(elenco_compensi[3].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[3].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi[3].tipo, 'provvigione')
        self.assertEqual(elenco_compensi[3].contratto, contratto_fastweb)
        contratto_fastweb.delete()
        self.assertEqual(Compenso.objects.count(), 0)


class TestProvvigioneAgenteModel(TestCase):

    def setUp(self):
        connection.cursor().execute(factories.CREATE_VIEW_PROVVIGIONI_AGENTE)
        self.user = UserFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))

    def test_provvigione_base(self):
        self.assertEqual(Compenso.objects.count(), 0)
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso = factories.CompensoFactory()
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso.consolidato = True
        compenso.save()
        self.assertEqual(ProvvigioneAgente.objects.count(), 1)
        provvigione = ProvvigioneAgente.objects.all()[0]
        self.assertEqual(provvigione.agente, compenso.agente)
        self.assertEqual(provvigione.mese, compenso.data.month)
        self.assertEqual(provvigione.anno, compenso.data.year)
        self.assertEqual(provvigione.totale, compenso.valore)
        self.assertEqual(provvigione.totale_bonifici, 0)
        self.assertEqual(provvigione.totale_storni, 0)
        self.assertEqual(provvigione.numero_compensi, 1)
        self.assertEqual(provvigione.numero_ricorrenti, 0)
        self.assertEqual(provvigione.numero_gettoni, 1)
        compenso.valore = Decimal('11.33')
        compenso.save()
        self.assertEqual(ProvvigioneAgente.objects.count(), 1)
        provvigione = ProvvigioneAgente.objects.all()[0]
        self.assertEqual(provvigione.agente, compenso.agente)
        self.assertEqual(provvigione.mese, compenso.data.month)
        self.assertEqual(provvigione.anno, compenso.data.year)
        self.assertEqual(provvigione.totale, compenso.valore)
        self.assertEqual(provvigione.totale_bonifici, 0)
        self.assertEqual(provvigione.totale_storni, 0)
        self.assertEqual(provvigione.numero_compensi, 1)
        self.assertEqual(provvigione.numero_ricorrenti, 0)
        self.assertEqual(provvigione.numero_gettoni, 1)
        compenso2 = factories.CompensoFactory(agente=compenso.agente, consolidato=True)
        self.assertEqual(ProvvigioneAgente.objects.count(), 1)
        provvigione = ProvvigioneAgente.objects.all()[0]
        self.assertEqual(provvigione.agente, compenso.agente)
        self.assertEqual(provvigione.mese, compenso.data.month)
        self.assertEqual(provvigione.anno, compenso.data.year)
        self.assertEqual(provvigione.totale, (compenso2.valore + compenso.valore))
        self.assertEqual(provvigione.totale_bonifici, 0)
        self.assertEqual(provvigione.totale_storni, 0)
        self.assertEqual(provvigione.numero_compensi, 2)
        self.assertEqual(provvigione.numero_ricorrenti, 0)
        self.assertEqual(provvigione.numero_gettoni, 2)
        compenso3 = factories.CompensoFactory(
            data=date(year=2016, month=8, day=12), valore=Decimal('32.50'),
            consolidato=True
        )
        self.assertEqual(ProvvigioneAgente.objects.count(), 2)
        elenco_provvigioni = ProvvigioneAgente.objects.all()
        self.assertEqual(elenco_provvigioni[0].agente, compenso.agente)
        self.assertEqual(elenco_provvigioni[0].mese, compenso.data.month)
        self.assertEqual(elenco_provvigioni[0].anno, compenso.data.year)
        self.assertEqual(elenco_provvigioni[0].totale, (compenso2.valore + compenso.valore))
        self.assertEqual(elenco_provvigioni[0].totale_bonifici, 0)
        self.assertEqual(elenco_provvigioni[0].totale_storni, 0)
        self.assertEqual(elenco_provvigioni[0].numero_compensi, 2)
        self.assertEqual(elenco_provvigioni[0].numero_ricorrenti, 0)
        self.assertEqual(elenco_provvigioni[0].numero_gettoni, 2)
        self.assertEqual(elenco_provvigioni[1].agente, compenso3.agente)
        self.assertEqual(elenco_provvigioni[1].mese, 8)
        self.assertEqual(elenco_provvigioni[1].anno, compenso3.data.year)
        self.assertEqual(elenco_provvigioni[1].totale, compenso3.valore)
        self.assertEqual(elenco_provvigioni[1].totale_bonifici, 0)
        self.assertEqual(elenco_provvigioni[1].totale_storni, 0)
        self.assertEqual(elenco_provvigioni[1].numero_compensi, 1)
        self.assertEqual(elenco_provvigioni[1].numero_ricorrenti, 0)
        self.assertEqual(elenco_provvigioni[1].numero_gettoni, 1)

    def test_provvigione_storno(self):
        self.assertEqual(Compenso.objects.count(), 0)
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso = factories.CompensoFactory(tipo='storno')
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso.consolidato = True
        compenso.save()
        self.assertEqual(ProvvigioneAgente.objects.count(), 1)
        provvigione = ProvvigioneAgente.objects.all()[0]
        self.assertEqual(provvigione.agente, compenso.agente)
        self.assertEqual(provvigione.mese, compenso.data.month)
        self.assertEqual(provvigione.anno, compenso.data.year)
        self.assertEqual(provvigione.totale, compenso.valore)
        self.assertEqual(provvigione.totale_bonifici, 0)
        self.assertEqual(provvigione.totale_storni, compenso.importo)
        self.assertEqual(provvigione.numero_compensi, 1)
        self.assertEqual(provvigione.numero_ricorrenti, 0)
        self.assertEqual(provvigione.numero_gettoni, 1)

    def test_provvigione_bonifico(self):
        self.assertEqual(Compenso.objects.count(), 0)
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso = factories.CompensoFactory(tipo='bonifico')
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso.consolidato = True
        compenso.save()
        self.assertEqual(ProvvigioneAgente.objects.count(), 1)
        provvigione = ProvvigioneAgente.objects.all()[0]
        self.assertEqual(provvigione.agente, compenso.agente)
        self.assertEqual(provvigione.mese, compenso.data.month)
        self.assertEqual(provvigione.anno, compenso.data.year)
        self.assertEqual(provvigione.totale, compenso.valore)
        self.assertEqual(provvigione.totale_bonifici, compenso.importo)
        self.assertEqual(provvigione.totale_storni, 0)
        self.assertEqual(provvigione.numero_compensi, 1)
        self.assertEqual(provvigione.numero_ricorrenti, 0)
        self.assertEqual(provvigione.numero_gettoni, 1)

    def test_provvigione_rata(self):
        self.assertEqual(Compenso.objects.count(), 0)
        self.assertEqual(ProvvigioneAgente.objects.count(), 0)
        compenso = factories.CompensoFactory(tipo='rata')
        self.assertEqual(ProvvigioneAgente.objects.count(), 1)
        provvigione = ProvvigioneAgente.objects.all()[0]
        self.assertEqual(provvigione.agente, compenso.agente)
        self.assertEqual(provvigione.mese, compenso.data.month)
        self.assertEqual(provvigione.anno, compenso.data.year)
        self.assertEqual(provvigione.totale, compenso.valore)
        self.assertEqual(provvigione.totale_bonifici, 0)
        self.assertEqual(provvigione.totale_storni, 0)
        self.assertEqual(provvigione.numero_compensi, 1)
        self.assertEqual(provvigione.numero_ricorrenti, 1)
        self.assertEqual(provvigione.numero_gettoni, 0)


@skip("da rivedere")
class TestGestioneContrattoModel(TestCase):
    fixtures = ['canoni_initial', 'contratti_test', 'auth_test', 'anagrafe_test']

    def test_trasferimento_offerta(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb_1 = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb_1, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb_1.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb_1.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi_1 = Compenso.objects.filter(contratto=contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi_1[0].agente, agente)
        self.assertEqual(elenco_compensi_1[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi_1[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi_1[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[0].contratto, contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_1[1].agente, agente)
        self.assertEqual(elenco_compensi_1[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi_1[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi_1[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[1].contratto, contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_1[2].agente, agente_master)
        self.assertEqual(elenco_compensi_1[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi_1[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi_1[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[2].contratto, contratto_fastweb_1)
        contratto_fastweb_2 = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice',
            data_stipula=date(year=2006, month=1, day=21),
            numero_rate=5,
            data_inizio_rate=date(year=2012, month=8, day=19),
            importo_rata_mensile=Decimal('100.00')
        )
        self.assertEqual(contratto_fastweb_2.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb_2.gettone_totale_agente, Decimal('0.00'))
        elenco_compensi_2 = Compenso.objects.filter(contratto=contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2.count(), 5)
        self.assertEqual(elenco_compensi_2[0].data, date(year=2012, month=12, day=19))
        self.assertEqual(elenco_compensi_2[0].agente, agente_master)
        self.assertEqual(elenco_compensi_2[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[0].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[0].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[1].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi_2[1].agente, agente_master)
        self.assertEqual(elenco_compensi_2[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[1].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[1].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[2].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi_2[2].agente, agente_master)
        self.assertEqual(elenco_compensi_2[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[2].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[2].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[3].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi_2[3].agente, agente_master)
        self.assertEqual(elenco_compensi_2[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[3].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[3].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[4].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi_2[4].agente, agente_master)
        self.assertEqual(elenco_compensi_2[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[4].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[4].contratto, contratto_fastweb_2)
        primo_compenso = elenco_compensi_2[0]
        primo_compenso.importo = Decimal('999.00')
        primo_compenso.save()
        self.assertEqual(primo_compenso.importo, Decimal('999.00'))
        astel = user_model.objects.get(username='astel')
        elenco_offerte = Offerta.objects.all()
        self.assertEqual(elenco_offerte.count(), 0)
        nuova_offerta = Offerta(
            ragione_sociale='prova', tipo='mastervoice', proprietario=astel
        )
        nuova_offerta.save()
        self.assertEqual(elenco_offerte.count(), 1)
        self.assertEqual(nuova_offerta.get_totale_offerta(), 0)
        contratto_fastweb_1.offerta = nuova_offerta
        contratto_fastweb_1.save()
        GestioneContratto.objects.all().delete()
        gestione_1 = GestioneContratto.objects.create(contratto_origine=contratto_fastweb_1)
        GestioneContratto.objects.create(contratto_origine=contratto_fastweb_2)
        self.assertEqual(GestioneContratto.objects.count(), 2)
        gestione_1.contratto_destinazione = contratto_fastweb_2
        gestione_1.save()
        self.assertEqual(GestioneContratto.objects.count(), 1)
        self.assertEqual(Contratto.objects.count(), 1)
        self.assertEqual(Contratto.objects.all()[0], contratto_fastweb_2)
        contratto_fastweb_2 = Contratto.objects.all()[0]
        self.assertEqual(contratto_fastweb_2.offerta, nuova_offerta)
        elenco_compensi_2 = Compenso.objects.filter(contratto=contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2.count(), 5)
        comp = Compenso.objects.get(pk=primo_compenso.pk)
        self.assertEqual(comp.importo, Decimal('999.00'))
        self.assertEqual(comp.contratto, contratto_fastweb_2)

    def test_trasferimento_preordine(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb_1 = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb_1, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb_1.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb_1.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi_1 = Compenso.objects.filter(contratto=contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi_1[0].agente, agente)
        self.assertEqual(elenco_compensi_1[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi_1[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi_1[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[0].contratto, contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_1[1].agente, agente)
        self.assertEqual(elenco_compensi_1[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi_1[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi_1[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[1].contratto, contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_1[2].agente, agente_master)
        self.assertEqual(elenco_compensi_1[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi_1[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi_1[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[2].contratto, contratto_fastweb_1)
        contratto_fastweb_2 = Contratto.objects.create(
            agente=agente, fornitore='mastervoice',
            data_stipula=date(year=2006, month=1, day=21),
            data_inizio_rate=date(year=2012, month=7, day=19), numero_rate=5,
            importo_rata_mensile=Decimal('100.00')
        )
        self.assertEqual(contratto_fastweb_2.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb_2.gettone_totale_agente, Decimal('0.00'))
        elenco_compensi_2 = Compenso.objects.filter(contratto=contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2.count(), 5)
        self.assertEqual(elenco_compensi_2[0].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi_2[0].agente, agente_master)
        self.assertEqual(elenco_compensi_2[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[0].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[0].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[1].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi_2[1].agente, agente_master)
        self.assertEqual(elenco_compensi_2[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[1].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[1].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[2].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi_2[2].agente, agente_master)
        self.assertEqual(elenco_compensi_2[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[2].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[2].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[3].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi_2[3].agente, agente_master)
        self.assertEqual(elenco_compensi_2[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[3].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[3].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[4].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_2[4].agente, agente_master)
        self.assertEqual(elenco_compensi_2[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[4].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[4].contratto, contratto_fastweb_2)
        primo_compenso = elenco_compensi_2[0]
        primo_compenso.importo = Decimal('999.00')
        primo_compenso.save()
        self.assertEqual(primo_compenso.importo, Decimal('999.00'))
        elenco_preordini = Preordine.objects.all()
        self.assertEqual(elenco_preordini.count(), 0)
        nuovo_preordine = Preordine(
            azienda='prova', tipo='mastervoice', agente=agente,
            data_firma_contratto=date(year=2012, month=9, day=19)
        )
        nuovo_preordine.save()
        self.assertEqual(elenco_preordini.count(), 1)
        contratto_fastweb_1.preordine = nuovo_preordine
        contratto_fastweb_1.save()
        GestioneContratto.objects.all().delete()
        gestione_1 = GestioneContratto.objects.create(contratto_origine=contratto_fastweb_1)
        GestioneContratto.objects.create(contratto_origine=contratto_fastweb_2)
        self.assertEqual(GestioneContratto.objects.count(), 2)
        gestione_1.contratto_destinazione = contratto_fastweb_2
        gestione_1.save()
        self.assertEqual(GestioneContratto.objects.count(), 1)
        self.assertEqual(Contratto.objects.count(), 1)
        self.assertEqual(Contratto.objects.all()[0], contratto_fastweb_2)
        contratto_fastweb_2 = Contratto.objects.all()[0]
        self.assertEqual(contratto_fastweb_2.preordine, nuovo_preordine)
        elenco_compensi_2 = Compenso.objects.filter(contratto=contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2.count(), 5)
        comp = Compenso.objects.get(pk=primo_compenso.pk)
        self.assertEqual(comp.importo, Decimal('999.00'))
        self.assertEqual(comp.contratto, contratto_fastweb_2)

    def test_trasferimento_progetto(self):
        Contratto.objects.all().delete()
        self.assertEqual(Contratto.objects.count(), 0)
        Compenso.objects.all().delete()
        self.assertEqual(Compenso.objects.count(), 0)
        agente = Agente.objects.get(pk=2)
        agente_master = Agente.objects.get(pk=99)
        tipo_contratto = TipoContratto.objects.get(pk=7)
        contratto_fastweb_1 = Contratto.objects.create(
            agente=agente,
            fornitore='fastweb', data_stipula=date(year=2006, month=1, day=19),
            data_inizio_rate=date(year=2012, month=7, day=19)
        )
        dettaglio = DettaglioContratto.objects.create(contratto=contratto_fastweb_1, tipo_contratto=tipo_contratto)
        dettaglio.save()
        self.assertEqual(contratto_fastweb_1.gettone_totale_mastertraining, Decimal('2000.00'))
        self.assertEqual(contratto_fastweb_1.gettone_totale_agente, Decimal('220.00'))
        self.assertEqual(Compenso.objects.count(), 3)
        elenco_compensi_1 = Compenso.objects.filter(contratto=contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[0].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi_1[0].agente, agente)
        self.assertEqual(elenco_compensi_1[0].importo, Decimal('55.00'))
        self.assertEqual(elenco_compensi_1[0].valore, Decimal('55.00'))
        self.assertEqual(elenco_compensi_1[0].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[0].contratto, contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[1].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_1[1].agente, agente)
        self.assertEqual(elenco_compensi_1[1].importo, Decimal('165.00'))
        self.assertEqual(elenco_compensi_1[1].valore, Decimal('165.00'))
        self.assertEqual(elenco_compensi_1[1].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[1].contratto, contratto_fastweb_1)
        self.assertEqual(elenco_compensi_1[2].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_1[2].agente, agente_master)
        self.assertEqual(elenco_compensi_1[2].importo, Decimal('2000.00'))
        self.assertEqual(elenco_compensi_1[2].valore, Decimal('2000.00'))
        self.assertEqual(elenco_compensi_1[2].tipo, 'provvigione')
        self.assertEqual(elenco_compensi_1[2].contratto, contratto_fastweb_1)
        contratto_fastweb_2 = Contratto.objects.create(
            agente=agente,
            fornitore='mastervoice', data_stipula=date(year=2006, month=1, day=21),
            data_inizio_rate=date(year=2012, month=7, day=19), numero_rate=5,
            importo_rata_mensile=Decimal('100.00')
        )
        self.assertEqual(contratto_fastweb_2.gettone_totale_mastertraining, Decimal('0.00'))
        self.assertEqual(contratto_fastweb_2.gettone_totale_agente, Decimal('0.00'))
        elenco_compensi_2 = Compenso.objects.filter(contratto=contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2.count(), 5)
        self.assertEqual(elenco_compensi_2[0].data, date(year=2012, month=11, day=19))
        self.assertEqual(elenco_compensi_2[0].agente, agente_master)
        self.assertEqual(elenco_compensi_2[0].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[0].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[0].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[0].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[1].data, date(year=2012, month=10, day=19))
        self.assertEqual(elenco_compensi_2[1].agente, agente_master)
        self.assertEqual(elenco_compensi_2[1].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[1].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[1].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[1].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[2].data, date(year=2012, month=9, day=19))
        self.assertEqual(elenco_compensi_2[2].agente, agente_master)
        self.assertEqual(elenco_compensi_2[2].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[2].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[2].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[2].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[3].data, date(year=2012, month=8, day=19))
        self.assertEqual(elenco_compensi_2[3].agente, agente_master)
        self.assertEqual(elenco_compensi_2[3].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[3].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[3].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[3].contratto, contratto_fastweb_2)
        self.assertEqual(elenco_compensi_2[4].data, date(year=2012, month=7, day=19))
        self.assertEqual(elenco_compensi_2[4].agente, agente_master)
        self.assertEqual(elenco_compensi_2[4].importo, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[4].valore, Decimal('100.00'))
        self.assertEqual(elenco_compensi_2[4].tipo, 'rata')
        self.assertEqual(elenco_compensi_2[4].contratto, contratto_fastweb_2)
        primo_compenso = elenco_compensi_2[0]
        primo_compenso.importo = Decimal('999.00')
        primo_compenso.save()
        self.assertEqual(primo_compenso.importo, Decimal('999.00'))
        elenco_preordini = Progetto.objects.all()
        self.assertEqual(elenco_preordini.count(), 0)
        # nuovo_progetto = Progetto(ragione_sociale='prova', cliente=Anagrafica.objects.all()[0])
        # nuovo_progetto.save()
        # self.assertEqual(elenco_preordini.count(), 1)
        # contratto_fastweb_1.progetto = nuovo_progetto
        # contratto_fastweb_1.save()
        # GestioneContratto.objects.all().delete()
        # gestione_1 = GestioneContratto.objects.create(contratto_origine=contratto_fastweb_1)
        # GestioneContratto.objects.create(contratto_origine=contratto_fastweb_2)
        # self.assertEqual(GestioneContratto.objects.count(), 2)
        # gestione_1.contratto_destinazione = contratto_fastweb_2
        # gestione_1.save()
        # self.assertEqual(GestioneContratto.objects.count(), 1)
        # self.assertEqual(Contratto.objects.count(), 1)
        # self.assertEqual(Contratto.objects.all()[0], contratto_fastweb_2)
        # contratto_fastweb_2 = Contratto.objects.all()[0]
        # self.assertEqual(contratto_fastweb_2.progetto, nuovo_progetto)
        # elenco_compensi_2 = Compenso.objects.filter(contratto=contratto_fastweb_2)
        # self.assertEqual(elenco_compensi_2.count(), 5)
        # comp = Compenso.objects.get(pk=primo_compenso.pk)
        # self.assertEqual(comp.importo, Decimal('999.00'))
        # self.assertEqual(comp.contratto, contratto_fastweb_2)
