from django.test import TestCase
from mastergest.contratti.models import SchemaProvvigioneAgente
from mastergest.contratti.forms import SchemaProvvigioneAgenteForm


class SchemaProvvigioneAgenteFormTest(TestCase):
    fixtures = ['contratti_test']

    def setUp(self):
        schema_provvigione_1 = SchemaProvvigioneAgente.objects.get(pk=1)
        schema_provvigione_2 = SchemaProvvigioneAgente.objects.get(pk=2)
        self.assertEqual(schema_provvigione_1.fornitore, schema_provvigione_2.fornitore)
        self.assertEqual(schema_provvigione_1.agente, schema_provvigione_2.agente)

    def test_data_sbagliata(self):
        schema_provvigione_1 = SchemaProvvigioneAgente.objects.get(pk=1)
        data = dict(
            agente=schema_provvigione_1.agente_id,
            data_inizio_validita='2011-11-20',
            data_fine_validita='2011-11-10',
            mesi_liquidazione=0, fornitore=schema_provvigione_1.fornitore,
            schema_provvigione=schema_provvigione_1.schema_provvigione_id,
            percentuale_liquidazione=90, tipologia='fisso')
        form = SchemaProvvigioneAgenteForm(data=data, instance=schema_provvigione_1)
        self.assertEqual(
            form.errors['data_fine_validita'],
            ["La data di fine validita' (2011-11-10) e' precedente alla data di inizio (2011-11-20)."]
        )
