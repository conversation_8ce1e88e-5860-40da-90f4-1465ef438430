# Generated by Django 2.2.12 on 2021-08-17 16:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0035_rename_magister_matthaeus_enabled'),
    ]

    operations = [
        migrations.AddField(
            model_name='magister',
            name='storage_size',
            field=models.PositiveIntegerField(default=1, help_text='Storage size in Gb'),
        ),
        migrations.AlterField(
            model_name='magister',
            name='size',
            field=models.CharField(choices=[('1', '1'), ('2', '2'), ('4', '4')], default='1', help_text='CPU and RAM', max_length=50),
        ),
    ]
