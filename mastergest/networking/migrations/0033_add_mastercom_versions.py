# Generated by Django 2.2.12 on 2021-05-24 16:49

from django.db import migrations, models
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0032_add_mastercom_night_media_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='MastercomVersion',
            fields=[
            ],
            options={
                'verbose_name': 'mastercom version',
                'verbose_name_plural': 'mastercom versions',
                'ordering': ('id',),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('networking.mastercom',),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='mailer_db_password',
            field=models.CharField(default=mastergest.networking.models.get_random_password, max_length=200),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='mailer_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='notify_version',
            field=models.Char<PERSON><PERSON>(blank=True, db_index=True, help_text='Use only to override default', max_length=200),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='parents_dev_version',
            field=models.CharField(blank=True, help_text='Use to activate dev site', max_length=200),
        ),
    ]
