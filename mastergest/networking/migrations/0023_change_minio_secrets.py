# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-08-20 08:49
from __future__ import unicode_literals

import random
import string
from django.db import migrations


def get_random_password():
    return ''.join(random.choice(string.letters + string.digits) for _ in range(64))


def change_secrets(apps, schema_editor):
    Mastercom = apps.get_model('networking', 'Mastercom')
    for mastercom in Mastercom.objects.all():
        mastercom.minio_master_secret_key = get_random_password()
        mastercom.minio_slave_secret_key = get_random_password()
        mastercom.save()


class Migration(migrations.Migration):
    dependencies = [
        ('networking', '0022_add_minio_fields'),
    ]

    operations = [
        migrations.RunPython(change_secrets),
    ]
