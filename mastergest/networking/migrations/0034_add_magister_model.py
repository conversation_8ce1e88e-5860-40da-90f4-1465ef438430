# Generated by Django 2.2.12 on 2021-06-03 09:03

from django.db import migrations, models
import django.db.models.deletion
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0038_auto_20210422_1105'),
        ('networking', '0033_add_mastercom_versions'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='mastercom',
            name='autodeploy',
        ),
        migrations.RemoveField(
            model_name='mastercom',
            name='deployed',
        ),
        migrations.RemoveField(
            model_name='mastercom',
            name='last_deployed_config',
        ),
        migrations.CreateModel(
            name='Magister',
            fields=[
                ('id', models.CharField(db_index=True, help_text='Prefix for site name. Eg.: demo.magistersuite.com', max_length=50, primary_key=True, serialize=False)),
                ('site_id', models.CharField(blank=True, help_text='Override for site name. Usually left blank', max_length=50)),
                ('status', models.CharField(choices=[('active', 'Active'), ('disabled', 'Disabled'), ('removed', 'Removed')], default='active', max_length=50)),
                ('size', models.CharField(choices=[('1', '1'), ('2', '2'), ('4', '4')], default='1', max_length=50)),
                ('cyrenaeus_enabled', models.BooleanField(default=False)),
                ('mattaeus_enabled', models.BooleanField(default=False)),
                ('version', models.CharField(blank=True, help_text='Use only to override default', max_length=200)),
                ('admin_password', models.CharField(default=mastergest.networking.models.get_random_password, max_length=200)),
                ('db_password', models.CharField(default=mastergest.networking.models.get_random_password, max_length=200)),
                ('media_secret', models.CharField(default=mastergest.networking.models.get_random_password, max_length=200)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Sede', verbose_name='location')),
            ],
            options={
                'verbose_name': 'magister',
                'verbose_name_plural': 'magister',
                'ordering': ('id',),
            },
        ),
    ]
