# Generated by Django 2.2.7 on 2019-11-20 15:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0025_merge_20191008_0939'),
    ]

    operations = [
        migrations.AlterField(
            model_name='datitecnicicliente',
            name='sede',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Sede'),
        ),
        migrations.AlterField(
            model_name='datitecnicicliente',
            name='sottotipo',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='listino.SottoTipo'),
        ),
        migrations.AlterField(
            model_name='datitecnicicliente',
            name='tipo',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='listino.Tipo'),
        ),
        migrations.AlterField(
            model_name='hardware',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Sede', verbose_name='location'),
        ),
        migrations.AlterField(
            model_name='hardware',
            name='mastercom',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='networking.Mastercom'),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Sede', verbose_name='location'),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='slave_db_pool',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='slave_db_pool_pk', to='networking.SlaveDbPool'),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='slave_db_pool_destination',
            field=models.ForeignKey(blank=True, help_text='Leave blank: use only for slave relocation', limit_choices_to={'version': 2}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='slave_db_pool_destination_pk', to='networking.SlaveDbPool'),
        ),
        migrations.AlterField(
            model_name='servercloud',
            name='sede_cliente',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Sede'),
        ),
    ]
