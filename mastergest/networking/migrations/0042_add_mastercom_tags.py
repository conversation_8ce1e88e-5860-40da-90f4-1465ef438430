# Generated by Django 2.2.12 on 2022-02-03 15:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0041_add_mastercom_status_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTag',
            fields=[
                ('name', models.CharField(max_length=50, primary_key=True, serialize=False)),
            ],
            options={
                'verbose_name': 'mail tag',
                'verbose_name_plural': 'mail tags',
                'ordering': ('name',),
            },
        ),
        migrations.AddField(
            model_name='mastercomemailaccount',
            name='percentage',
            field=models.PositiveIntegerField(default=100),
        ),
        migrations.AlterUniqueTogether(
            name='mastercomemailaccount',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='mastercomemailaccount',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='email_tags', to='networking.EmailTag'),
        ),
    ]
