# Generated by Django 2.2.10 on 2020-08-25 07:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0029_add_mailer_models'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='mastercom',
            name='os',
            field=models.CharField(choices=[('ubuntu_precise', 'Ubuntu 12.04 Precise'), ('ubuntu_xenial', 'Ubuntu 16.04 Xenial'), ('ubuntu_bionic', 'Ubuntu 18.04 Bionic'), ('ubuntu_focal', 'Ubuntu 20.04 Focal')], max_length=50),
        ),
    ]
