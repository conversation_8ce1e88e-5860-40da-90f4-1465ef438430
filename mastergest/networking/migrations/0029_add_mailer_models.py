# Generated by Django 2.2.10 on 2020-05-07 15:18

from django.db import migrations, models
import django.db.models.deletion
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0028_auto_20191218_1001'),
    ]

    operations = [
        migrations.CreateModel(
            name='Email<PERSON>rovider',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('host', models.CharField(max_length=200)),
                ('port', models.IntegerField(default=587)),
                ('security', models.CharField(choices=[('', 'None'), ('tls', 'Tls'), ('ssl', 'Ssl')], default='tls', max_length=50)),
                ('recipients_per_message', models.IntegerField(default=0)),
                ('mails_per_day', models.IntegerField(default=0)),
                ('total_recipients_per_day', models.IntegerField(default=0)),
                ('unique_recipients_per_day', models.IntegerField(default=0)),
            ],
            options={
                'verbose_name': 'mail provider',
                'verbose_name_plural': 'mail providers',
                'ordering': ('name',),
            },
        ),
        migrations.AddField(
            model_name='mastercom',
            name='mailer_password',
            field=models.CharField(default=mastergest.networking.models.get_random_password, max_length=200),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='mailer_version',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.CreateModel(
            name='MastercomEmailAccount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('order', models.IntegerField(default=1)),
                ('username', models.CharField(max_length=200)),
                ('password', models.CharField(max_length=200)),
                ('enabled', models.BooleanField(default=True)),
                ('mastercom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_accounts', to='networking.Mastercom')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_providers', to='networking.EmailProvider')),
            ],
            options={
                'verbose_name': 'mastercom mail account',
                'verbose_name_plural': 'mastercom mail accounts',
                'ordering': ('order',),
                'unique_together': {('mastercom', 'email', 'enabled')},
            },
        ),
    ]
