# Generated by Django 2.2.12 on 2021-12-21 09:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0039_add_mastercom_moodle'),
    ]

    operations = [
        migrations.AddField(
            model_name='magister',
            name='cyrenaeus_currency',
            field=models.CharField(choices=[('EUR', 'EUR - Euro'), ('CAD', 'CAD - Dollaro Canadese'), ('CHF', 'CHF - Franco Svizzero')], default='EUR', max_length=200),
        ),
        migrations.AddField(
            model_name='magister',
            name='matthaeus_currency',
            field=models.CharField(choices=[('EUR', 'EUR - Euro'), ('CAD', 'CAD - Dollaro Canadese'), ('CHF', 'CHF - Franco Svizzero')], default='EUR', max_length=200),
        ),
    ]
