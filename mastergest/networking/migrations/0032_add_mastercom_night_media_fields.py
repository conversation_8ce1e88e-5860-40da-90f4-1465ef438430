# Generated by Django 2.2.12 on 2021-02-23 08:33

from django.db import migrations, models
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0031_add_mastercom_notify_enabled'),
    ]

    operations = [
        migrations.AddField(
            model_name='mastercom',
            name='night_media_kms',
            field=models.CharField(default=mastergest.networking.models.get_random_password_32, max_length=200),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='night_media_secret',
            field=models.Char<PERSON><PERSON>(default=mastergest.networking.models.get_random_password, max_length=200),
        ),
    ]
