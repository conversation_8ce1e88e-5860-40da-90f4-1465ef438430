# Generated by Django 2.2.12 on 2021-12-13 16:15

from django.db import migrations, models
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0038_auto_20210823_1208'),
    ]

    operations = [
        migrations.AddField(
            model_name='mastercom',
            name='moodle_api_password',
            field=models.CharField(default=mastergest.networking.models.get_random_password, max_length=200),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='moodle_db_password',
            field=models.<PERSON><PERSON><PERSON><PERSON>(default=mastergest.networking.models.get_random_password, max_length=200),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='moodle_enabled',
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name='mastercom',
            name='moodle_admin_password',
            field=models.Char<PERSON>ield(default=mastergest.networking.models.get_random_password, max_length=200),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='mastercom',
            name='moodle_version',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=50),
        ),
    ]
