# Generated by Django 2.2.7 on 2019-12-18 09:01

from django.db import migrations, models
import django.db.models.deletion
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0027_auto_20191205_1734'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mastercom',
            name='slave_db_pool',
            field=models.ForeignKey(blank=True, default=mastergest.networking.models.get_default_slave_db_pool, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='slave_db_pool_pk', to='networking.SlaveDbPool'),
        ),
    ]
