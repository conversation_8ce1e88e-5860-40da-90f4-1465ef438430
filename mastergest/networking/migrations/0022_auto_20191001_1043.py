# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-01 08:43
from __future__ import unicode_literals

from django.db import migrations, models
import mastergest.networking.models


class Migration(migrations.Migration):

    dependencies = [
        ('networking', '0021_readd_version_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='hardware',
            name='type',
            field=models.CharField(blank=True, choices=[('mastercom', 'Mastercom'), ('firewall', 'Firewall'), ('totem', 'Totem'), ('pinguino', 'Pinguino'), ('controller_unifi', 'Controller UniFi')], max_length=50),
        ),
        migrations.AlterField(
            model_name='hardware',
            name='username',
            field=models.CharField(default='root', max_length=200),
        ),
        migrations.AlterField(
            model_name='interfacciarete',
            name='tipo',
            field=models.Char<PERSON>ield(choices=[('virtuale', 'Virtuale'), ('fisica', 'Fisica')], default='fisica', max_length=200),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='master_db_cluster_name',
            field=models.CharField(blank=True, default='main', max_length=200),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='os',
            field=models.CharField(choices=[('ubuntu_precise', 'Ubuntu 12.04 Precise'), ('ubuntu_xenial', 'Ubuntu 16.04 Xenial'), ('ubuntu_bionic', 'Ubuntu 18.04 Bionic')], max_length=50),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='repository',
            field=models.CharField(choices=[('stable', 'Stable'), ('testing', 'Testing')], default='stable', max_length=50),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='school_id',
            field=models.PositiveIntegerField(help_text='Old id (to be dismissed).', null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='status',
            field=models.CharField(choices=[('installation', 'Installation'), ('active', 'Active'), ('suspended', 'Suspended'), ('dismissed', 'Dismissed')], default='installation', max_length=50),
        ),
        migrations.AlterField(
            model_name='mastercom',
            name='username',
            field=models.CharField(default='root', max_length=200),
        ),
        migrations.AlterField(
            model_name='mastercomparameter',
            name='name',
            field=models.CharField(db_column='nome', max_length=200, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='mastercomparameter',
            name='notes',
            field=models.TextField(blank=True, db_column='note'),
        ),
        migrations.AlterField(
            model_name='mastercomparameter',
            name='value',
            field=models.CharField(db_column='valore', max_length=200),
        ),
        migrations.AlterField(
            model_name='mastercomparameterdatabase',
            name='name',
            field=models.CharField(db_column='nome', max_length=200, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='mastercomparameterdatabase',
            name='notes',
            field=models.TextField(blank=True, db_column='note'),
        ),
        migrations.AlterField(
            model_name='servercloud',
            name='provider',
            field=models.CharField(choices=[('ovh', 'OVH'), ('fastvps', 'FastVps'), ('hetzner', 'Hetzner'), ('kimsufi', 'KimSufi'), ('soyoustart', 'SoYouStart')], max_length=200),
        ),
        migrations.AlterField(
            model_name='servercloud',
            name='settore',
            field=models.CharField(blank=True, choices=[('mastercom', 'MasterCom'), ('mastervoice', 'MasterVoice'), ('mastertraining', 'MasterTraining')], max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='servercloud',
            name='status',
            field=models.CharField(choices=[('mantenere', 'Mantenere'), ('eliminare', 'Eliminare')], default='mantenere', max_length=200),
        ),
        migrations.AlterField(
            model_name='servercloud',
            name='username',
            field=models.CharField(default='root', max_length=200),
        ),
        migrations.AlterField(
            model_name='slavedbpool',
            name='version',
            field=models.PositiveIntegerField(choices=[(1, '1'), (2, '2')], default=2),
        ),
    ]
