from django.core.management.base import BaseCommand

from mastergest.anagrafe.models import Comune, Anagrafica


class Command(BaseCommand):
    help = 'Aggiorna i comuni nelle anagrafiche'
    requires_system_checks = False

    def handle(self, *args, **options):
        elenco_clienti = Anagrafica.objects.filter(
            comune__isnull=True, stato_id=1, attivo=True, cliente=True, tipo_cliente='cliente'
        )
        print('TROVATI %s CLIENTI ATTIVI ITALIANI SENZA COMUNE INSERITO' % elenco_clienti.count())
        elenco_aggiornati = 0
        senza_citta = 0
        citta_non_trovata = 0
        for cliente in elenco_clienti:
            if not cliente.comune:
                if cliente.citta:
                    try:
                        if cliente.provincia:
                            comune = Comune.objects.get(nome__iexact=cliente.citta.strip(), provincia=cliente.provincia)
                        else:
                            comune = Comune.objects.get(nome__iexact=cliente.citta.strip())
                        cliente.comune = comune
                        cliente.save()
                        elenco_aggiornati += 1
                    except <PERSON>mu<PERSON>.DoesNotExist:
                        print('%s - citta %s non trovata tra i comuni!' % (cliente, cliente.citta))
                        citta_non_trovata += 1
                    except Exception as e:
                        print(cliente, e)
                else:
                    print('%s SENZA CITTA INSERITA!' % cliente)
                    senza_citta += 1
        print('AGGIORNATI %s CLIENTI' % elenco_aggiornati)
        print('CLIENTI SENZA CITTA: %s' % senza_citta)
        print('CLIENTI CITTA NON RICONOSCIUTA: %s' % citta_non_trovata)
