from __future__ import absolute_import
from __future__ import division
from __future__ import print_function
from __future__ import unicode_literals
from django.core.management.base import BaseCommand

from mastergest.anagrafe.models import Sede, Referente

class Command(BaseCommand):

    def handle(self, *args, **options):
        elenco_referenti = Referente.objects.filter(sede_cliente__isnull=True).order_by('-id')
        for referente in elenco_referenti:
            print('%s - %s' % (referente.id, referente))
            if not referente.sede_cliente:
                if referente.anagrafica:
                    try:
                        sede_legale = Sede.objects.get(anagrafica=referente.anagrafica, legale=True)
                        referente.sede_cliente = sede_legale
                        referente.save()
                    except Sede.DoesNotExist:
                        print('%s - Sede non trovata!' % referente)
