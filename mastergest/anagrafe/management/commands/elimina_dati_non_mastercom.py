from django.core.management.base import BaseCommand

from mastergest.anagrafe.models import Anagrafica, Sede, Referente
from mastergest.contratti.models import (
    Contra<PERSON>, Preordine, Compenso, MaterialePreordine, MaterialeContratto, Agente
)
from mastergest.fatturazione.models import Fattura, RigaFattura, ScadenzaPagamento
from mastergest.canoni.models import Canone, ScadenzaCanone, LineaDati, DatiLineaKpn
from mastergest.sdi.models import (
    TrasmissioneFattura, RicezioneNotifica, RichiestaRicevuta, RicezioneFattura, 
    FatturaSDI, RigaFatturaSDI, ScadenzaFornitoreSDI
)
from mastergest.incassi.models import Incasso, Abbinamento
from mastergest.solleciti.models import Sollecito, SchedaSolleciti, ScadenzaSollecito, Azione
from mastergest.assistenza.models import Compito, Chiamata, TempoAssistenza, Formazione, Intervento
from mastergest.offerte.models import (
    Offerta, RigaOfferta, AccessorioOfferta, LineaInternetOfferta, ProgettoTest,
    ContattoCommerciale, IncontroCommerciale
)
from mastergest.mastervoice.models import StatoAvanzamento, <PERSON>getto
from mastergest.networking.models import (
    Hardware, Mastercom, Magister
)
from django.contrib.auth.models import User
from mastergest.attachments.models import Attachment
from mastergest.anagrafe.models import ProfiloUtente


class Command(BaseCommand):
    help = 'ELIMINA TUTTI I DATI NON MASTERCOM DAL Database'
    requires_system_checks = False

    def handle(self, *args, **options):
        clienti_non_mastercom = Anagrafica.objects.filter(mastercom=False)
        print('TROVATI %s CLIENTI NON MASTERCOM' % clienti_non_mastercom.count())

        clienti_mastercom = Anagrafica.objects.filter(mastercom=True)
        print('TROVATI %s CLIENTI MASTERCOM' % clienti_mastercom.count())

        sedi_non_mastercom = Sede.objects.filter(anagrafica__in=clienti_non_mastercom)
        print('TROVATE %s SEDI NON MASTERCOM' % sedi_non_mastercom.count())

        sedi_mastercom = Sede.objects.filter(anagrafica__in=clienti_mastercom)
        print('TROVATE %s SEDI NON MASTERCOM' % sedi_mastercom.count())

        elenco_contratti_non_mastercom = Contratto.objects.exclude(sede__in=sedi_mastercom)
        print('TROVATI %s CONTRATTI NON MASTERCOM' % elenco_contratti_non_mastercom.count())

        # ELIMINA I CANONI NON MASTERCOM
        elenco_canoni_non_mastercom = Canone.objects.filter(contratto__in=elenco_contratti_non_mastercom)
        print('TROVATI %s CANONI NON MASTERCOM' % elenco_canoni_non_mastercom.count())
        elenco_scadenze_non_mastercom = ScadenzaCanone.objects.filter(canone__in=elenco_canoni_non_mastercom)
        print('TROVATE %s SCADENZE NON MASTERCOM' % elenco_scadenze_non_mastercom.count())
        LineaDati.objects.all().delete()
        print('LineaDati ELIMINATE!')
        DatiLineaKpn.objects.all().delete()
        print('DatiLineaKpn ELIMINATE!')
        elenco_scadenze_non_mastercom.delete()
        print('SCADENZE NON MASTERCOM ELIMINATE!')
        elenco_canoni_non_mastercom.delete()
        print('CANONI NON MASTERCOM ELIMINATI!')

        # ELIMINA LE SCADENZE DEI CANONI GIA FATTURATI
        elenco_scadenze_fatturate = ScadenzaCanone.objects.filter(fatturato=True)
        print('TROVATE %s SCADENZE FATTURATE' % elenco_scadenze_fatturate.count())
        elenco_scadenze_fatturate.delete()
        print('SCADENZE GIA FATTURATE ELIMINATE!')

        # ELIMINA I CONTRATTI NON MASTERCOM
        elenco_compensi = Compenso.objects.all()
        print('TROVATI %s COMPENSI' % elenco_compensi.count())
        elenco_compensi._raw_delete(elenco_compensi.db)

        elenco_materiali_contratti = MaterialeContratto.objects.filter(contratto__in=elenco_contratti_non_mastercom)
        print('TROVATI %s MATERIALI CONTRATTI NON MASTERCOM' % elenco_materiali_contratti.count())
        elenco_materiali_contratti._raw_delete(elenco_materiali_contratti.db)
        print('MATERIALI CONTRATTI NON MASTERCOM ELIMINATI!')
                
        elenco_contratti_non_mastercom.delete()
        print('CONTRATTI NON MASTERCOM ELIMINATI!')

        # ELIMINA I DATI SDI
        elenco_ricezioni = RicezioneNotifica.objects.all()
        print('TROVATE %s RICEZIONI NOTIFICHE' % elenco_ricezioni.count())
        elenco_ricezioni.delete()
        print('RICEZIONI NOTIFICHE ELIMINATE!')

        elenco_trasmissioni = TrasmissioneFattura.objects.all()
        print('TROVATE %s TRASMISSIONI FATTURE' % elenco_trasmissioni.count())
        elenco_trasmissioni.delete()
        print('TRASMISSIONI FATTURE ELIMINATE!')

        elenco_richieste_ricevute = RichiestaRicevuta.objects.all()
        print('TROVATE %s RICHIESTE RICEVUTE' % elenco_richieste_ricevute.count())
        elenco_richieste_ricevute.delete()
        print('RICHIESTE RICEVUTE ELIMINATE!')

        elenco_scadenze_fornitore_sdi = ScadenzaFornitoreSDI.objects.all()
        print('TROVATE %s SCADENZE FORNITORE SDI' % elenco_scadenze_fornitore_sdi.count())
        elenco_scadenze_fornitore_sdi.delete()
        print('SCADENZE FORNITORE ELIMINATE!')

        elenco_righe_fatture_sdi = RigaFatturaSDI.objects.all()
        print('TROVATE %s RIGHE FATTURE SDI' % elenco_righe_fatture_sdi.count())
        elenco_righe_fatture_sdi.delete()
        print('RIGHE FATTURE SDI ELIMINATE!')

        elenco_fatture_sdi = FatturaSDI.objects.all()
        print('TROVATE %s FATTURE SDI' % elenco_fatture_sdi.count())
        elenco_fatture_sdi.delete()
        print('FATTURE SDI ELIMINATE!')

        elenco_ricezioni_fatture = RicezioneFattura.objects.all()
        print('TROVATE %s RICEZIONI FATTURE' % elenco_ricezioni_fatture.count())
        elenco_ricezioni_fatture.delete()
        print('RICEZIONI FATTURE ELIMINATE!')

        # ELIMINA GLI INCASSI
        elenco_abbinamenti = Abbinamento.objects.all()
        print('TROVATI %s ABBINAMENTI' % elenco_abbinamenti.count())
        elenco_abbinamenti._raw_delete(elenco_abbinamenti.db)
        print('ABBINAMENTI ELIMINATI!')

        elenco_incassi = Incasso.objects.all()
        print('TROVATI %s INCASSI' % elenco_incassi.count())
        elenco_incassi._raw_delete(elenco_incassi.db)
        print('INCASSI ELIMINATI!')

        # ELIMINA I SOLLECITI
        elenco_azioni = Azione.objects.all()
        print('TROVATE %s AZIONI' % elenco_azioni.count())
        elenco_azioni._raw_delete(elenco_azioni.db)
        print('AZIONI ELIMINATE!')

        elenco_scadenze_solleciti = ScadenzaSollecito.objects.all()
        print('TROVATE %s SCADENZE SOLLECITI' % elenco_scadenze_solleciti.count())
        elenco_scadenze_solleciti._raw_delete(elenco_scadenze_solleciti.db)
        print('SCADENZE SOLLECITI ELIMINATE!')
         
        elenco_solleciti = Sollecito.objects.all()
        print('TROVATI %s SOLLECITI' % elenco_solleciti.count())
        elenco_solleciti._raw_delete(elenco_solleciti.db)
        print('SOLLECITI ELIMINATI!')

        elenco_schede_solleciti = SchedaSolleciti.objects.all()
        print('TROVATE %s SCHEDE SOLLECITI' % elenco_schede_solleciti.count())
        elenco_schede_solleciti._raw_delete(elenco_schede_solleciti.db)
        print('SCHEDE SOLLECITI ELIMINATE!')

        # ELIMINA LE FATTURE
        elenco_righe_fatture = RigaFattura.objects.all()
        print('TROVATE %s RIGHE FATTURE' % elenco_righe_fatture.count())
        elenco_righe_fatture._raw_delete(elenco_righe_fatture.db)
        print('RIGHE FATTURE ELIMINATE!')

        elenco_scadenze_pagamento = ScadenzaPagamento.objects.all()
        print('TROVATE %s SCADENZE PAGAMENTO' % elenco_scadenze_pagamento.count())
        elenco_scadenze_pagamento._raw_delete(elenco_scadenze_pagamento.db)
        print('SCADENZE PAGAMENTO ELIMINATE!')
                
        elenco_fatture = Fattura.objects.all()
        print('TROVATE %s FATTURE' % elenco_fatture.count())
        elenco_fatture._raw_delete(elenco_fatture.db)
        print('FATTURE ELIMINATE!')

        # ELIMINA COMPITI E CHIAMATE
        elenco_compiti_non_mastercom = Compito.objects.filter(cliente__in=clienti_non_mastercom)
        print('TROVATI %s COMPITI NON MASTERCOM' % elenco_compiti_non_mastercom.count())

        elenco_tempi_assistenza = TempoAssistenza.objects.filter(compito__in=elenco_compiti_non_mastercom)
        print('TROVATI %s TEMPI ASSISTENZA NON MASTERCOM' % elenco_tempi_assistenza.count())
        elenco_tempi_assistenza._raw_delete(elenco_tempi_assistenza.db)
        print('TEMPI ASSISTENZA NON MASTERCOM ELIMINATI!')

        elenco_compiti_non_mastercom._raw_delete(elenco_compiti_non_mastercom.db)
        print('COMPITI NON MASTERCOM ELIMINATI!')

        elenco_chiamate_non_mastercom = Chiamata.objects.filter(cliente__in=clienti_non_mastercom)
        print('TROVATE %s CHIAMATE NON MASTERCOM' % elenco_chiamate_non_mastercom.count())
        elenco_chiamate_non_mastercom._raw_delete(elenco_chiamate_non_mastercom.db)
        print('CHIAMATE NON MASTERCOM ELIMINATE!')

        elenco_formazioni_non_mc = Formazione.objects.filter(sede_cliente__in=sedi_non_mastercom)
        print('TROVATE %s FORMAZIONI NON MASTERCOM' % elenco_formazioni_non_mc.count())
        elenco_formazioni_non_mc.delete()
        print('FORMAZIONI NON MASTERCOM ELIMINATE!')

        elenco_interventi_non_mc = Intervento.objects.filter(sede_cliente__in=sedi_non_mastercom)
        print('TROVATI %s INTERVENTI NON MASTERCOM' % elenco_interventi_non_mc.count())
        elenco_interventi_non_mc._raw_delete(elenco_interventi_non_mc.db)
        print('INTERVENTI NON MASTERCOM ELIMINATI!')

        # ELIMINA LE OFFERTE
        
        elenco_offerte_non_mastercom = Offerta.objects.filter(azienda__in=clienti_non_mastercom)
        print('TROVATE %s OFFERTE NON MASTERCOM' % elenco_offerte_non_mastercom.count())

        elenco_materiali_preordine = MaterialePreordine.objects.all()
        print('TROVATI %s MATERIALI PREORDINE' % elenco_materiali_preordine.count())
        elenco_materiali_preordine._raw_delete(elenco_materiali_preordine.db)

        elenco_preordini = Preordine.objects.exclude(sede_cliente__in=sedi_mastercom)
        print('TROVATI %s PREORDINI NON MC' % elenco_preordini.count())
        elenco_preordini._raw_delete(elenco_preordini.db)
        print('PREORDINI NON MC ELIMINATI!')

        elenco_accessori_offerte_non_mastercom = AccessorioOfferta.objects.filter(offerta__in=elenco_offerte_non_mastercom)
        print('TROVATI %s ACCESSORI OFFERTE NON MASTERCOM' % elenco_accessori_offerte_non_mastercom.count())
        elenco_accessori_offerte_non_mastercom._raw_delete(elenco_accessori_offerte_non_mastercom.db)
        print('ACCESSORI OFFERTE NON MASTERCOM ELIMINATI!')
        
        elenco_righe_offerte_non_mastercom = RigaOfferta.objects.filter(offerta__in=elenco_offerte_non_mastercom)
        print('TROVATE %s RIGHE OFFERTE NON MASTERCOM' % elenco_righe_offerte_non_mastercom.count())
        elenco_righe_offerte_non_mastercom._raw_delete(elenco_righe_offerte_non_mastercom.db)
        print('RIGHE OFFERTE NON MASTERCOM ELIMINATE!')

        elenco_linee_internet_offerte_non_mastercom = LineaInternetOfferta.objects.filter(offerta__in=elenco_offerte_non_mastercom)
        print('TROVATE %s LINEE INTERNET OFFERTE NON MASTERCOM' % elenco_linee_internet_offerte_non_mastercom.count())
        elenco_linee_internet_offerte_non_mastercom._raw_delete(elenco_linee_internet_offerte_non_mastercom.db)
        print('LINEE INTERNET NON MASTERCOM ELIMINATE!')

        elenco_offerte_non_mastercom._raw_delete(elenco_offerte_non_mastercom.db)
        print('OFFERTE NON MASTERCOM ELIMINATE!')

        # ELIMINA I PROGETTI
        elenco_progetti_test = ProgettoTest.objects.all()
        print('TROVATI %s PROGETTI TEST' % elenco_progetti_test.count())
        elenco_progetti_test._raw_delete(elenco_progetti_test.db)   
        print('PROGETTI TEST ELIMINATI!')

        elenco_progetti_non_mc = Progetto.objects.filter(cliente__in=clienti_non_mastercom)
        print('TROVATI %s PROGETTI' % elenco_progetti_non_mc.count())

        elenco_stati_avanzamento = StatoAvanzamento.objects.filter(progetto__in=elenco_progetti_non_mc)
        print('TROVATI %s STATI AVANZAMENTO' % elenco_stati_avanzamento.count())
        elenco_stati_avanzamento._raw_delete(elenco_stati_avanzamento.db)   
        print('STATI AVANZAMENTO ELIMINATI!')

        elenco_progetti_non_mc._raw_delete(elenco_progetti_non_mc.db)
        print('PROGETTI ELIMINATI!')

        # ELIMINA NETWORKING
        elenco_dati_server_non_mc = Mastercom.objects.filter(location__in=sedi_non_mastercom)
        print('TROVATI %s SERVER NON MASTERCOM' % elenco_dati_server_non_mc.count())

        elenco_hardware = Hardware.objects.filter(mastercom__in=elenco_dati_server_non_mc)
        print('TROVATI %s HARDWARE NON MASTERCOM' % elenco_hardware.count())
        elenco_hardware.delete()
        print('HARDWARE ELIMINATI!')
        
        elenco_dati_server_non_mc.delete()
        print('SERVER NON MASTERCOM ELIMINATI!')

        elenco_dati_magister = Magister.objects.all()
        print('TROVATI %s MAGISTER' % elenco_dati_magister.count())
        elenco_dati_magister.delete()
        print('MAGISTER ELIMINATI!')

        # ELIMINA I CONTATTI COMMERCIALI
        elenco_contatti_commerciali = ContattoCommerciale.objects.exclude(azienda__in=clienti_mastercom)
        print('TROVATI %s CONTATTI COMMERCIALI' % elenco_contatti_commerciali.count())

        elenco_incontri_commerciali = IncontroCommerciale.objects.filter(contatto_commerciale__in=elenco_contatti_commerciali)
        print('TROVATI %s INCONTRI COMMERCIALI NON MASTERCOM' % elenco_incontri_commerciali.count())
        elenco_incontri_commerciali.delete()
        print('INCONTRI COMMERCIALI ELIMINATI!')

        elenco_contatti_commerciali.delete()
        print('CONTATTI COMMERCIALI ELIMINATI!')

        # ELIMINA UTENTI
        # elenco_utenti_agenti = User.objects.filter(utente_pk__agente__isnull=False)
        # print('TROVATI %s UTENTI AGENTI NON MASTERCOM' % elenco_utenti_agenti.count())

        # elenco_allegati_agenti = Attachment.objects.filter(creator__in=elenco_utenti_agenti)
        # print('TROVATI %s ALLEGATI AGENTI' % elenco_allegati_agenti.count())
        # elenco_allegati_agenti.delete()

        # elenco_agenti = Agente.objects.all()
        # print('TROVATI %s AGENTI' % elenco_agenti.count())
        # elenco_agenti.delete()
        # print('AGENTI ELIMINATI!')

        # elenco_utenti_agenti.delete()
        # print('UTENTI AGENTI ELIMINATI!')

        elenco_utenti_traffico = User.objects.filter(utente_pk__cliente_traffico=True)
        print('TROVATI %s UTENTI TRAFFICO NON MASTERCOM' % elenco_utenti_traffico.count())

        elenco_profili_utente = ProfiloUtente.objects.filter(user__in=elenco_utenti_traffico)
        print('TROVATI %s PROFILI UTENTE NON MASTERCOM' % elenco_profili_utente.count())
        elenco_profili_utente.delete()
        print('PROFILO UTENTE ELIMINATI!')


        elenco_utenti_traffico.delete()
        print('UTENTI TRAFFICO ELIMINATI!')


        # ELIMINA LE ANAGRAFICHE NON MASTERCOM
        elenco_referenti_non_mastercom = Referente.objects.filter(anagrafica__in=clienti_non_mastercom)
        print('TROVATI %s REFERENTI NON MASTERCOM' % elenco_referenti_non_mastercom.count())
        elenco_referenti_non_mastercom.delete()
        print('REFERENTI NON MASTERCOM ELIMINATI!')

        # sedi_non_mastercom.delete()
        # print('SEDE NON MASTERCOM ELIMINATE!')
        # clienti_non_mastercom.delete()
        # print('CLIENTI NON MASTERCOM ELIMINATI!')

        print('TUTTI I DATI NON MASTERCOM ELIMINATI!')
