from django.db.models.deletion import Collector
from django.db import models
from django.db import router
from pprint import pprint
from django.core.management.base import BaseCommand
from optparse import make_option
from mastergest.anagrafe.models import Anagrafica, Sede


class Command(BaseCommand):
    help = 'Scambio Anagrafiche'
    option_list = BaseCommand.option_list + (
        make_option('-a', '--anagrafica', action='store_true', dest='anagrafica',
            default=False),
        make_option('-s', '--sedi', action='store_true', dest='sedi',
            default=False),
    )
    requires_system_checks = False

    def handle(self, *args, **options):
        if options['anagrafica']:
            if args:
                if len(args) == 2:
                    origine = Anagrafica.objects.get(id=args[0])
                    if origine:
                        destinazione = Anagrafica.objects.get(id=args[1])
                        if destinazione:
                            print('MIGRAZIONE ANAGRAFICA DA: %s %s ------> %s %s' % (args[0], origine, args[1], destinazione))
                            msg = "\nAttenzione! il comando corrente procedera' con la migrazione dell'Anagrafica da:\n%s %s\na:\n%s %s\n" \
                                    "Procedere? (si/no): " % (args[0], origine, args[1], destinazione)
                            confirm = input(msg)
                            while 1:
                                if confirm not in ('si', 'no'):
                                    confirm = input('Prego scrivere "si" o "no": ')
                                    continue
                                if confirm == 'si':
                                    self.migra_anagrafica(origine, destinazione)
                                if confirm == 'no':
                                    return
                                break
                    else:
                        print('ORIGINE O DESTINAZIONE NON TROVATE!')
                else:
                    print('ERRORE! MANCA UN PARAMETRO')
            else:
                print('ERRORE! NESSUN PARAMETRO FORNITO')
        if options['sedi']:
            if args:
                if len(args) == 2:
                    origine = Sede.objects.get(id=args[0])
                    if origine:
                        destinazione = Sede.objects.get(id=args[1])
                        if destinazione:
                            print('MIGRAZIONE SEDE DA: %s %s ------> %s %s' % (args[0], origine, args[1], destinazione))
                            msg = "\nAttenzione! il comando corrente procedera' con la migrazione della Sede da:\n%s %s\na:\n%s %s\n" \
                                    "Procedere? (si/no): " % (args[0], origine, args[1], destinazione)
                            confirm = input(msg)
                            while 1:
                                if confirm not in ('si', 'no'):
                                    confirm = input('Prego scrivere "si" o "no": ')
                                    continue
                                if confirm == 'si':
                                    self.migra_sede(origine, destinazione)
                                if confirm == 'no':
                                    return
                                break
                    else:
                        print('ORIGINE O DESTINAZIONE NON TROVATE!')
                else:
                    print('ERRORE! MANCA UN PARAMETRO')
            else:
                print('ERRORE! NESSUN PARAMETRO FORNITO')

    def get_related(self, queryset):
        using = router.db_for_read(queryset.model)
        coll = Collector(using=using)
        coll.collect(queryset, nullable=True)
        return coll.data

    def find_related(self, cl, app):
        """Find all classes which are related to the class cl (in app) by
        having it as a foreign key."""
        all_models = models.get_models()
        ci_model = models.get_model(app, cl)
        elenco_modelli = []
        for a_model in all_models:
            for f in a_model._meta.fields:
                if isinstance(f, models.ForeignKey) and (f.rel.to == ci_model):
                    if not a_model._meta.proxy:
                        elenco_modelli.append(a_model)
        return elenco_modelli

    def migra_anagrafica(self, origine, destinazione):
        elenco_collegamenti = self.find_related('Anagrafica', 'anagrafe')
        if elenco_collegamenti:
            pprint(elenco_collegamenti)
            for classe in elenco_collegamenti:
                if not classe.__name__ == 'SchedaSolleciti' and not classe.__name__ == 'AnagraficaTabSchool':
                    nome_campo = None
                    if hasattr(classe, 'anagrafica'):
                        nome_campo = 'anagrafica'
                        print('CLASSE CON CAMPO <ANAGRAFICA>')
                    elif hasattr(classe, 'cliente'):
                        nome_campo = 'cliente'
                        print('CLASSE CON CAMPO <CLIENTE>')
                    elif hasattr(classe, 'azienda'):
                        nome_campo = 'azienda'
                        print('CLASSE CON CAMPO <AZIENDA>')
                    elif hasattr(classe, 'scuola'):
                        nome_campo = 'scuola'
                        print('CLASSE CON CAMPO <AZIENDA>')
                    elif hasattr(classe, 'fornitore'):
                        nome_campo = 'fornitore'
                        print('CLASSE CON CAMPO <FORNITORE>')
                    elif hasattr(classe, 'rivenditore'):
                        nome_campo = 'rivenditore'
                        print('CLASSE CON CAMPO <FORNITORE>')
                    else:
                        print(classe, ' ----> Oggetto ignorato - foreign key sconosciuta')
                    if nome_campo:
                        d = dict()
                        d[nome_campo] = origine
                        elenco_oggetti = classe.objects.filter(**d)
                        if elenco_oggetti:
                            for oggetto in elenco_oggetti:
                                if classe.__name__ == 'Sede':
                                    oggetto.legale = False
                                    oggetto.descrizione += ' - ' + origine.ragione_sociale
                                setattr(oggetto, nome_campo, destinazione)
                                oggetto.save()
                                print('%s %s (id:%s) modificato!' % (classe.__name__, oggetto, oggetto.id))
                        else:
                            print('NESSUN OGGETTO %s trovato' % classe.__name__)

    def migra_sede(self, origine, destinazione):
        elenco_collegamenti = self.find_related('Sede', 'anagrafe')
        if elenco_collegamenti:
            pprint(elenco_collegamenti)
            for classe in elenco_collegamenti:
                if not classe.__name__ == 'SchedaSolleciti' and not classe.__name__ == 'AnagraficaTabSchool':
                    nome_campo = None
                    if hasattr(classe, 'sede'):
                        nome_campo = 'sede'
                        print('CLASSE CON CAMPO <SEDE>')
                    elif hasattr(classe, 'sede_cliente'):
                        nome_campo = 'cliente'
                        print('CLASSE CON CAMPO <SEDE_CLIENTE>')
                    else:
                        print(classe, ' ----> Oggetto ignorato - foreign key sconosciuta')
                    if nome_campo:
                        d = dict()
                        d[nome_campo] = origine
                        elenco_oggetti = classe.objects.filter(**d)
                        if elenco_oggetti:
                            for oggetto in elenco_oggetti:
                                setattr(oggetto, nome_campo, destinazione)
                                oggetto.save()
                                print('%s %s (id:%s) modificato!' % (classe.__name__, oggetto, oggetto.id))
                        else:
                            print('NESSUN OGGETTO %s trovato' % classe.__name__)
