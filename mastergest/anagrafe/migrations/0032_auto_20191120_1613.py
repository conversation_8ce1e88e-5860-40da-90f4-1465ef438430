# Generated by Django 2.2.7 on 2019-11-20 15:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0031_auto_20191008_0939'),
    ]

    operations = [
        migrations.AlterField(
            model_name='anagrafica',
            name='agente_riferimento',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='contratti.Agente'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='banca_appoggio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_default_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='banca_appoggio_traffico',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_traffico_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. traffico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='destinazione_documenti',
            field=models.ForeignKey(blank=True, help_text='Se non specificato verranno indirizzati alla sede legale', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='destinazione_documenti_pk', to='anagrafe.Sede'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='distretto_digitale',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.DistrettoDigitale'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='esenzione_iva_default',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fatturazione.EsenzioneIva', verbose_name='esenzione iva default fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='pagamento_default_fatture',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='pagamento_default_fatture_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='pagamento_default_traffico_voip',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='pagamento_default_traffico_voip_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default traffico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='provincia',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagrafe.Provincia'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='stato',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, to='anagrafe.Stato'),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='agente',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='contratti.Agente'),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='area_competenza',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.AreaCompetenza'),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='area_manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='area_manager_pk', to=settings.AUTH_USER_MODEL, verbose_name='Responsabile'),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='azienda',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Anagrafica'),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, primary_key=True, related_name='utente_pk', serialize=False, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='referente',
            name='anagrafica',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Anagrafica'),
        ),
        migrations.AlterField(
            model_name='referente',
            name='contatto_commerciale',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='contatto_commerciale_pk', to='offerte.ContattoCommerciale'),
        ),
        migrations.AlterField(
            model_name='referente',
            name='ruolo',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='anagrafe.RuoloReferente'),
        ),
        migrations.AlterField(
            model_name='richiestaferie',
            name='responsabile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='responsabile_pk', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='richiestaferie',
            name='richiedente',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='richiedente_pk', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='sede',
            name='stato',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, to='anagrafe.Stato'),
        ),
    ]
