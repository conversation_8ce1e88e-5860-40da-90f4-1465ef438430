# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-09-02 07:26
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0026_auto_20190802_1501'),
    ]

    operations = [
        migrations.AddField(
            model_name='anagrafica',
            name='cambio_divisa',
            field=models.DecimalField(blank=True, decimal_places=6, help_text=b'Valore in Euro della divisa selezionata al cambio attuale: es. 0.84 -> 1 euro = 0.84 * divisa selezionata', max_digits=9, null=True),
        ),
        migrations.AddField(
            model_name='anagrafica',
            name='divisa_fatturazione',
            field=models.CharField(choices=[(b'EUR', b'EUR - Euro'), (b'CHF', b'CHF - Franco Svizzero')], default=b'EUR', max_length=200),
        ),
    ]
