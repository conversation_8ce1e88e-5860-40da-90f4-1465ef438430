# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0017_stato_codice'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewsLetter',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nome', models.CharField(max_length=200)),
                ('external_id', models.CharField(max_length=200)),
            ],
            options={
                'ordering': ('nome',),
                'verbose_name': 'NewsLetter Referenti',
                'verbose_name_plural': 'NewsLetter Referenti',
            },
        ),
        migrations.AddField(
            model_name='referente',
            name='iscrizione_newsletter',
            field=models.ManyToManyField(to='anagrafe.NewsLetter'),
        ),
    ]
