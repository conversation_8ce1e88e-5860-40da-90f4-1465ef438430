# Generated by Django 2.2.28 on 2024-08-20 07:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0029_auto_20240527_1527'),
        ('anagrafe', '0052_anagrafica_fattura_capogruppo'),
    ]

    operations = [
        migrations.AddField(
            model_name='anagrafica',
            name='banca_appoggio_magister',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_magister_default_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. fatture (Magister)'),
        ),
        migrations.AddField(
            model_name='anagrafica',
            name='esenzione_iva_default_magister',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='esenzione_iva_default_magister_pk', to='fatturazione.EsenzioneIva', verbose_name='esenzione iva default fatture (Magister)'),
        ),
        migrations.AddField(
            model_name='anagrafica',
            name='pagamento_default_fatture_magister',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='pagamento_default_fatture_magister_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default fatture (Magister)'),
        ),
    ]
