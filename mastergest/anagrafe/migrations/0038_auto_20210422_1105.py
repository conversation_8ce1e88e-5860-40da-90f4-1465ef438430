# Generated by Django 2.2.12 on 2021-04-22 09:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0037_referente_sede_cliente'),
    ]

    operations = [
        migrations.CreateModel(
            name='Comu<PERSON>',
            fields=[
                ('codice', models.CharField(max_length=4, primary_key=True, serialize=False)),
                ('nome', models.Char<PERSON>ield(max_length=200)),
                ('provincia', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='anagrafe.Provincia')),
            ],
            options={
                'verbose_name_plural': 'Comuni',
                'ordering': ('nome', 'provincia'),
            },
        ),
        migrations.AddField(
            model_name='anagrafica',
            name='comune',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='anagrafe.Comune'),
        ),
    ]
