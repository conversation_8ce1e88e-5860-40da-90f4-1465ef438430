# Generated by Django 2.2.28 on 2024-08-20 07:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0029_auto_20240527_1527'),
        ('anagrafe', '0055_auto_20240820_0927'),
    ]

    operations = [
        migrations.AddField(
            model_name='sede',
            name='banca_appoggio_magister',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_magister_sede_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. fatture (Magister)'),
        ),
        migrations.AddField(
            model_name='sede',
            name='pagamento_default_fatture_magister',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='pagamento_default_fatture_sede_magister_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default fatture (Magister)'),
        ),
        migrations.AlterField(
            model_name='sede',
            name='banca_appoggio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_default_sede_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. fatture (Mastertraining)'),
        ),
        migrations.AlterField(
            model_name='sede',
            name='pagamento_default_fatture',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='pagamento_default_fatture_sede_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default fatture (Mastertraining)'),
        ),
    ]
