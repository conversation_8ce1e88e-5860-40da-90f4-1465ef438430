# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-01 08:43
from __future__ import unicode_literals

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0026_auto_20190802_1501'),
    ]

    operations = [
        migrations.AlterField(
            model_name='anagrafica',
            name='abi',
            field=models.CharField(blank=True, max_length=5, verbose_name='ABI'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='abi_rid',
            field=models.CharField(blank=True, max_length=5, verbose_name='ABI'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='alias',
            field=models.CharField(db_index=True, help_text='alias per uso interno', max_length=200, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='azienda',
            field=models.BooleanField(default=False, verbose_name='az.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='banca',
            field=models.CharField(blank=True, max_length=200, verbose_name='Banca'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='banca_appoggio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='banca_appoggio_default_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='banca_appoggio_traffico',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='banca_appoggio_traffico_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. traffico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='banca_rid',
            field=models.CharField(blank=True, max_length=200, verbose_name='Banca'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='blocco_fatturazione',
            field=models.BooleanField(default=False, help_text='Se abilitato, impedisce la modifica di fatture esistenti o la generazione di nuove fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='cab',
            field=models.CharField(blank=True, max_length=5, verbose_name='CAB'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='cab_rid',
            field=models.CharField(blank=True, max_length=5, verbose_name='CAB'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='cliente',
            field=models.BooleanField(default=True, verbose_name='cli.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='codice_pa',
            field=models.CharField(blank=True, max_length=7, null=True, verbose_name='Codice Destinatario SDI'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='destinazione_documenti',
            field=models.ForeignKey(blank=True, help_text='Se non specificato verranno indirizzati alla sede legale', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='destinazione_documenti_pk', to='anagrafe.Sede'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='dividi_fatture_canoni_competenza',
            field=models.BooleanField(default=False, help_text='Se abilitato, divide le fatture dei canoni per ogni mese relativo alla data inizio competenza', verbose_name='Dividi fatt. canoni per mese competenza'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='email',
            field=models.EmailField(blank=True, default='', help_text='E-mail istituzionale.', max_length=254, verbose_name='email'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='email_fatturazione',
            field=models.EmailField(blank=True, default='', help_text='E-mail per invio fatture.', max_length=254, verbose_name='email fatturazione'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='email_fatturazione_cc',
            field=models.CharField(blank=True, default='', help_text='E-mail addizionali per invio fatture. (separare con ";" per inserimenti multipli)', max_length=1000, verbose_name='email fatturazione (CC)'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='email_referente_amministrativo',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email Ref. Amm.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='email_referente_tecnico',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email Ref. Tecnico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='esenzione_iva_default',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fatturazione.EsenzioneIva', verbose_name='esenzione iva default fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='fatturato_recente',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Imponibile degli ultimi 12 mesi', max_digits=9, verbose_name='fatturato recente tot. (€)'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='fatturato_recente_canoni',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Imponibile canoni degli ultimi 12 mesi', max_digits=9, verbose_name='fatt. recente canoni (€)'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='fatturato_recente_traffico',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Imponibile traffico degli ultimi 12 mesi', max_digits=9, verbose_name='fatt. recente traffico (€)'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='fornitore',
            field=models.BooleanField(default=False, verbose_name='forn.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='gestione_centri_costo',
            field=models.BooleanField(default=False, help_text='Se abilitato, gestisce le fatture con il riepilogo dei centri di costo inseriti nelle sedi', verbose_name='Gestione Fatture con centri di costo'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='iban',
            field=models.CharField(blank=True, max_length=50, verbose_name='IBAN'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='iban_rid',
            field=models.CharField(blank=True, max_length=50, verbose_name='IBAN'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='mastercom',
            field=models.BooleanField(default=False, verbose_name='MC'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='mastertraining',
            field=models.BooleanField(default=False, verbose_name='MT'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='mastervoice',
            field=models.BooleanField(default=False, verbose_name='MV'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='pagamento_default_fatture',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pagamento_default_fatture_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default fatture'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='pagamento_default_traffico_voip',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pagamento_default_traffico_voip_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default traffico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='pec',
            field=models.EmailField(blank=True, default='', help_text='Posta elettronica certificata.', max_length=254, verbose_name='pec'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='privato',
            field=models.BooleanField(default=False, verbose_name='priv.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='raggruppa_fatture_canoni_sedi',
            field=models.BooleanField(default=True, help_text='Se abilitato, raggruppa le fatture dei canoni per ogni cliente, altrimenti fa una fattura per ogni canone', verbose_name='Raggruppa fatt. canoni per cliente'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='raggruppa_fatture_traffico_sedi',
            field=models.BooleanField(default=False, help_text='Se abilitato, raggruppa le fatture del traffico del cliente selezionato per ogni sede, altrimenti raggruppa tutte le fatture in una per cliente', verbose_name='Raggruppa fatt. traffico per sede'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='referente_amministrativo',
            field=models.CharField(blank=True, max_length=200, verbose_name='Ref. Amministrativo'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='referente_tecnico',
            field=models.CharField(blank=True, max_length=200, verbose_name='Ref. Tecnico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='rivenditore',
            field=models.BooleanField(default=False, verbose_name='riv.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='scuola',
            field=models.BooleanField(default=False, verbose_name='scuola'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='split_payment',
            field=models.BooleanField(default=False, verbose_name='Gestito con split payment'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='telefono_referente_amministrativo',
            field=models.CharField(blank=True, max_length=200, verbose_name='Tel. Ref. Amm.'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='telefono_referente_tecnico',
            field=models.CharField(blank=True, max_length=200, verbose_name='Tel. Ref. Tecnico'),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='tipo_cliente',
            field=models.CharField(choices=[('cliente', 'Cliente'), ('contatto_commerciale', 'Contatto Commerciale')], default='cliente', max_length=200),
        ),
        migrations.AlterField(
            model_name='anagrafica',
            name='titolo',
            field=models.CharField(blank=True, help_text='Es. Spett.le/Ist.', max_length=50),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='area_manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='area_manager_pk', to=settings.AUTH_USER_MODEL, verbose_name='Responsabile'),
        ),
        migrations.AlterField(
            model_name='profiloutente',
            name='cliente_traffico',
            field=models.BooleanField(default=False, verbose_name='cliente traffico'),
        ),
        migrations.AlterField(
            model_name='provincia',
            name='area',
            field=models.CharField(choices=[('NORD_OVEST', 'Nord-Ovest'), ('NORD_EST', 'Nord-Est'), ('CENTRO', 'Centro'), ('SUD', 'Sud'), ('ISOLE', 'Isole')], max_length=200),
        ),
        migrations.AlterField(
            model_name='provincia',
            name='regione',
            field=models.CharField(choices=[('ABRUZZO', 'Abruzzo'), ('BASILICATA', 'Basilicata'), ('CALABRIA', 'Calabria'), ('CAMPANIA', 'Campania'), ('EMILIA_ROMAGNA', 'Emilia Romagna'), ('FRIULI_VENEZIA_GIULIA', 'Friuli Venezia-Giulia'), ('LAZIO', 'Lazio'), ('LIGURIA', 'Liguria'), ('LOMBARDIA', 'Lombardia'), ('MARCHE', 'Marche'), ('MOLISE', 'Molise'), ('PIEMONTE', 'Piemonte'), ('PUGLIA', 'Puglia'), ('SARDEGNA', 'Sardegna'), ('SICILIA', 'Sicilia'), ('TOSCANA', 'Toscana'), ('TRENTINO_ALTO_ADIGE', 'Trentino-Alto Adige'), ('UMBRIA', 'Umbria'), ('VALLE_DAOSTA', "Valle D'Aosta"), ('VENETO', 'Veneto')], max_length=200),
        ),
        migrations.AlterField(
            model_name='referente',
            name='iscrizione_newsletter',
            field=models.ManyToManyField(blank=True, to='anagrafe.NewsLetter', verbose_name='iscrizioni newsletter'),
        ),
        migrations.AlterField(
            model_name='richiestaferie',
            name='data_autorizzazione',
            field=models.DateTimeField(blank=True, null=True, verbose_name='data di autorizzazione'),
        ),
        migrations.AlterField(
            model_name='richiestaferie',
            name='data_richiesta',
            field=models.DateTimeField(blank=True, null=True, verbose_name='data inoltro richesta di autorizzazione'),
        ),
        migrations.AlterField(
            model_name='richiestaferie',
            name='tipo',
            field=models.CharField(choices=[('ferie', 'ferie'), ('permesso', 'permesso'), ('recupero', 'recupero')], default='ferie', max_length=200),
        ),
        migrations.AlterField(
            model_name='sede',
            name='centro_costo',
            field=models.CharField(blank=True, max_length=200, verbose_name='Codice Centro di costo'),
        ),
        migrations.AlterField(
            model_name='sede',
            name='provincia',
            field=models.CharField(blank=True, max_length=50, verbose_name='prov.'),
        ),
        migrations.AlterField(
            model_name='stato',
            name='area',
            field=models.CharField(choices=[('11', 'Unione europea'), ('12', 'Europa centro orientale'), ('13', 'Altri paesi europei'), ('21', 'Africa settentrionale'), ('22', 'Africa occidentale'), ('23', 'Africa orientale'), ('24', 'Africa centro meridionale'), ('31', 'Asia occidentale'), ('32', 'Asia centro meridionale'), ('33', 'Asia orientale'), ('41', 'America settentrionale'), ('42', 'America centro meridionale'), ('50', 'Oceania'), ('60', 'Apolidi')], max_length=200),
        ),
        migrations.AlterField(
            model_name='stato',
            name='continente',
            field=models.CharField(choices=[('1', 'Europa'), ('2', 'Africa'), ('3', 'Asia'), ('4', 'America'), ('5', 'Oceania'), ('6', 'Apolidi')], max_length=200),
        ),
    ]
