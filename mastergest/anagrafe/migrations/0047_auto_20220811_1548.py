# Generated by Django 2.2.12 on 2022-08-11 13:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fatturazione', '0020_fattura_sede_cliente'),
        ('anagrafe', '0046_sede_pec'),
    ]

    operations = [
        migrations.AddField(
            model_name='anagrafica',
            name='riferimento_amministrazione',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='sede',
            name='abi',
            field=models.CharField(blank=True, max_length=5, verbose_name='ABI'),
        ),
        migrations.AddField(
            model_name='sede',
            name='abi_rid',
            field=models.CharField(blank=True, max_length=5, verbose_name='ABI'),
        ),
        migrations.AddField(
            model_name='sede',
            name='banca',
            field=models.CharField(blank=True, max_length=200, verbose_name='Banca'),
        ),
        migrations.AddField(
            model_name='sede',
            name='banca_appoggio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='banca_appoggio_default_sede_pk', to='fatturazione.BancaAppoggio', verbose_name='banca app. fatture'),
        ),
        migrations.AddField(
            model_name='sede',
            name='banca_rid',
            field=models.CharField(blank=True, max_length=200, verbose_name='Banca'),
        ),
        migrations.AddField(
            model_name='sede',
            name='cab',
            field=models.CharField(blank=True, max_length=5, verbose_name='CAB'),
        ),
        migrations.AddField(
            model_name='sede',
            name='cab_rid',
            field=models.CharField(blank=True, max_length=5, verbose_name='CAB'),
        ),
        migrations.AddField(
            model_name='sede',
            name='iban',
            field=models.CharField(blank=True, max_length=50, verbose_name='IBAN'),
        ),
        migrations.AddField(
            model_name='sede',
            name='iban_rid',
            field=models.CharField(blank=True, max_length=50, verbose_name='IBAN'),
        ),
        migrations.AddField(
            model_name='sede',
            name='pagamento_default_fatture',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='pagamento_default_fatture_sede_pk', to='fatturazione.TipoPagamentoGamma', verbose_name='pag. default fatture'),
        ),
        migrations.AddField(
            model_name='sede',
            name='riferimento_amministrazione',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
    ]
