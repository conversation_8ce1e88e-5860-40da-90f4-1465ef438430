# -*- coding: utf-8 -*-


from django.db import migrations, models
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
        ('anagrafe', '0015_auto_20180828_1131'),
    ]

    operations = [
        migrations.AddField(
            model_name='anagrafica',
            name='fatturato_recente_canoni',
            field=models.DecimalField(default=Decimal('0.00'), help_text=b'Imponibile canoni degli ultimi 12 mesi', verbose_name='fatt. recente canoni (\u20ac)', max_digits=9, decimal_places=2),
        ),
        migrations.AddField(
            model_name='anagrafica',
            name='fatturato_recente_traffico',
            field=models.DecimalField(default=Decimal('0.00'), help_text=b'Imponibile traffico degli ultimi 12 mesi', verbose_name='fatt. recente traffico (\u20ac)', max_digits=9, decimal_places=2),
        ),
        migrations.Alter<PERSON>ield(
            model_name='anagrafica',
            name='fatturato_recente',
            field=models.DecimalField(default=Decimal('0.00'), help_text=b'Imponibile degli ultimi 12 mesi', verbose_name='fatturato recente tot. (\u20ac)', max_digits=9, decimal_places=2),
        ),
    ]
