[{"pk": 1, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Italy", "descrizione": "Italia", "area": "11"}}, {"pk": 201, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Republic of Albania", "descrizione": "Albania", "area": "12"}}, {"pk": 202, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "State of Andorra", "descrizione": "Andorra", "area": "13"}}, {"pk": "RE", "model": "anagrafe.provincia", "fields": {"regione": "EMILIA_ROMAGNA", "descrizione": "REGGIO NELL'EMILIA", "area": "NORD_EST"}}, {"pk": "MI", "model": "anagrafe.provincia", "fields": {"regione": "LOMBARDIA", "descrizione": "MILANO", "area": "NORD_OVEST"}}, {"pk": "RM", "model": "anagrafe.provincia", "fields": {"regione": "LAZIO", "descrizione": "ROMA", "area": "CENTRO"}}, {"pk": 1, "model": "anagrafe.anagrafica", "fields": {"ragione_sociale": "Mastertraining", "alias": "Mastertraining", "titolo": "Spett.", "email": "<EMAIL>", "indirizzo": "Via Timolini 18", "cap": "42015", "stato": 1, "partita_iva": "012345678910", "citta": "Correggio", "provincia": "RE", "cliente": false, "fornitore": true, "rivenditore": false, "mastervoice": false, "mastercom": false, "destinazione_documenti": 11, "url": "", "attivo": true}}, {"pk": 10, "model": "anagrafe.sede", "fields": {"anagrafica": 1, "legale": true, "email": "<EMAIL>", "indirizzo": "Via Timolini 18", "cap": "42015", "stato": 1, "citta": "Correggio", "provincia": "RE", "descrizione": "Sede legale"}}, {"pk": 11, "model": "anagrafe.sede", "fields": {"anagrafica": 1, "legale": false, "email": "<EMAIL>", "indirizzo": "Via Sani 9", "cap": "42100", "stato": 1, "citta": "Reggio Emilia", "provincia": "RE", "descrizione": "Sede operativa"}}, {"pk": 2, "model": "anagrafe.anagrafica", "fields": {"ragione_sociale": "Allnet", "alias": "Allnet", "cliente": false, "fornitore": true, "rivenditore": false, "mastervoice": false, "mastercom": false, "partita_iva": "012345678911", "url": "http://www.allnet-italia.it/", "attivo": true}}, {"pk": 20, "model": "anagrafe.sede", "fields": {"anagrafica": 2, "legale": true, "descrizione": "Sede legale"}}, {"pk": 3, "model": "anagrafe.anagrafica", "fields": {"ragione_sociale": "Astel", "alias": "Astel", "cliente": false, "fornitore": false, "rivenditore": true, "mastervoice": false, "mastercom": false, "url": "", "partita_iva": "012345678912", "attivo": true}}, {"pk": 30, "model": "anagrafe.sede", "fields": {"anagrafica": 3, "legale": true, "descrizione": "Sede legale"}}, {"pk": 4, "model": "anagrafe.anagrafica", "fields": {"titolo": "Spett.", "ragione_sociale": "<PERSON>", "alias": "<PERSON>", "indirizzo": "Via Leopardi 1", "cap": "20100", "stato": 1, "citta": "Milano", "provincia": "MI", "cliente": true, "fornitore": false, "rivenditore": false, "mastervoice": true, "mastercom": true, "url": "", "partita_iva": "012345678913", "attivo": true}}, {"pk": 40, "model": "anagrafe.sede", "fields": {"anagrafica": 4, "legale": true, "indirizzo": "Via Leopardi 1", "cap": "20100", "stato": 1, "citta": "Milano", "provincia": "MI", "descrizione": "Sede legale"}}, {"pk": 41, "model": "anagrafe.sede", "fields": {"anagrafica": 4, "legale": false, "indirizzo": "Via D'Annunzio 999", "cap": "00100", "stato": 1, "citta": "Roma", "provincia": "RM", "descrizione": "Sede di Roma"}}, {"pk": 42, "model": "anagrafe.sede", "fields": {"anagrafica": 4, "legale": false, "indirizzo": "Via podrov, 888", "cap": "Z5461", "stato": 201, "citta": "Tirana", "provincia": "", "descrizione": "Sede di Tirana"}}, {"pk": 5, "model": "anagrafe.anagrafica", "fields": {"ragione_sociale": "Beta", "alias": "Beta", "iban": "***************************", "abi": "08986", "cab": "63341", "cliente": true, "fornitore": false, "rivenditore": false, "mastervoice": true, "mastercom": false, "url": "", "partita_iva": "012345678914", "attivo": true}}, {"pk": 50, "model": "anagrafe.sede", "fields": {"anagrafica": 5, "legale": true, "descrizione": "Sede legale"}}, {"pk": 51, "model": "anagrafe.sede", "fields": {"anagrafica": 5, "legale": false, "indirizzo": "Via Mazzini 12", "cap": "00100", "citta": "Roma", "provincia": "RM", "descrizione": "Sede di Roma"}}, {"pk": 99, "model": "anagrafe.anagrafica", "fields": {"ragione_sociale": "Vecchiume", "alias": "Vecchiume", "cliente": true, "fornitore": false, "rivenditore": false, "mastervoice": true, "mastercom": false, "url": "", "partita_iva": "012345678915", "attivo": false}}, {"pk": 990, "model": "anagrafe.sede", "fields": {"anagrafica": 99, "legale": true, "descrizione": "Sede legale"}}]