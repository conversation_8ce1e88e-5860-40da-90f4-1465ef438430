[{"pk": 1, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Italy", "descrizione": "Italia", "area": "11"}}, {"pk": 201, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Albania", "descrizione": "Albania", "area": "12"}}, {"pk": 202, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Andorra", "descrizione": "Andorra", "area": "13"}}, {"pk": 203, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Austria", "descrizione": "Austria", "area": "11"}}, {"pk": 206, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Belgium", "descrizione": "Belgio", "area": "11"}}, {"pk": 209, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Bulgaria", "descrizione": "Bulgaria", "area": "11"}}, {"pk": 212, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Denmark", "descrizione": "Danimarca", "area": "11"}}, {"pk": 214, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Finland", "descrizione": "Finlandia", "area": "11"}}, {"pk": 215, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "France", "descrizione": "Francia", "area": "11"}}, {"pk": 216, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Germany", "descrizione": "Germania", "area": "11"}}, {"pk": 219, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "United Kingdom", "descrizione": "Regno Unito", "area": "11"}}, {"pk": 220, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Greece", "descrizione": "Grecia", "area": "11"}}, {"pk": 221, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Ireland", "descrizione": "Irlanda", "area": "11"}}, {"pk": 223, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Iceland", "descrizione": "Islanda", "area": "13"}}, {"pk": 225, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Liechtenstein", "descrizione": "Liechtenstein", "area": "13"}}, {"pk": 226, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Luxembourg", "descrizione": "Lussemburgo", "area": "11"}}, {"pk": 227, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Malta", "descrizione": "Malta", "area": "11"}}, {"pk": 229, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Monaco", "descrizione": "Monaco", "area": "13"}}, {"pk": 231, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Norway", "descrizione": "Norvegia", "area": "13"}}, {"pk": 232, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Netherlands", "descrizione": "Olanda <PERSON> <PERSON><PERSON>", "area": "11"}}, {"pk": 233, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Poland", "descrizione": "Polonia", "area": "11"}}, {"pk": 234, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Portugal", "descrizione": "Portogallo", "area": "11"}}, {"pk": 235, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Romania", "descrizione": "Romania", "area": "11"}}, {"pk": 236, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "San Marino", "descrizione": "San Marino", "area": "13"}}, {"pk": 239, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Spain", "descrizione": "Spagna", "area": "11"}}, {"pk": 240, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Sweden", "descrizione": "Svezia", "area": "11"}}, {"pk": 241, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Switzerland", "descrizione": "Svizzera", "area": "13"}}, {"pk": 243, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Ukraine", "descrizione": "Ucraina", "area": "12"}}, {"pk": 244, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Hungary", "descrizione": "Ungheria", "area": "11"}}, {"pk": 245, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Russian Federation", "descrizione": "Russa, Federazione", "area": "12"}}, {"pk": 246, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Vatican City State", "descrizione": "Stato della Città del Vaticano", "area": "13"}}, {"pk": 247, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Estonia", "descrizione": "Estonia", "area": "11"}}, {"pk": 248, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Latvia", "descrizione": "<PERSON><PERSON><PERSON>", "area": "11"}}, {"pk": 249, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Lithuania", "descrizione": "Lituania", "area": "11"}}, {"pk": 250, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Croatia", "descrizione": "Croazia", "area": "12"}}, {"pk": 251, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Slovenia", "descrizione": "Slovenia", "area": "11"}}, {"pk": 252, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Bosnia and Herzegovina", "descrizione": "Bosnia-Erzegovina", "area": "12"}}, {"pk": 253, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Macedonia (FYROM)", "descrizione": "Macedonia, Repubblica di", "area": "12"}}, {"pk": 254, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Moldova, Republic of", "descrizione": "Moldova", "area": "12"}}, {"pk": 255, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Slovakia", "descrizione": "Slovacchia", "area": "11"}}, {"pk": 256, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Belarus", "descrizione": "Bielorussia", "area": "12"}}, {"pk": 257, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Czech Republic", "descrizione": "Ceca, Repubblica", "area": "11"}}, {"pk": 270, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Montenegro", "descrizione": "Montenegro", "area": "12"}}, {"pk": 271, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Serbia", "descrizione": "Serbia, Repubblica di", "area": "12"}}, {"pk": 272, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Kosovo", "descrizione": "Kosovo", "area": "12"}}, {"pk": 301, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Afghanistan", "descrizione": "Afghanistan", "area": "32"}}, {"pk": 302, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Saudi Arabia", "descrizione": "Arabia Saudita", "area": "31"}}, {"pk": 304, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Bahrain", "descrizione": "<PERSON><PERSON><PERSON>", "area": "31"}}, {"pk": 305, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Bangladesh", "descrizione": "Bangladesh", "area": "32"}}, {"pk": 306, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Bhutan", "descrizione": "Bhutan", "area": "32"}}, {"pk": 307, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Myanmar", "descrizione": "Myanmar (ex Birmania)", "area": "33"}}, {"pk": 309, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Brunei Darussalam", "descrizione": "Brunei", "area": "33"}}, {"pk": 310, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Cambodia", "descrizione": "Cambogia", "area": "33"}}, {"pk": 311, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Sri Lanka", "descrizione": "Sri Lanka (ex Ceylon)", "area": "32"}}, {"pk": 314, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "China", "descrizione": "Cinese, Repubblica Popolare", "area": "33"}}, {"pk": 315, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Cyprus", "descrizione": "Cipro", "area": "11"}}, {"pk": 319, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Korea, Democratic People's Republic of", "descrizione": "Corea, Repubblica Popolare Democratica (Corea del Nord)", "area": "33"}}, {"pk": 320, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Korea, Republic of", "descrizione": "Corea, Repubblica (Corea del Sud)", "area": "33"}}, {"pk": 322, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "United Arab Emirates", "descrizione": "Emirati Arabi Uniti", "area": "31"}}, {"pk": 323, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Philippines", "descrizione": "<PERSON><PERSON><PERSON>", "area": "33"}}, {"pk": 324, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Palestinian Territory, Occupied", "descrizione": "Territori dell'Autonomia Palestinese", "area": "31"}}, {"pk": 326, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Japan", "descrizione": "Giappone", "area": "33"}}, {"pk": 327, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Jordan", "descrizione": "Giordania", "area": "31"}}, {"pk": 330, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "India", "descrizione": "India", "area": "32"}}, {"pk": 331, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Indonesia", "descrizione": "Indonesia", "area": "33"}}, {"pk": 332, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Iran, Islamic Republic of", "descrizione": "Iran, Repubblica Islamica del", "area": "31"}}, {"pk": 333, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Iraq", "descrizione": "Iraq", "area": "31"}}, {"pk": 334, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Israel", "descrizione": "<PERSON><PERSON>", "area": "31"}}, {"pk": 335, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Kuwait", "descrizione": "Kuwait", "area": "31"}}, {"pk": 336, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Lao People's Democratic Republic", "descrizione": "Laos", "area": "33"}}, {"pk": 337, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Lebanon", "descrizione": "Libano", "area": "31"}}, {"pk": 338, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "East Timor", "descrizione": "Timor Orientale", "area": "33"}}, {"pk": 339, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Maldives", "descrizione": "Maldive", "area": "32"}}, {"pk": 340, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Malaysia", "descrizione": "Malaysia", "area": "33"}}, {"pk": 341, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Mongolia", "descrizione": "Mongolia", "area": "33"}}, {"pk": 342, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Nepal", "descrizione": "Nepal", "area": "32"}}, {"pk": 343, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Oman", "descrizione": "Oman", "area": "31"}}, {"pk": 344, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Pakistan", "descrizione": "Pakistan", "area": "32"}}, {"pk": 345, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Qatar", "descrizione": "Qatar", "area": "31"}}, {"pk": 346, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Singapore", "descrizione": "Singapore", "area": "33"}}, {"pk": 348, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Syrian Arab Republic", "descrizione": "Siria", "area": "31"}}, {"pk": 349, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Thailand", "descrizione": "Thailandia", "area": "33"}}, {"pk": 351, "model": "anagrafe.stato", "fields": {"continente": "1", "descrizione_inglese": "Turkey", "descrizione": "Tu<PERSON><PERSON>", "area": "12"}}, {"pk": 353, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Viet Nam", "descrizione": "Vietnam", "area": "33"}}, {"pk": 354, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Yemen", "descrizione": "Yemen", "area": "31"}}, {"pk": 356, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Kazakhstan", "descrizione": "Kazakhstan", "area": "32"}}, {"pk": 357, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Uzbekistan", "descrizione": "Uzbekistan", "area": "32"}}, {"pk": 358, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Armenia", "descrizione": "Armenia", "area": "31"}}, {"pk": 359, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Azerbaijan", "descrizione": "Azerbaigian", "area": "31"}}, {"pk": 360, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Georgia", "descrizione": "Georgia", "area": "31"}}, {"pk": 361, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Kyrgyzstan", "descrizione": "Kirghizistan", "area": "32"}}, {"pk": 362, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Tajikistan", "descrizione": "Tagikistan", "area": "32"}}, {"pk": 363, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Taiwan, Province of China", "descrizione": "Taiwan (ex Formosa)", "area": "33"}}, {"pk": 364, "model": "anagrafe.stato", "fields": {"continente": "3", "descrizione_inglese": "Turkmenistan", "descrizione": "Turkmenistan", "area": "32"}}, {"pk": 401, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Algeria", "descrizione": "Algeria", "area": "21"}}, {"pk": 402, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Angola", "descrizione": "Angola", "area": "24"}}, {"pk": 404, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Côte D'Ivoire", "descrizione": "Costa d'Avorio", "area": "22"}}, {"pk": 406, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Benin", "descrizione": "Benin (ex Dahomey)", "area": "22"}}, {"pk": 408, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Botswana", "descrizione": "Botswana", "area": "24"}}, {"pk": 409, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Burkina Faso", "descrizione": "Burkina Faso (ex Alto Volta)", "area": "22"}}, {"pk": 410, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Burundi", "descrizione": "Burundi", "area": "23"}}, {"pk": 411, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Cameroon", "descrizione": "<PERSON><PERSON>", "area": "24"}}, {"pk": 413, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Cape Verde", "descrizione": "Capo Verde", "area": "22"}}, {"pk": 414, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Central African Republic", "descrizione": "Centrafricana, Repubblica", "area": "24"}}, {"pk": 415, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Chad", "descrizione": "Ciad", "area": "24"}}, {"pk": 417, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Comoros", "descrizione": "Comore", "area": "23"}}, {"pk": 418, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Congo", "descrizione": "Congo (Repubblica del)", "area": "24"}}, {"pk": 419, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Egypt", "descrizione": "Egitto", "area": "21"}}, {"pk": 420, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Ethiopia", "descrizione": "Etiopia", "area": "23"}}, {"pk": 421, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Gabon", "descrizione": "Gabon", "area": "24"}}, {"pk": 422, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Gambia", "descrizione": "Gambia", "area": "22"}}, {"pk": 423, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Ghana", "descrizione": "Ghana", "area": "22"}}, {"pk": 424, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Djibouti", "descrizione": "Gibuti", "area": "23"}}, {"pk": 425, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Guinea", "descrizione": "Guinea", "area": "22"}}, {"pk": 426, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Guinea-Bissau", "descrizione": "Guinea Bissau", "area": "22"}}, {"pk": 427, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Equatorial Guinea", "descrizione": "Guinea Equatoriale", "area": "24"}}, {"pk": 428, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Kenya", "descrizione": "Kenya", "area": "23"}}, {"pk": 429, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Lesotho", "descrizione": "Lesotho", "area": "24"}}, {"pk": 430, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Liberia", "descrizione": "Liberia", "area": "22"}}, {"pk": 431, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Libyan Arab Jam<PERSON>riya", "descrizione": "Libia", "area": "21"}}, {"pk": 432, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Madagascar", "descrizione": "Madagascar", "area": "23"}}, {"pk": 434, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Malawi", "descrizione": "Malawi", "area": "23"}}, {"pk": 435, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Mali", "descrizione": "Mali", "area": "22"}}, {"pk": 436, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Morocco", "descrizione": "Marocco", "area": "21"}}, {"pk": 437, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Mauritania", "descrizione": "Mauritania", "area": "22"}}, {"pk": 438, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Mauritius", "descrizione": "Mauritius", "area": "23"}}, {"pk": 440, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Mozambique", "descrizione": "Mozambico", "area": "23"}}, {"pk": 441, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Namibia", "descrizione": "Namibia", "area": "24"}}, {"pk": 442, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Niger", "descrizione": "Niger", "area": "22"}}, {"pk": 443, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Nigeria", "descrizione": "Nigeria", "area": "22"}}, {"pk": 446, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Rwanda", "descrizione": "<PERSON><PERSON><PERSON>", "area": "23"}}, {"pk": 448, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Sao Tome and Principe", "descrizione": "São Tomé e Principe", "area": "24"}}, {"pk": 449, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Seychelles", "descrizione": "Seychelles", "area": "23"}}, {"pk": 450, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Senegal", "descrizione": "Senegal", "area": "22"}}, {"pk": 451, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Sierra Leone", "descrizione": "Sierra Leone", "area": "22"}}, {"pk": 453, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Somalia", "descrizione": "Somalia", "area": "23"}}, {"pk": 454, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "South Africa", "descrizione": "Sud Africa", "area": "24"}}, {"pk": 455, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Sudan", "descrizione": "Sudan", "area": "21"}}, {"pk": 456, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Swaziland", "descrizione": "Swaziland", "area": "24"}}, {"pk": 457, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Tanzania, United Republic of", "descrizione": "Tanzania", "area": "23"}}, {"pk": 458, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Togo", "descrizione": "Togo", "area": "22"}}, {"pk": 460, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Tunisia", "descrizione": "Tunisia", "area": "21"}}, {"pk": 461, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Uganda", "descrizione": "Uganda", "area": "23"}}, {"pk": 463, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Congo, The Democratic Republic of The", "descrizione": "Congo, Repubblica democratica del (ex Zaire)", "area": "24"}}, {"pk": 464, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Zambia", "descrizione": "Zambia", "area": "23"}}, {"pk": 465, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Zimbabwe", "descrizione": "Zimbabwe (ex Rhodesia)", "area": "23"}}, {"pk": 466, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "Eritrea", "descrizione": "Eritrea", "area": "23"}}, {"pk": 467, "model": "anagrafe.stato", "fields": {"continente": "2", "descrizione_inglese": "South Sudan, Republic of", "descrizione": "Sud Sudan, Repubblica del", "area": "21"}}, {"pk": 503, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Antigua and Barbuda", "descrizione": "Antigua e Barbuda", "area": "42"}}, {"pk": 505, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Bahamas", "descrizione": "Bahamas", "area": "42"}}, {"pk": 506, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Barbados", "descrizione": "Barbados", "area": "42"}}, {"pk": 507, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Belize", "descrizione": "Belize", "area": "42"}}, {"pk": 509, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Canada", "descrizione": "Canada", "area": "41"}}, {"pk": 513, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Costa Rica", "descrizione": "Costa Rica", "area": "42"}}, {"pk": 514, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Cuba", "descrizione": "Cuba", "area": "42"}}, {"pk": 515, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Dominica", "descrizione": "Dominica", "area": "42"}}, {"pk": 516, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Dominican Republic", "descrizione": "Dominicana, Repubblica", "area": "42"}}, {"pk": 517, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "El Salvador", "descrizione": "El Salvador", "area": "42"}}, {"pk": 518, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Jamaica", "descrizione": "Giamaic<PERSON>", "area": "42"}}, {"pk": 519, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Grenada", "descrizione": "Grenada", "area": "42"}}, {"pk": 523, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Guatemala", "descrizione": "Guatemala", "area": "42"}}, {"pk": 524, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Haiti", "descrizione": "Haiti", "area": "42"}}, {"pk": 525, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Honduras", "descrizione": "Honduras", "area": "42"}}, {"pk": 527, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Mexico", "descrizione": "Messico", "area": "42"}}, {"pk": 529, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Nicaragua", "descrizione": "Nicaragua", "area": "42"}}, {"pk": 530, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Panama", "descrizione": "Panama", "area": "42"}}, {"pk": 532, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Saint Lucia", "descrizione": "Saint Lucia", "area": "42"}}, {"pk": 533, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Saint Vincent and The Grenadines", "descrizione": "Saint Vincent e Grenadine", "area": "42"}}, {"pk": 534, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Saint Kitts and Nevis", "descrizione": "Saint Kitts e Nevis", "area": "42"}}, {"pk": 536, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "United States", "descrizione": "Stati Uniti d'America", "area": "41"}}, {"pk": 602, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Argentina", "descrizione": "Argentina", "area": "42"}}, {"pk": 604, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Bolivia, Plurinational State of", "descrizione": "Bolivia", "area": "42"}}, {"pk": 605, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Brazil", "descrizione": "Brasile", "area": "42"}}, {"pk": 606, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Chile", "descrizione": "Cile", "area": "42"}}, {"pk": 608, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Colombia", "descrizione": "Colombia", "area": "42"}}, {"pk": 609, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Ecuador", "descrizione": "Ecuador", "area": "42"}}, {"pk": 612, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Guyana", "descrizione": "Guyana", "area": "42"}}, {"pk": 614, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Paraguay", "descrizione": "Paraguay", "area": "42"}}, {"pk": 615, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Peru", "descrizione": "<PERSON><PERSON>", "area": "42"}}, {"pk": 616, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Suriname", "descrizione": "Suriname", "area": "42"}}, {"pk": 617, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Trinidad and Tobago", "descrizione": "Trinidad e Tobago", "area": "42"}}, {"pk": 618, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Uruguay", "descrizione": "Uruguay", "area": "42"}}, {"pk": 619, "model": "anagrafe.stato", "fields": {"continente": "4", "descrizione_inglese": "Venezuela, Bolivarian Republic of", "descrizione": "Venezuela", "area": "42"}}, {"pk": 701, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Australia", "descrizione": "Australia", "area": "50"}}, {"pk": 703, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Fiji", "descrizione": "Figi", "area": "50"}}, {"pk": 708, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Kiribati", "descrizione": "Kiribati", "area": "50"}}, {"pk": 712, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Marshall Islands", "descrizione": "Marshall, <PERSON><PERSON>", "area": "50"}}, {"pk": 713, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Micronesia, Federated States of", "descrizione": "Micronesia, Stati Federati", "area": "50"}}, {"pk": 715, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Nauru", "descrizione": "Nauru", "area": "50"}}, {"pk": 719, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "New Zealand", "descrizione": "Nuova Zelanda", "area": "50"}}, {"pk": 720, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "<PERSON><PERSON>", "descrizione": "<PERSON><PERSON>", "area": "50"}}, {"pk": 721, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Papua New Guinea", "descrizione": "Papua Nuova Guinea", "area": "50"}}, {"pk": 725, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Solomon Islands", "descrizione": "Salomone, Isole", "area": "50"}}, {"pk": 727, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Samoa", "descrizione": "Samoa", "area": "50"}}, {"pk": 730, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Tonga", "descrizione": "Tonga", "area": "50"}}, {"pk": 731, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Tuvalu", "descrizione": "Tuvalu", "area": "50"}}, {"pk": 732, "model": "anagrafe.stato", "fields": {"continente": "5", "descrizione_inglese": "Vanuatu", "descrizione": "Vanuatu", "area": "50"}}, {"pk": 999, "model": "anagrafe.stato", "fields": {"continente": "6", "descrizione_inglese": "Stateless", "descrizione": "Apolide", "area": "60"}}]