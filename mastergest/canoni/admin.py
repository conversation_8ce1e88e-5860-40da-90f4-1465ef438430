import csv
from decimal import Decimal
from datetime import datetime

from django.contrib import admin
from django.contrib.admin.utils import unquote
from django.contrib.admin import SimpleListFilter
from django.conf.urls import url
from django.contrib import messages
from django.http import HttpResponse
from django.http import Http404, HttpResponseRedirect
from django.template import Context
from django.template.defaultfilters import slugify
from django.utils import timezone

from webodt.shortcuts import render_to_response as webodt_render_to_response

from import_export.formats import base_formats
from import_export.admin import ExportActionMixin

from mastergest.canoni import forms
from mastergest.canoni.models import ScadenzaCanone, Canone, LineaDati
from mastergest.attachments.admin import AttachmentInlinesNoAdd
from mastergest.attachments.admin import AttachmentInlinesAddOnly
from mastergest.canoni.views import UploadView, VerificaView
from mastergest.canoni.utils import fattura_elenco_scadenze
from mastergest.utils.admin import MastergestAdmin
from mastergest.canoni.resources import DescrizioneFatturaResource

SIMBOLO_EURO = '\u20ac'


class ScadenzaCanoneInlineReadOnly(admin.TabularInline):
    fields = ('data_scadenza', 'importo', 'descrizione', 'fatturato')
    model = ScadenzaCanone
    readonly_fields = (
        'data_scadenza', 'importo', 'descrizione', 'fatturato', 'fattura', 'note'
    )
    extra = 0
    can_delete = False
    max_num = 0

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False


class CanoneInline(admin.TabularInline):
    fields = (
        'get_link', 'tipologia', 'categoria_canone', 'data_inizio',
        'data_fine', 'tipo_rinnovo', 'numero_rate_rinnovo', 'numero_rate',
        'importo_rata', 'periodicita', 'stato'
    )
    readonly_fields = ('get_link',)
    model = Canone
    form = forms.CanoneInlineForm
    extra = 0
    suit_classes = 'suit-tab suit-tab-canoni'


class CanoneStackedInline(admin.StackedInline):
    readonly_fields = ('get_link',)
    model = Canone
    form = forms.CanoneInlineForm
    extra = 0
    suit_classes = 'suit-tab suit-tab-canoni'
    fieldsets = (
        (
            None, dict(
                fields=(
                    ('tipologia', 'categoria_canone', 'get_link',),
                    ('data_inizio',),
                    ('data_fine',),
                    ('periodicita', 'anticipato', 'con_ratino'),
                    ('numero_rate', 'importo_rata',),
                    ('numero_rate_rinnovo', 'tipo_rinnovo',),
                    ('stato',),
                ))
        ),
    )


class StatoCanoneFilter(SimpleListFilter):
    title = 'Stato'
    parameter_name = 'stato_canone'

    def lookups(self, request, model_admin):
        return (
            ('attivo', 'attivo'),
            ('rinnovato', 'rinnovato'),
            ('disdetto', 'disdetto'),
            ('trasferito', 'trasferito'),
            ('sospeso', 'sospeso'),
        )

    def queryset(self, request, queryset):
        if self.value() is not None:
            if self.value() == 'attivo':
                return queryset.filter(canone_successivo__isnull=True).exclude(stato='disdetto').exclude(stato='sospeso').exclude(stato='trasferito')
            if self.value() == 'rinnovato':
                return queryset.filter(canone_successivo__isnull=False).exclude(stato='disdetto').exclude(stato='sospeso').exclude(stato='trasferito')
            else:
                return queryset.filter(stato=self.value())
        else:
            return queryset


class LineaDatiInline(admin.TabularInline):
    model = LineaDati
    readonly_fields = ('variazione', 'ultimo_aggiornamento')
    suit_classes = 'suit-tab suit-tab-lineedati'


class CanoneAdmin(MastergestAdmin):
    list_display = (
        'id', 'get_sede_cliente_link', 'tipologia', 'categoria_canone',
        'get_data_inizio_display', 'get_data_fine_display', 'periodicita',
        'numero_rate', 'importo_rata', 'get_rate_fatturate', 'get_rate_pagate',
        'get_stato_canone', 'note',
    )
    list_filter = (
        StatoCanoneFilter, 'tipologia', 'categoria_canone', 'periodicita', 'contratto_ricevuto',
        'contratto__sede__anagrafica__scuola', 'contratto__sede__anagrafica__azienda',
        'contratto__sede__anagrafica__attivo',
    )
    search_fields = (
        'contratto__sede__anagrafica__ragione_sociale',
        'contratto__sede__anagrafica__alias', 'note', 'stato', 'id',
        'contratto__sede__descrizione'
    )
    date_hierarchy = 'data_fine'
    readonly_fields = (
        'link_canone_successivo', 'numero_contratto_vecchio',
        'get_sede_cliente', 'get_totale_costi', 'get_utile', 'get_link_contratto'
    )
    inlines = [AttachmentInlinesAddOnly, AttachmentInlinesNoAdd, LineaDatiInline]
    form = forms.CanoneForm
    actions = ['export_to_csv', ]
    list_max_show_all = 10000
    fieldsets = (
        (
            None, dict(
                classes=('suit-tab suit-tab-dati_canone', ),
                fields=(
                    ('contratto', 'get_link_contratto'),
                    'get_sede_cliente',
                    'numero_contratto_vecchio',
                    'tipologia',
                    'categoria_canone',
                    ('periodicita', 'anticipato', 'con_ratino'),
                    'data_inizio',
                    'data_fine',
                    ('stato', 'bloccato'),
                    'contratto_ricevuto',
                    ('canone_successivo', 'link_canone_successivo',),
                ))
        ),
        (
            'RINNOVO', dict(
                classes=('suit-tab suit-tab-dati_canone', ),
                fields=(
                    ('tipo_rinnovo', 'numero_rate_rinnovo'),
                ))
        ),
        (
            'RATE', dict(
                classes=('suit-tab suit-tab-dati_canone', ),
                fields=(
                    ('importo_rata', 'numero_rate'),
                    'get_totale_costi',
                    'get_utile',
                    'descrizione_aggiuntiva_fattura',
                ))
        ),
        (
            None, dict(
                classes=('suit-tab suit-tab-dati_canone', ),
                fields=(
                    ('note',),
                ))
        ),
    )
    suit_form_tabs = (
        ('dati_canone', 'Dati Canone'),
        ('scadenze', 'Rate/Scadenze'),
        ('lineedati', 'Linee Dati'),
        ('allegati', 'Allegati'),)

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/rinnova/$',
                self.admin_site.admin_view(self.rinnovacanone),
                name='%s_%s_rinnova' % info
            ),
            url(
                r'^(.+)/aggiornascadenze/$',
                self.admin_site.admin_view(self.aggiornascadenze),
                name='%s_%s_aggiornascadenze' % info
            ),
            url(
                r'^(.+)/stampacontratto/$',
                self.admin_site.admin_view(self.stampacontratto),
                name='%s_%s_stampacontratto' % info
            ),
            url(
                r'^(.+)/stampafax/$',
                self.admin_site.admin_view(self.stampafax),
                name='%s_%s_stampafax' % info
            ),
        ]
        url_patterns += super(CanoneAdmin, self).get_urls()
        return url_patterns

    def stampacontratto(self, request, object_id):
        canone = self.get_object(request, unquote(object_id))
        if not canone:
            raise Http404()
        context = Context(
            dict(
                canone=canone, contratto=canone.contratto, sede=canone.get_sede_cliente()
            )
        )
        ragione_sociale = slugify(canone.get_sede_cliente().anagrafica).upper()
        filename = 'contratto_%s_%s.odt' % (canone.contratto.id, ragione_sociale)
        template = canone.get_template_stampa_contratto()
        if template:
            return webodt_render_to_response(
                template, context_instance=context, filename=filename
            )
        msg = "Template per Contratto del tipo canone corrente non presente!"
        messages.error(request, msg)
        return HttpResponseRedirect('/canoni/canone/%s' % (canone.pk))

    def stampafax(self, request, object_id):
        canone = self.get_object(request, unquote(object_id))
        if not canone:
            raise Http404()
        context = Context(
            dict(
                canone=canone, contratto=canone.contratto, sede=canone.get_sede_cliente(), data_odierna=timezone.now()
            )
        )
        ragione_sociale = slugify(canone.get_sede_cliente().anagrafica).upper()
        filename = 'fax_%s_%s.odt' % (canone.contratto.id, ragione_sociale)
        template = canone.get_template_stampa_fax()
        if template:
            return webodt_render_to_response(
                template, context_instance=context, filename=filename
            )
        msg = "Template per Fax del tipo canone corrente non presente!"
        messages.error(request, msg)
        return HttpResponseRedirect('/canoni/canone/%s' % (canone.pk))

    def rinnovacanone(self, request, object_id):
        canone = Canone.objects.get(pk=unquote(object_id))
        if not canone:
            raise Http404()
        nuovo_canone = canone.rinnova()
        if nuovo_canone:
            if canone.contratto.gestione_azienda == 'mastertraining':
                return HttpResponseRedirect('/canoni/canonemastertraining/%s' % (nuovo_canone.pk))
            if canone.contratto.gestione_azienda == 'magister':
                return HttpResponseRedirect('/canoni/canonemagister/%s' % (nuovo_canone.pk))
        else:
            raise Http404()

    def aggiornascadenze(self, request, object_id):
        canone = Canone.objects.get(pk=unquote(object_id))
        if not canone:
            raise Http404()
        if canone.bloccato:
            msg = "Attenzione! Il canone corrente e\' stato bloccato per la presenza di modifiche manuali. Generazione di scadenze non eseguita!"
            messages.error(request, msg)
        else:
            canone.aggiorna_scadenze()
            msg = "Scadenze canone aggiornate!"
            messages.success(request, msg)
        return HttpResponseRedirect('/canoni/canone/%s' % (canone.pk))

    def change_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, unquote(object_id))
        if obj:
            extra_context = extra_context or dict()
            elenco_scadenze = ScadenzaCanone.objects.filter(canone=obj)
            extra_context['lista_scadenze'] = elenco_scadenze
        return super(CanoneAdmin, self).change_view(request, object_id, extra_context=extra_context)

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_canoni.csv"'
        writer = csv.writer(response, delimiter=';')
        writer.writerow([
            'id',
            'sede cliente',
            'tipologia',
            'categoria_canone',
            'inizio',
            'fine',
            'periodicita',
            'numero rate',
            'importo rata',
            'fatturate',
            'pagate',
            'stato',
            'note'
        ])
        for canone in queryset:
            writer.writerow([
                canone.id,
                canone.get_sede_cliente(),
                canone.tipologia,
                canone.categoria_canone,
                canone.data_inizio.strftime("%d/%m/%Y"),
                canone.data_fine.strftime("%d/%m/%Y"),
                canone.get_periodicita_display(),
                canone.numero_rate,
                str(canone.importo_rata).replace('.', ','),
                canone.get_rate_fatturate(),
                canone.get_rate_pagate(),
                canone.get_stato_canone_csv(),
                canone.note
            ])
        return response
    export_to_csv.short_description = 'Esportazione Canoni (CSV)'

    def lookup_allowed(self, key, value):
        if key in ('contratto__sede__id__exact', ):
            return True
        return super(CanoneAdmin, self).lookup_allowed(key, value)

    def save_model(self, request, obj, form, change):
        if obj.data_fine:
            data_fine_calcolata = obj.get_data_fine_calcolata()
            if not obj.data_fine == data_fine_calcolata:
                messages.error(
                    request,
                    'Attenzione! la data di fine del canone (%s) non coincide con quella calcolata in base al numero di rate: %s' % (
                        obj.data_fine, data_fine_calcolata
                    )
                )
        return super(CanoneAdmin, self).save_model(request, obj, form, change)


class CanoneInScadenzaAdmin(CanoneAdmin):
    list_display = (
        'get_sede_cliente', 'tipologia', 'categoria_canone',
        'get_data_inizio_display', 'get_data_fine_display', 'periodicita',
        'anticipato', 'numero_rate', 'importo_rata', 'get_rate_fatturate',
        'get_rate_pagate', 'note', 'get_gestione_azienda',
    )
    list_filter = (
        'tipologia', 'categoria_canone', 'periodicita', 'contratto_ricevuto',
        'contratto__sede__anagrafica__scuola', 'contratto__gestione_azienda',
    )
    actions = ['rinnova_tutti', ]

    def rinnova_tutti(self, request, queryset):
        no_rinnovo = queryset.filter(canone_successivo__isnull=False)
        if no_rinnovo.count() > 0:
            msg = "Sono presenti Canoni gia' rinnovati."
            messages.error(request, msg)
            return
        for canone in queryset:
            canone.rinnova()
        msg = "Rinnovati %d canoni." % queryset.count()
        messages.success(request, msg)
    rinnova_tutti.short_description = 'Rinnova canoni selezionati'


class ScadenzaCanoneAdmin(MastergestAdmin):
    list_display = (
        'sede_cliente', 'data_scadenza', 'importo', 'descrizione',
        'fatturato', 'get_link_fattura', 'note', 'get_gestione_azienda'
    )
    list_filter = ('fatturato', 'data_scadenza', 'canone__contratto__gestione_azienda')
    search_fields = (
        'sede_cliente__anagrafica__ragione_sociale',
        'cliente__alias', 'descrizione',
        'note', 'sede_cliente__descrizione',
        'descrizione_fattura',
    )
    date_hierarchy = 'data_scadenza'
    list_max_show_all = 10000
    readonly_fields = ('get_link_fattura', 'ultima', 'get_link_canone')
    fieldsets = (
        (None, {'fields': (
            ('canone', 'get_link_canone',),
            'data_scadenza',
            'descrizione',
            'importo',
            'data_inizio',
            'data_fine',
            ('fatturato', 'get_link_fattura',),
            'ultima',
            'descrizione_fattura',
            'note',
        )}),
    )
    form = forms.ScadenzaCanoneForm
    actions = [
        'fattura_scadenze_massivo_multiplo',
        'fattura_scadenze_massivo_accorpata',
        'fattura_scadenze_massivo_per_sede',
        'fattura_scadenze_massivo_parametrico',
        'export_to_csv'
    ]

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^(.+)/fattura/$',
                self.admin_site.admin_view(self.fattura_scadenza),
                name='%s_%s_fattura' % info
            ),
        ]
        url_patterns += super(ScadenzaCanoneAdmin, self).get_urls()
        return url_patterns

    def fattura_scadenza(self, request, object_id):
        scadenza = ScadenzaCanone.objects.get(pk=unquote(object_id))
        if not scadenza:
            raise Http404()
        fattura = scadenza.fattura_scadenza()
        if fattura:
            if scadenza.canone.contratto.gestione_azienda == 'mastertraining':
                return HttpResponseRedirect('/fatturazione/fatturamastertraining/%s' % (fattura.pk))
            if scadenza.canone.contratto.gestione_azienda == 'magister':
                return HttpResponseRedirect('/fatturazione/fatturamagister/%s' % (fattura.pk))
        else:
            raise Http404()

    def fattura_scadenze_massivo_multiplo(self, request, queryset):
        for scadenza in queryset:
            scadenza.fattura_scadenza()
            msg = "Fatturata scadenza %s" % scadenza
            messages.success(request, msg)
    fattura_scadenze_massivo_multiplo.short_description = 'Fatt. Scad. Selez. (Fatture multiple)'

    def fattura_scadenze_massivo_accorpata(self, request, queryset):
        cliente_selezionato = None
        for scadenza in queryset:
            if not cliente_selezionato:
                cliente_selezionato = scadenza.get_cliente()
            else:
                if not scadenza.get_cliente() == cliente_selezionato:
                    msg = "Le scadenze selezionate appartengono a clienti diversi!"
                    messages.error(request, msg)
                    return
        nuova_fattura = fattura_elenco_scadenze(queryset)
        if nuova_fattura:
            msg = "Fatturate scadenze selezionate in %s" % nuova_fattura
            messages.success(request, msg)
    fattura_scadenze_massivo_accorpata.short_description = 'Fatt. Scad. Selez. (Fattura accorpata)'

    def fattura_scadenze_massivo_per_sede(self, request, queryset):
        elenco_sedi_clienti = queryset.values('sede_cliente').distinct()
        for id_sede in elenco_sedi_clienti:
            elenco_scadenze_sedi = queryset.filter(sede_cliente_id=id_sede['sede_cliente'])
            nuova_fattura = fattura_elenco_scadenze(elenco_scadenze_sedi)
            if nuova_fattura:
                msg = "Fatturate scadenze selezionate per sede %s in %s" % (nuova_fattura.sede_cliente, nuova_fattura)
                messages.success(request, msg)
    fattura_scadenze_massivo_per_sede.short_description = 'Fatt. Scad. Selez. (Fattura per sede)'

    def fattura_scadenze_massivo_parametrico(self, request, queryset):
        elenco_clienti = queryset.values('cliente').distinct()
        for id_cliente in elenco_clienti:
            elenco_scadenze_cliente = queryset.filter(cliente_id=id_cliente['cliente'])
            if elenco_scadenze_cliente:
                cliente = elenco_scadenze_cliente[0].cliente
                totale_scadenze = Decimal('0.00')
                for scadenza in elenco_scadenze_cliente:
                    totale_scadenze += scadenza.importo
                if totale_scadenze < cliente.importo_minimo_fattura:
                    msg = 'Le scadenze selezionate per il cliente %s non raggiungono la fatturazione minima (%s %s)' % (
                        cliente, cliente.importo_minimo_fattura, SIMBOLO_EURO
                    )
                    messages.warning(request, msg)
                else:
                    if cliente.raggruppa_fatture_canoni_per_sede:
                        elenco_sedi_clienti = queryset.values('sede_cliente').distinct()
                        for id_sede in elenco_sedi_clienti:
                            elenco_scadenze_sedi = queryset.filter(sede_cliente_id=id_sede['sede_cliente'])
                            if cliente.dividi_fatture_canoni_competenza:
                                elenco_mesi_competenza = elenco_scadenze_sedi.dates('data_inizio', 'month')
                                for data_mese in elenco_mesi_competenza:
                                    scadenze_mese = elenco_scadenze_sedi.filter(data_inizio__month=data_mese.month)
                                    nuova_fattura = fattura_elenco_scadenze(scadenze_mese)
                                    if nuova_fattura:
                                        msg = 'Fatturate scadenze cliente per sede %s in %s' % (nuova_fattura.sede_cliente, nuova_fattura)
                                        messages.success(request, msg)
                            else:
                                nuova_fattura = fattura_elenco_scadenze(elenco_scadenze_sedi)
                                if nuova_fattura:
                                    msg = 'Fatturate scadenze cliente per sede %s in %s' % (nuova_fattura.sede_cliente, nuova_fattura)
                                    messages.success(request, msg)
                    elif cliente.raggruppa_fatture_canoni_sedi:
                        if cliente.dividi_fatture_canoni_competenza:
                            elenco_mesi_competenza = elenco_scadenze_cliente.dates('data_inizio', 'month')
                            for data_mese in elenco_mesi_competenza:
                                scadenze_mese = elenco_scadenze_cliente.filter(data_inizio__month=data_mese.month)
                                nuova_fattura = fattura_elenco_scadenze(scadenze_mese)
                                if nuova_fattura:
                                    msg = 'Fatturate scadenze cliente %s in %s' % (cliente, nuova_fattura)
                                    messages.success(request, msg)
                        else:
                            nuova_fattura = fattura_elenco_scadenze(elenco_scadenze_cliente)
                            if nuova_fattura:
                                msg = 'Fatturate scadenze cliente %s in %s' % (cliente, nuova_fattura)
                                messages.success(request, msg)
                    else:
                        for scadenza in elenco_scadenze_cliente:
                            scadenza.fattura_scadenza()
                            msg = "Fatturata scadenza %s" % scadenza
                            messages.success(request, msg)
    fattura_scadenze_massivo_parametrico.short_description = 'Fatt. Scad. Selez. (Parametri cliente)'

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment;filename="export_scadenze.csv"'
        writer = csv.writer(response, delimiter=';')
        writer.writerow([
            'canone',
            'tipologia',
            'categoria',
            'sede cliente',
            'data scadenza',
            'periodicita',
            'descrizione',
            'data inizio competenza',
            'data fine competenza',
            'importo',
            'descrizione per fattura',
            'note'
        ])
        for scadenza in queryset:
            writer.writerow([
                scadenza.canone,
                scadenza.canone.tipologia,
                scadenza.canone.categoria_canone,
                scadenza.canone.get_sede_cliente(),
                scadenza.data_scadenza.strftime("%d/%m/%Y"),
                scadenza.canone.get_periodicita_display(),
                scadenza.descrizione,
                scadenza.data_inizio.strftime("%d/%m/%Y"),
                scadenza.data_fine.strftime("%d/%m/%Y"),
                str(scadenza.importo).replace('.', ','),
                scadenza.descrizione_fattura,
                scadenza.note
            ])
        return response
    export_to_csv.short_description = 'Esportazione Scadenze (CSV)'


class LimiteDataScadenzaFilter(SimpleListFilter):
    title = 'Limite Data Scadenza'
    parameter_name = 'limite_data_scadenza'

    def lookups(self, request, model_admin):
        return (
            ('fine_mese_corrente', 'Entro Fine mese corrente'),
            ('fine_mese_precedente', 'Entro Fine mese precedente'),
            ('fine_2_mesi_precedenti', 'Entro Fine 2 mesi precedenti'),
        )

    def queryset(self, request, queryset):
        data_corrente = timezone.now()
        mese_corrente = data_corrente.month
        anno_corrente = data_corrente.year
        if self.value() is not None:
            if self.value() == 'fine_mese_corrente':
                if mese_corrente == 12:
                    primo_mese = datetime(year=anno_corrente + 1, month=1, day=1)
                else:
                    primo_mese = datetime(year=anno_corrente, month=mese_corrente + 1, day=1)
            elif self.value() == 'fine_mese_precedente':
                primo_mese = datetime(year=anno_corrente, month=mese_corrente, day=1)
            elif self.value() == 'fine_2_mesi_precedenti':
                if mese_corrente == 1:
                    primo_mese = datetime(year=anno_corrente - 1, month=12, day=1)
                else:
                    primo_mese = datetime(year=anno_corrente, month=mese_corrente - 1, day=1)
            return queryset.filter(data_scadenza__lt=primo_mese)
        else:
            return queryset


class MeseCompetenzaFilter(SimpleListFilter):
    title = 'Mese Competenza'
    parameter_name = 'mese_competenza'

    def lookups(self, request, model_admin):
        return (
            (1, 'Gennaio'),
            (2, 'Febbraio'),
            (3, 'Marzo'),
            (4, 'Aprile'),
            (5, 'Maggio'),
            (6, 'Giugno'),
            (7, 'Luglio'),
            (8, 'Agosto'),
            (9, 'Settembre'),
            (10, 'Ottobre'),
            (11, 'Novembre'),
            (12, 'Dicembre'),
        )

    def queryset(self, request, queryset):
        if self.value() is not None:
            return queryset.filter(data_inizio__month=self.value())
        else:
            return queryset


class AnnoCompetenzaFilter(SimpleListFilter):
    title = 'Anno Competenza'
    parameter_name = 'anno_competenza'

    def lookups(self, request, model_admin):
        anno_corrente = timezone.now().year
        elenco_anni = []
        for anno in range(anno_corrente - 6, anno_corrente + 2):
            elenco_anni.append((anno, '%s' % anno))
        return elenco_anni

    def queryset(self, request, queryset):
        if self.value() is not None:
            return queryset.filter(data_inizio__year=self.value())
        else:
            return queryset


class ScadenzaDaFatturareAdmin(ScadenzaCanoneAdmin):
    list_display = (
        'data_scadenza', 'get_sede_filtro_link', 'importo',
        'descrizione_fattura', 'note', 'data_inizio', 'get_gestione_azienda'
    )
    list_filter = (
        LimiteDataScadenzaFilter, 'fatturato', 'data_scadenza',
        'sede_cliente__anagrafica__mastercom',
        'sede_cliente__anagrafica__mastervoice',
        'sede_cliente__anagrafica__magister',
        MeseCompetenzaFilter, AnnoCompetenzaFilter,
        'canone__contratto__gestione_azienda',
    )
    readonly_fields = (
        'get_link_fattura', 'ultima', 'canone', 'descrizione',
        'importo', 'data_scadenza', 'data_fine', 'fatturato', 'data_inizio',
        'get_link_fattura', 'descrizione_fattura', 'note', 'get_link_canone'
    )
    list_per_page = 50

    def lookup_allowed(self, key, value):
        if key in ('canone__contratto__sede__id__exact', ):
            return True
        return super(ScadenzaDaFatturareAdmin, self).lookup_allowed(key, value)


class DescrizioneFatturaAdmin(ExportActionMixin, admin.ModelAdmin):
    list_display = (
        'tipologia', 'categoria_canone', 'descrizione', 'piano_conti_gamma',
        'modello_contratto', 'modello_fax'
    )
    list_filter = ('tipologia', 'categoria_canone', )
    search_fields = ('tipologia__nome', 'categoria_canone__nome', 'descrizione')
    form = forms.DescrizioneFatturaForm
    resource_class = DescrizioneFatturaResource
    formats = (
        base_formats.CSV,
        base_formats.XLS,
        base_formats.XLSX,
        base_formats.ODS,
    )
    fieldsets = (
        (None, {'fields': (
            'tipologia',
            'categoria_canone',
            'descrizione',
            'piano_conti_gamma',
            'modello_contratto',
            'modello_fax',
        )}),
    )


class AssociaScadenzaFatturaAdmin(ScadenzaCanoneAdmin):
    list_display = ('sede_cliente', 'data_scadenza', 'importo', 'descrizione_fattura', 'riga_fattura')
    readonly_fields = (
        'get_link_fattura', 'ultima', 'canone', 'descrizione',
        'importo', 'data_scadenza', 'data_fine', 'fatturato',
        'get_link_fattura', 'descrizione_fattura', 'note', 'get_link_canone'
    )
    list_editable = ('riga_fattura', )
    raw_id_fields = ('riga_fattura', )


class TipologiaAdmin(admin.ModelAdmin):
    list_display = ('id', 'nome', )
    search_fields = ('nome', )


class CategoriaAdmin(admin.ModelAdmin):
    list_display = ('id', 'nome', )
    search_fields = ('nome', )


class DatiLineaKpnAdmin(admin.ModelAdmin):
    list_display = (
        'identificativo', 'importo', 'ragione_sociale', 'comune', 'codice_prodotto',
        'riga', 'verificato', 'rateo', 'get_link_canone'
    )
    search_fields = ('ragione_sociale', 'descrizione', 'identificativo', 'td', 'id_linea')
    list_filter = ('cadenza', 'verificato', 'rateo', 'codice_prodotto', )
    list_max_show_all = 10000
    list_per_page = 50

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        url_patterns = [
            url(
                r'^verifica/$',
                self.admin_site.admin_view(VerificaView.as_view()),
                name='canoni_datilineaipkom_verifica'
            ),
            url(
                r'upload/$',
                self.admin_site.admin_view(UploadView.as_view()),
                name='canoni_datilineaipkom_upload'
            ),
        ]
        url_patterns += super(DatiLineaKpnAdmin, self).get_urls()
        return url_patterns
