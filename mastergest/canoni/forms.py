from django import forms
from django.conf import settings

from suit.widgets import SuitDateWidget, NumberInput

from mastergest.contratti.widgets import ContrattoWidget
from mastergest.canoni.widgets import CanoneWidget
from mastergest.canoni import models
from mastergest.utils.widgets import EuroInput
from mastergest.canoni.utils import parse_dati_linea_kpn


class CanoneInlineForm(forms.ModelForm):
    get_link = forms.CharField(required=False)

    class Meta:
        model = models.Canone
        fields = '__all__'
        widgets = dict(
            numero_rate=NumberInput(attrs={'class': 'input-mini'}),
            numero_rate_rinnovo=NumberInput(attrs={'class': 'input-mini'}),
            data_inizio=SuitDateWidget(),
            data_fine=SuitDateWidget(),
            importo_rata=EuroInput(),
        )


class CanoneForm(forms.ModelForm):
    get_utile = forms.<PERSON>r<PERSON><PERSON>(required=False)
    get_totale_costi = forms.Char<PERSON><PERSON>(required=False)
    get_link_contratto = forms.Char<PERSON>ield(required=False)
    link_canone_successivo = forms.CharField(required=False)
    get_sede_cliente = forms.CharField(required=False)

    class Meta:
        model = models.Canone
        fields = '__all__'
        widgets = dict(
            contratto=ContrattoWidget(rel=models.Canone.contratto.field.remote_field),
            canone_successivo=CanoneWidget(rel=models.Canone.canone_successivo.field.remote_field),
            numero_rate=NumberInput(attrs={'class': 'input-mini'}),
            numero_rate_rinnovo=NumberInput(attrs={'class': 'input-mini'}),
            data_inizio=SuitDateWidget(),
            data_fine=SuitDateWidget(),
            # tipologia=Select2Widget(attrs={'class': 'input-xlarge'}),
            # categoria_canone=Select2Widget(attrs={'class': 'input-xlarge'}),
            note=forms.Textarea(attrs={'rows': 3, 'class': 'input-xxlarge'}),
            descrizione_aggiuntiva_fattura=forms.Textarea(attrs={'rows': 3, 'class': 'input-xxlarge'}),
            importo_rata=EuroInput(),
        )


class ScadenzaCanoneForm(forms.ModelForm):
    get_link_fattura = forms.CharField(required=False)
    get_link_canone = forms.CharField(required=False)

    class Meta:
        model = models.ScadenzaCanone
        fields = '__all__'
        widgets = dict(
            canone=CanoneWidget(rel=models.ScadenzaCanone.canone.field.remote_field),
            descrizione=forms.TextInput(attrs={'class': 'input-xxlarge'}),
            importo=EuroInput(),
            data_inizio=SuitDateWidget(),
            data_fine=SuitDateWidget(),
            data_scadenza=SuitDateWidget(),
            descrizione_fattura=forms.Textarea(attrs={'rows': 3, 'class': 'input-xxlarge'}),
            note=forms.Textarea(attrs={'rows': 3, 'class': 'input-xxlarge'}),
        )


class DescrizioneFatturaForm(forms.ModelForm):

    class Meta:
        model = models.DescrizioneFattura
        fields = '__all__'
        widgets = dict(
            descrizione=forms.Textarea(attrs={'rows': 8, 'class': 'input-xxlarge'}),
            # tipologia=Select2Widget(attrs={'class': 'input-xlarge'}),
            # categoria_canone=Select2Widget(attrs={'class': 'input-xlarge'}),
            # piano_conti_gamma=Select2Widget(attrs={'class': 'input-xxlarge'}),
        )


class UploadForm(forms.Form):
    csv_file = forms.FileField(label='CSV file', help_text='Dimensione massima: %d Mb' % settings.FILE_UPLOAD_MAX_MEMORY_SIZE_MB)

    def clean_csv_file(self):
        return parse_dati_linea_kpn(self.cleaned_data['csv_file'])

    def save(self):
        data = self.cleaned_data['csv_file']
        models.DatiLineaKpn.objects.all().delete()
        dati_linee_kpn = []
        for id, dati_linea in enumerate(data['elenco_dati']):
            dati_linea['id'] = id
            dati_linee_kpn.append(models.DatiLineaKpn(**dati_linea))
        models.DatiLineaKpn.objects.bulk_create(dati_linee_kpn)
