from django.apps import AppConfig
from django.db.models.signals import post_save, post_delete

default_app_config = 'mastergest.canoni.CanoniConfig'


class CanoniConfig(AppConfig):
    name = 'mastergest.canoni'
    verbose_name = 'Canoni'

    def ready(self):
        from mastergest.canoni import models
        from mastergest.canoni.callbacks import aggiorna_contratto_attivo
        post_save.connect(aggiorna_contratto_attivo, sender=models.Canone)
        post_delete.connect(aggiorna_contratto_attivo, sender=models.Canone)
