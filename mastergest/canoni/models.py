import os
import calendar

from datetime import date, timedelta
from decimal import Decimal, getcontext, ROUND_HALF_UP
from dateutil.relativedelta import relativedelta

from django.db import models
from django.core.files.temp import tempfile
from django.core.exceptions import ValidationError
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html

from mastergest.contratti.models import Contratto
from mastergest.utils.dates import calcola_data_scadenza
from mastergest.fatturazione.models import Fattura, RigaFattura, PianoContiGamma
from mastergest.fatturazione.utils import aggiungi_scaglioni_pagamento_fattura
from mastergest.canoni.utils import calcola_importo_ratino
from mastergest.canoni.constants import TIPI_RINNOVO, PERIODICITA, STATO_CANONI

getcontext().rounding = ROUND_HALF_UP

DECIMAL_ZERO = Decimal('0.00')


class Tipologia(models.Model):
    nome = models.CharField(max_length=200)

    class Meta:
        verbose_name_plural = 'tipologie canoni'
        verbose_name = 'tipologia canone'
        ordering = ['nome', ]

    def __str__(self):
        return str(self.nome)


class Categoria(models.Model):
    nome = models.CharField(max_length=200)

    class Meta:
        verbose_name_plural = 'categorie canoni'
        verbose_name = 'categoria canone'
        ordering = ['nome', ]

    def __str__(self):
        return str(self.nome)


class Canone(models.Model):
    contratto = models.ForeignKey(Contratto, on_delete=models.PROTECT)
    canone_successivo = models.ForeignKey(
        'self', null=True, blank=True, on_delete=models.SET_NULL
    )
    anticipato = models.BooleanField(default=False)
    data_inizio = models.DateField('data inizio')
    data_fine = models.DateField('data fine', null=True, blank=True)
    tipo_rinnovo = models.CharField(max_length=200, choices=TIPI_RINNOVO, default='manuale')
    numero_rate = models.PositiveIntegerField('rate', default=1)
    numero_rate_rinnovo = models.PositiveIntegerField('rate rinnovo', default=1)
    importo_rata = models.DecimalField(
        'importo rata', max_digits=9, decimal_places=2, default=0
    )
    periodicita = models.CharField(max_length=200, choices=PERIODICITA, default=360)
    note = models.TextField(blank=True)
    stato = models.CharField(max_length=200, choices=STATO_CANONI, null=True, blank=True,)
    contratto_ricevuto = models.BooleanField(default=False)
    numero_contratto_vecchio = models.PositiveIntegerField(null=True)
    tipologia = models.ForeignKey(
        Tipologia, default=1, on_delete=models.PROTECT
    )
    categoria_canone = models.ForeignKey(
        Categoria, null=True, blank=True, on_delete=models.PROTECT
    )
    descrizione_aggiuntiva_fattura = models.CharField(
        'descr. agg. fattura', max_length=1000, null=True, blank=True
    )
    con_ratino = models.BooleanField(default=False)
    bloccato = models.BooleanField(default=False, help_text='mettendo il blocco si impedisce di rigenerare le scadenze')

    class Meta:
        verbose_name_plural = 'canoni'
        ordering = ['data_fine', 'data_inizio']

    def __str__(self):
        if hasattr(self, 'contratto'):
            return 'Canone %s n.%s per %s (%s - %s)' % (
                self.tipologia, self.pk, self.contratto.sede, self.data_inizio, self.data_fine
            )
        else:
            return 'Canone %s n.%s (%s - %s)' % (
                self.tipologia, self.pk, self.data_inizio, self.data_fine
            )

    def get_data_fine_calcolata(self):
        data_fine = None
        if self.data_inizio and self.periodicita:
            data_fine = self.data_inizio + relativedelta(
                months=(int(self.periodicita) / 30) * int(self.numero_rate)
            ) - relativedelta(days=1)
            if self.con_ratino:
                data_fine_mese = calcola_data_scadenza(
                    data_iniziale=data_fine, fine_mese=True
                )
                data_fine = data_fine_mese
        return data_fine

    def get_gestione_azienda(self):
        return '%s' % self.contratto.gestione_azienda or ''
    get_gestione_azienda.short_description = 'Gest. Azienda'

    def save(self, *args, **kwargs):
        if not self.data_fine:
            data_fine = self.get_data_fine_calcolata()
            if data_fine:
                self.data_fine = data_fine
        crea_scadenze = False
        if not self.id:
            crea_scadenze = True
        super(Canone, self).save(*args, **kwargs)
        if crea_scadenze:
            self.aggiorna_scadenze()

    def clean(self):
        if self.con_ratino:
            if not self.periodicita == '30':
                raise ValidationError('Attenzione! E\' possibile utilizzare il ratino solo per i canoni mensili.')
            if self.data_inizio:
                if self.data_inizio.day == 1:
                    raise ValidationError('Attenzione! Non E\' possibile utilizzare il ratino per date di inizio al primo del mese.')
        if self.id:
            if not self.get_template_descrizione_fattura():
                raise ValidationError('Attenzione! Manca la descrizione fattura associata alla tipologia e categoria di canone corrente.')

    def rinnova(self):
        if not self.canone_successivo:
            contratto_allegato = None
            if self.tipo_rinnovo == 'tacito':
                contratto_allegato = self.contratto
            elif self.tipo_rinnovo == 'manuale':
                nuovo_contratto = Contratto()
                nuovo_contratto.azienda = self.contratto.azienda
                nuovo_contratto.fornitore = self.contratto.fornitore
                nuovo_contratto.categoria = 'canone'
                nuovo_contratto.gestione_azienda = self.contratto.gestione_azienda
                nuovo_contratto.data_consegna = self.data_fine + relativedelta(days=1)
                nuovo_contratto.data_consegna_contratto = self.data_fine + relativedelta(days=1)
                nuovo_contratto.data_stipula = self.data_fine + relativedelta(days=1)
                nuovo_contratto.agente = self.contratto.agente
                nuovo_contratto.sede = self.contratto.sede
                nuovo_contratto.save()
                contratto_allegato = nuovo_contratto
            nuovo_canone = Canone()
            nuovo_canone.contratto = contratto_allegato
            nuovo_canone.tipologia = self.tipologia
            nuovo_canone.categoria_canone = self.categoria_canone
            nuovo_canone.tipo_rinnovo = self.tipo_rinnovo
            nuovo_canone.periodicita = self.periodicita
            nuovo_canone.data_inizio = self.data_fine + relativedelta(days=1)
            nuovo_canone.numero_rate = self.numero_rate_rinnovo
            nuovo_canone.numero_rate_rinnovo = self.numero_rate_rinnovo
            nuovo_canone.importo_rata = self.importo_rata
            nuovo_canone.descrizione_aggiuntiva_fattura = self.descrizione_aggiuntiva_fattura
            nuovo_canone.anticipato = self.anticipato
            nuovo_canone.note = self.note
            nuovo_canone.save()
            self.canone_successivo = nuovo_canone
            self.save()
            return nuovo_canone
        else:
            return self

    def get_url(self):
        url = reverse('admin:canoni_canone_change', args=(self.id,))
        return url

    def get_link(self):
        url_file = self.get_url()
        return format_html('<a href="%s" target="">Vedi Canone</a>' % (url_file))
    get_link.short_description = 'Canone'
    get_link.admin_order_field = 'canone'

    def link_canone_successivo(self):
        if self.canone_successivo:
            return format_html('<a href="%s" target="">Vedi Canone</a>' % (self.canone_successivo.get_url()))
        else:
            return ''
    link_canone_successivo.short_description = ''
    link_canone_successivo.admin_order_field = 'canone_successivo'

    def get_sede_cliente(self):
        return self.contratto.sede
    get_sede_cliente.short_description = 'Sede Cliente'

    def get_sede_cliente_link(self):
        if self.contratto.sede:
            url = '?q=&contratto__sede__id__exact=%s' % self.contratto.sede.pk
            return format_html('<a href="%s">%s</a>' % (url, self.contratto.sede))
    get_sede_cliente_link.short_description = 'Cliente'
    get_sede_cliente_link.admin_order_field = 'cliente'

    def get_link_contratto(self):
        if self.contratto:
            return self.contratto.get_link_display()
    get_link_contratto.short_description = 'Vedi'
    get_link_contratto.admin_order_field = 'contratto'

    def get_cliente(self):
        if self.contratto.sede:
            return self.contratto.sede.anagrafica

    def get_utile(self):
        if self.importo_rata:
            totale_costi = self.get_totale_costi()
            if totale_costi:
                utile = self.importo_rata - totale_costi
                return utile
    get_utile.short_description = 'Utile'

    def get_totale_costi(self):
        if self.lineadati_set.all():
            totale_costo = DECIMAL_ZERO
            for linea in self.lineadati_set.all():
                totale_costo += linea.costo
            return totale_costo
    get_totale_costi.short_description = 'Totale Costi'

    def get_numero_contratto(self):
        if self.numero_contratto_vecchio:
            return self.numero_contratto_vecchio
        else:
            if self.contratto:
                return self.contratto.id

    def get_data_contratto(self):
        if self.contratto:
            return self.contratto.data_consegna_contratto

    def get_template_descrizione_fattura(self):
        if self.tipologia and self.categoria_canone:
            try:
                desc = DescrizioneFattura.objects.get(
                    tipologia=self.tipologia,
                    categoria_canone=self.categoria_canone
                )
                descrizione = desc.descrizione.replace('##numero_contratto##', str(self.get_numero_contratto()))
                data_contratto = self.get_data_contratto()
                if data_contratto:
                    descrizione = descrizione.replace('##data_contratto##', data_contratto.strftime('%d/%m/%Y'))
                if self.descrizione_aggiuntiva_fattura:
                    descrizione = descrizione.replace('##descrizione_aggiuntiva_fattura##', self.descrizione_aggiuntiva_fattura)
                else:
                    descrizione = descrizione.replace('##descrizione_aggiuntiva_fattura##', '')
                if self.contratto:
                    if self.contratto.sede:
                        if self.contratto.sede.anagrafica.stato.id in [1, 241]:
                            descrizione_sede = 'Sede di %s (%s) sita in %s' % (
                                self.contratto.sede.citta,
                                self.contratto.sede.provincia,
                                self.contratto.sede.indirizzo,
                            )
                        else:
                            descrizione_sede = 'site of %s located in %s' % (
                                self.contratto.sede.citta,
                                self.contratto.sede.indirizzo,
                            )
                        descrizione = descrizione.replace('##sede_cliente##', descrizione_sede)
                return descrizione
            except DescrizioneFattura.DoesNotExist:
                return None

    def get_piano_conti_gamma(self):
        if self.tipologia and self.categoria_canone:
            try:
                desc = DescrizioneFattura.objects.get(tipologia=self.tipologia, categoria_canone=self.categoria_canone)
                return desc.piano_conti_gamma
            except DescrizioneFattura.DoesNotExist:
                return None

    def get_template_stampa_contratto(self):
        if self.tipologia and self.categoria_canone:
            try:
                desc = DescrizioneFattura.objects.get(tipologia=self.tipologia, categoria_canone=self.categoria_canone)
                if desc.modello_contratto:
                    cartella_origine = tempfile.mkdtemp()
                    nome_file_origine = os.path.split(desc.modello_contratto.file.name)[1]
                    path_file_origine = '%s/%s' % (cartella_origine, nome_file_origine)
                    file_origine = open(path_file_origine, "wb")
                    file_origine.write(desc.modello_contratto.file.read())
                    file_origine.close()
                    return path_file_origine
            except DescrizioneFattura.DoesNotExist:
                return None
        return None

    def get_template_stampa_fax(self):
        if self.tipologia and self.categoria_canone:
            try:
                desc = DescrizioneFattura.objects.get(tipologia=self.tipologia, categoria_canone=self.categoria_canone)
                if desc.modello_fax:
                    cartella_origine = tempfile.mkdtemp()
                    nome_file_origine = os.path.split(desc.modello_fax.file.name)[1]
                    path_file_origine = '%s/%s' % (cartella_origine, nome_file_origine)
                    file_origine = open(path_file_origine, "wb")
                    file_origine.write(desc.modello_fax.file.read())
                    file_origine.close()
                    return path_file_origine
            except DescrizioneFattura.DoesNotExist:
                return None

    def aggiorna_scadenze(self):
        if self.bloccato:
            return 'Canone Bloccato! aggiornamento scadenze non effettuato.'
        if self.data_inizio:
            for scadenza in self.scadenzacanone_set.all():
                if not scadenza.fatturato:
                    scadenza.delete()
            if self.importo_rata and self.data_inizio and self.numero_rate > 0:
                if self.numero_rate == 1 and self.periodicita:
                    data_inizio = self.data_inizio
                    if self.con_ratino and self.periodicita == '30':
                        ratino = ScadenzaCanone()
                        ratino.canone = self
                        ratino.data_inizio = data_inizio
                        fine_mese = calcola_data_scadenza(data_iniziale=data_inizio, fine_mese=True)
                        ratino.data_fine = fine_mese
                        ratino.data_scadenza = data_inizio
                        ratino.descrizione = 'Ratino %s' % self
                        ratino.importo = calcola_importo_ratino(data_inizio, self.importo_rata)
                        ratino.ultima = False
                        ratino.save()
                        data_inizio = fine_mese + relativedelta(days=1)
                    data_fine = self.data_fine
                    rata_scadenza = ScadenzaCanone()
                    rata_scadenza.canone = self
                    rata_scadenza.data_fine = data_fine
                    rata_scadenza.data_inizio = data_inizio
                    rata_scadenza.data_scadenza = data_inizio
                    rata_scadenza.descrizione = 'Rata %s' % self
                    rata_scadenza.importo = self.importo_rata
                    rata_scadenza.ultima = True
                    rata_scadenza.save()
                else:
                    data_inizio_rate = self.data_inizio
                    if self.con_ratino and self.periodicita == '30':
                        ratino = ScadenzaCanone()
                        ratino.canone = self
                        ratino.data_inizio = data_inizio_rate
                        fine_mese = calcola_data_scadenza(data_iniziale=data_inizio_rate, fine_mese=True)
                        ratino.data_fine = fine_mese
                        ratino.data_scadenza = data_inizio_rate
                        ratino.descrizione = 'Ratino %s' % self
                        ratino.importo = calcola_importo_ratino(data_inizio_rate, self.importo_rata)
                        ratino.ultima = False
                        ratino.save()
                        data_inizio_rate = fine_mese + relativedelta(days=1)
                    for count in range(1, self.numero_rate + 1):
                        if self.anticipato:
                            data_rata = data_inizio_rate + relativedelta(months=(int(self.periodicita) / 30) * count) - relativedelta(days=1)
                            data_inizio = data_inizio_rate + relativedelta(months=(int(self.periodicita) / 30) * (count - 1))
                            primo_giorno_mese = date(month=data_inizio.month, day=1, year=data_inizio.year)
                            data_scadenza = primo_giorno_mese - timedelta(days=1)
                        else:
                            data_rata = data_inizio_rate + relativedelta(months=(int(self.periodicita) / 30) * count) - relativedelta(days=1)
                            data_inizio = data_inizio_rate + relativedelta(months=(int(self.periodicita) / 30) * (count - 1))
                            data_scadenza = data_inizio
                        rata_scadenza = ScadenzaCanone()
                        rata_scadenza.canone = self
                        rata_scadenza.data_fine = data_rata
                        rata_scadenza.data_inizio = data_inizio
                        rata_scadenza.data_scadenza = data_scadenza
                        rata_scadenza.descrizione = 'Rata %s - n. %s di %s' % (self, count, self.numero_rate)
                        rata_scadenza.importo = self.importo_rata
                        if count == self.numero_rate:
                            rata_scadenza.ultima = True
                        rata_scadenza.save()
            elenco_scadenze_fatturate = ScadenzaCanone.objects.filter(canone=self, fatturato=True)
            for scadenza in elenco_scadenze_fatturate:
                try:
                    scadenza_uguale = ScadenzaCanone.objects.get(
                        canone=self,
                        fatturato=False,
                        # importo=scadenza.importo,
                        data_inizio=scadenza.data_inizio,
                        data_fine=scadenza.data_fine,
                        data_scadenza=scadenza.data_scadenza
                    )
                    scadenza_uguale.delete()
                except ScadenzaCanone.DoesNotExist:
                    pass

    def get_stato_canone_csv(self):
        if self.stato:
            return self.stato
        else:
            if self.canone_successivo:
                return 'rinnovato'
            else:
                return 'attivo'

    def get_stato_canone(self):
        if self.stato:
            return format_html('<div class="totale_negativo">%s</span>' % self.stato)
        else:
            if self.canone_successivo:
                return format_html('<div class="rimanente_parziale">rinnovato</span>')
            else:
                return format_html('<div class="rimanente_tutto">attivo</span>')
    get_stato_canone.short_description = 'Stato'

    def is_attivo(self):
        if self.stato or self.canone_successivo:
            return False
        return True

    def get_rate_fatturate(self):
        fatturate = 0
        totali = 0
        if self.scadenzacanone_set.all():
            totali = self.scadenzacanone_set.all().count()
            fatturate = self.scadenzacanone_set.filter(fatturato=True).count()
        return '%s/%s' % (fatturate, totali)
    get_rate_fatturate.short_description = 'Fatt.'

    def get_rate_pagate(self):
        fatturate = 0
        pagate = 0
        if self.scadenzacanone_set.all():
            fatturate = self.scadenzacanone_set.filter(fatturato=True).count()
            for scadenza in self.scadenzacanone_set.filter(fatturato=True, riga_fattura__isnull=False):
                if scadenza.riga_fattura.fattura.pagata:
                    pagate += 1
        return '%s/%s' % (pagate, fatturate)
    get_rate_pagate.short_description = 'Pag.'

    def get_data_inizio_display(self):
        return self.data_inizio.strftime('%d/%m/%Y')
    get_data_inizio_display.short_description = 'Inizio'
    get_data_inizio_display.admin_order_field = 'data_inizio'

    def get_data_fine_display(self):
        if self.stato not in ['disdetto', 'sospeso', 'trasferito']:
            if 'attivo' in self.get_stato_canone():
                if self.data_fine < date.today():
                    return format_html('<div class="totale_negativo">%s</span>' % self.data_fine.strftime('%d/%m/%Y'))
        return format_html('<div class="rimanente_nullo">%s</span>' % self.data_fine.strftime('%d/%m/%Y'))
    get_data_fine_display.short_description = 'Fine'
    get_data_fine_display.admin_order_field = 'data_fine'


class CanoneMastertrainingManager(models.Manager):
    
        def get_queryset(self):
            qs = super(CanoneMastertrainingManager, self).get_queryset()
            return qs.filter(contratto__gestione_azienda='mastertraining')


class CanoneMastertraining(Canone):
    objects = CanoneMastertrainingManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Canoni Mastertraining'


class CanoneMagisterManager(models.Manager):
    
        def get_queryset(self):
            qs = super(CanoneMagisterManager, self).get_queryset()
            return qs.filter(contratto__gestione_azienda='magister')


class CanoneMagister(Canone):
    objects = CanoneMagisterManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'Canoni Magister'


class LineaDati(models.Model):
    canone = models.ForeignKey(Canone, on_delete=models.PROTECT)
    identificativo = models.CharField(max_length=200)
    descrizione = models.CharField(max_length=200)
    costo = models.DecimalField(max_digits=9, decimal_places=2, default=0)
    variazione = models.DecimalField(max_digits=9, decimal_places=2, default=0)
    ultimo_aggiornamento = models.DateTimeField(default=timezone.now)

    class Meta:
        verbose_name_plural = 'linee dati'
        verbose_name = 'linea dati'
        ordering = ['identificativo', ]

    def __str__(self):
        return str(self.identificativo)


class CanoneInScadenzaManager(models.Manager):

    def get_queryset(self):
        qs = super(CanoneInScadenzaManager, self).get_queryset()
        data_scadenza_massima = date.today() + relativedelta(months=2)
        primo_giorno, ultimo_giorno = calendar.monthrange(data_scadenza_massima.year, data_scadenza_massima.month)
        return qs.filter(
            data_fine__lte=date(day=ultimo_giorno, month=data_scadenza_massima.month, year=data_scadenza_massima.year),
            canone_successivo__isnull=True
        ).exclude(stato='disdetto').exclude(stato='sospeso').exclude(stato='trasferito')


class CanoneInScadenza(Canone):
    objects = CanoneInScadenzaManager()

    class Meta:
        proxy = True
        verbose_name = 'canone in scadenza'
        verbose_name_plural = 'canoni in scadenza'


class ScadenzaCanone(models.Model):
    canone = models.ForeignKey(Canone, on_delete=models.CASCADE)
    data_inizio = models.DateField('data inizio competenza')
    data_fine = models.DateField('data fine competenza')
    data_scadenza = models.DateField('data scadenza', null=True, blank=True)
    importo = models.DecimalField('importo', max_digits=9, decimal_places=2, default=0)
    descrizione = models.CharField(max_length=200)
    descrizione_fattura = models.TextField('descrizione per fattura', blank=True, null=True)
    fatturato = models.BooleanField(default=False)
    riga_fattura = models.ForeignKey(RigaFattura, null=True, blank=True, on_delete=models.SET_NULL)
    note = models.TextField(blank=True)
    ultima = models.BooleanField(default=False)
    sede_cliente = models.ForeignKey(
        'anagrafe.sede', null=True, blank=True, on_delete=models.PROTECT
    )
    cliente = models.ForeignKey(
        'anagrafe.anagrafica', null=True, blank=True, on_delete=models.PROTECT
    )

    class Meta:
        verbose_name_plural = 'rate canoni'
        verbose_name = 'rata canone'
        ordering = ['-data_scadenza', 'canone']

    def __str__(self):
        if hasattr(self, 'canone'):
            return 'Scadenza Canone %s per %s scad. %s - importo: %s' % (
                self.canone.tipologia, self.canone.contratto.sede, self.data_scadenza, self.importo
            )
        else:
            return self.descrizione

    def save(self, *args, **kwargs):
        if not self.data_scadenza:
            if self.data_inizio:
                self.data_scadenza = self.data_inizio
        if not self.descrizione_fattura:
            self.descrizione_fattura = self.get_descrizione_fattura()
        self.sede_cliente = self.canone.contratto.sede
        self.cliente = self.canone.contratto.sede.anagrafica
        super(ScadenzaCanone, self).save(*args, **kwargs)

    def fattura_scadenza(self):
        if not self.fatturato:
            if self.canone.contratto.gestione_azienda:
                ultima_data_utile = Fattura.objects.all().filter(gestione_azienda=self.canone.contratto.gestione_azienda).order_by('-anno_numero')[0].data
            else:
                ultima_data_utile = Fattura.objects.all().order_by('-anno_numero')[0].data
            nuova_fattura = Fattura(
                cliente=self.canone.get_sede_cliente().anagrafica, data=ultima_data_utile,
                sede_cliente=self.canone.get_sede_cliente(), gestione_azienda=self.canone.contratto.gestione_azienda
            )
            nuova_fattura.save()
            riga_scadenza = RigaFattura(
                fattura=nuova_fattura,
                quantita=Decimal('1.00'),
                descrizione=self.descrizione_fattura,
                costo_unitario=self.importo,
                piano_conti_gamma=self.canone.get_piano_conti_gamma(),
                sede_cliente=self.canone.get_sede_cliente()
            )
            riga_scadenza.save()
            nuova_fattura.get_scadenze_pagamenti().delete()
            aggiungi_scaglioni_pagamento_fattura(nuova_fattura)
            nuova_fattura.save()
            self.fatturato = True
            self.riga_fattura = riga_scadenza
            self.save()
            return nuova_fattura

    def get_cliente(self):
        return self.canone.get_sede_cliente().anagrafica or ''
    get_cliente.short_description = 'Cliente'
    get_cliente.admin_order_field = 'canone'

    def get_gestione_azienda(self):
        return '%s' % self.canone.contratto.gestione_azienda or ''
    get_gestione_azienda.short_description = 'Gest. Azienda'

    def get_sede_cliente(self):
        return self.canone.get_sede_cliente() or ''
    get_sede_cliente.short_description = 'Sede Cliente'
    get_sede_cliente.admin_order_field = 'canone'

    def get_link_fattura(self):
        if self.riga_fattura:
            return self.riga_fattura.fattura.get_link()
        else:
            return ''
    get_link_fattura.short_description = 'Fattura'
    get_link_fattura.admin_order_field = 'fattura'

    def get_url(self):
        url = reverse('admin:canoni_scadenzacanone_change', args=(self.id,))
        return url

    def get_link(self):
        return format_html('<a href="%s">%s</a>' % (self.get_url(), self.descrizione))

    def get_descrizione_fattura(self):
        if self.canone:
            descrizione = self.canone.get_template_descrizione_fattura()
            if descrizione:
                descrizione = descrizione.replace('##data_inizio##', str(self.data_inizio.strftime('%d/%m/%Y')))
                descrizione = descrizione.replace('##data_fine##', str(self.data_fine.strftime('%d/%m/%Y')))
                data_inizio_mese_prossimo = calcola_data_scadenza(data_iniziale=self.data_inizio, fine_mese=True, giorni_oltre_fine_mese=1)
                descrizione = descrizione.replace('##data_inizio_prossimo_mese##', str(data_inizio_mese_prossimo.strftime('%d/%m/%Y')))
                data_fine_mese_prossimo = calcola_data_scadenza(data_iniziale=self.data_inizio, mesi_scadenza=1, fine_mese=True)
                descrizione = descrizione.replace('##data_fine_prossimo_mese##', str(data_fine_mese_prossimo.strftime('%d/%m/%Y')))
            return descrizione

    def get_link_canone(self):
        return self.canone.get_link()
    get_link_canone.short_description = ''
    get_link_canone.admin_order_field = 'Canone'

    def get_sede_filtro_link(self):
        sede = self.canone.get_sede_cliente()
        if sede:
            url = '?canone__contratto__sede__id__exact=%s' % sede.id
            return format_html('<a href="%s">%s</a>' % (url, sede))
        return sede
    get_sede_filtro_link.short_description = 'Sede Cliente'
    get_sede_filtro_link.admin_order_field = 'sede_cliente'


class ScadenzaDaFatturareManager(models.Manager):

    def get_queryset(self):
        qs = super(ScadenzaDaFatturareManager, self).get_queryset()
        data_scadenza_massima = date.today() + relativedelta(months=3)
        return qs.filter(fatturato=False, data_scadenza__lte=data_scadenza_massima).exclude(canone__stato='sospeso')


class ScadenzaDaFatturare(ScadenzaCanone):
    objects = ScadenzaDaFatturareManager()

    class Meta:
        proxy = True
        verbose_name = 'scadenza da fatturare'
        verbose_name_plural = 'scadenze da fatturare'


def canoni_upload(instance, filename):
    return 'canoni/%s/%s' % (instance.pk, filename)


class DescrizioneFattura(models.Model):
    descrizione = models.TextField(
        'descrizione per fattura',
        help_text='tag disponibili:<br>##numero_contratto##<br>##data_contratto##<br>##data_inizio##<br>##data_fine##<br>##data_inizio_prossimo_mese##<br>##data_fine_prossimo_mese##<br>##sede_cliente##<br>##descrizione_aggiuntiva_fattura##'
    )
    piano_conti_gamma = models.ForeignKey(
        PianoContiGamma, null=True, blank=True, on_delete=models.PROTECT
    )
    modello_contratto = models.FileField(upload_to=canoni_upload, null=True, blank=True)
    modello_fax = models.FileField(upload_to=canoni_upload, null=True, blank=True)
    tipologia = models.ForeignKey(
        Tipologia, null=True, blank=True, on_delete=models.PROTECT
    )
    categoria_canone = models.ForeignKey(
        Categoria, null=True, blank=True, on_delete=models.PROTECT
    )

    class Meta:
        verbose_name_plural = 'Parametri Canoni'
        verbose_name = 'Parametro Canone'
        ordering = ['tipologia', 'categoria_canone']
        unique_together = (('tipologia', 'categoria_canone'),)

    def __str__(self):
        return str('Parametri %s %s' % (self.tipologia, self.categoria_canone))


class AssociaScadenzaFatturaManager(models.Manager):

    def get_queryset(self):
        qs = super(AssociaScadenzaFatturaManager, self).get_queryset()
        data_scadenza_massima = date.today()
        return qs.filter(data_scadenza__lte=data_scadenza_massima)


class AssociaScadenzaFattura(ScadenzaCanone):
    objects = AssociaScadenzaFatturaManager()

    class Meta:
        proxy = True
        verbose_name = 'associa scadenza fattura'
        verbose_name_plural = 'associa scadenze a fatture'


class DatiLineaKpn(models.Model):
    riga = models.PositiveIntegerField(default=0)
    documento = models.PositiveIntegerField()
    esercizio = models.PositiveIntegerField()
    descrizione = models.TextField()
    identificativo = models.PositiveIntegerField()
    cadenza = models.CharField(max_length=200)
    importo = models.DecimalField(max_digits=9, decimal_places=2, default=0)
    td = models.CharField(max_length=200)
    id_linea = models.PositiveIntegerField()
    ordine_linea = models.PositiveIntegerField()
    ragione_sociale = models.CharField(max_length=200)
    nome = models.CharField(max_length=200, null=True, blank=True)
    via = models.CharField(max_length=200)
    localita = models.CharField(max_length=200, null=True, blank=True)
    comune = models.CharField(max_length=200)
    cap = models.CharField(max_length=200, null=True, blank=True)
    provincia = models.CharField(max_length=200, null=True, blank=True)
    nazione = models.CharField(max_length=200, null=True, blank=True)
    protocollo = models.CharField(max_length=200)
    codice_prodotto = models.CharField(max_length=200)
    codice_articolo = models.CharField(max_length=200)
    codice_ordine_cliente = models.CharField(max_length=200)
    verificato = models.BooleanField(default=False)
    rateo = models.BooleanField(default=False)
    canone = models.ForeignKey(
        Canone, editable=False, null=True, blank=True, on_delete=models.PROTECT
    )

    class Meta:
        verbose_name_plural = 'dati linee kpn'
        ordering = ['ragione_sociale', 'riga', ]

    def get_link_canone(self):
        if self.canone:
            return self.canone.get_link()
        else:
            return ''
    get_link_canone.short_description = ''
    get_link_canone.admin_order_field = 'Canone'
