# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2019-10-01 08:43
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('acquisti', '0003_auto_20180312_0949'),
    ]

    operations = [
        migrations.AlterField(
            model_name='acquisto',
            name='autorizzato',
            field=models.BooleanField(default=False, verbose_name='aut.'),
        ),
        migrations.AlterField(
            model_name='acquisto',
            name='da_evadere',
            field=models.BooleanField(default=False, verbose_name='da evadere'),
        ),
        migrations.AlterField(
            model_name='acquisto',
            name='data_autorizzazione',
            field=models.DateTimeField(blank=True, null=True, verbose_name='data di autorizzazione'),
        ),
        migrations.AlterField(
            model_name='acquisto',
            name='data_richiesta',
            field=models.DateTimeField(blank=True, null=True, verbose_name='data inoltro richesta di autorizzazione'),
        ),
        migrations.AlterField(
            model_name='acquisto',
            name='richiedi_autorizzazione',
            field=models.BooleanField(default=False, verbose_name='rich. aut.'),
        ),
        migrations.AlterField(
            model_name='acquisto',
            name='settore',
            field=models.CharField(choices=[('mastervoice', 'MasterVoice'), ('mastercom', 'MasterCom'), ('interno', 'Interno')], default='mastervoice', max_length=200),
        ),
        migrations.AlterField(
            model_name='acquisto',
            name='stato_evasione',
            field=models.CharField(default='inevaso', max_length=200, verbose_name='stato'),
        ),
        migrations.AlterField(
            model_name='rigaacquisto',
            name='codice_fornitore',
            field=models.CharField(blank=True, max_length=200, verbose_name='cod. forn.'),
        ),
        migrations.AlterField(
            model_name='rigaacquisto',
            name='codice_interno',
            field=models.CharField(blank=True, max_length=200, verbose_name='cod. int.'),
        ),
        migrations.AlterField(
            model_name='rigaacquisto',
            name='quantita',
            field=models.PositiveIntegerField(default=1, verbose_name='q.ta'),
        ),
        migrations.AlterField(
            model_name='rigaacquisto',
            name='quantita_da_evadere',
            field=models.PositiveIntegerField(default=0, verbose_name='da evad.'),
        ),
        migrations.AlterField(
            model_name='rigaacquisto',
            name='quantita_evasa',
            field=models.PositiveIntegerField(default=0, verbose_name='evasa'),
        ),
        migrations.AlterField(
            model_name='rigaacquisto',
            name='quantita_scorte',
            field=models.PositiveIntegerField(default=0, verbose_name='scorte'),
        ),
    ]
