# -*- coding: utf-8 -*-


from django.db import models, migrations
import datetime


class Migration(migrations.Migration):

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Acquisto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('settore', models.CharField(default=b'mastervoice', max_length=200, choices=[(b'mastervoice', b'MasterVoice'), (b'mastercom', b'MasterCom'), (b'interno', b'Interno')])),
                ('riferimento', models.CharField(max_length=200, blank=True)),
                ('priorita', models.CharField(max_length=200, blank=True)),
                ('richiedi_autorizzazione', models.BooleanField(default=False, verbose_name=b'rich. aut.')),
                ('autorizzato', models.BooleanField(default=False, verbose_name=b'aut.')),
                ('data_inserimento', models.DateTimeField(default=datetime.datetime.now)),
                ('data_richiesta', models.DateTimeField(null=True, verbose_name=b'data inoltro richesta di autorizzazione', blank=True)),
                ('data_autorizzazione', models.DateTimeField(null=True, verbose_name=b'data di autorizzazione', blank=True)),
                ('note', models.TextField(blank=True)),
                ('stato_evasione', models.CharField(default=b'inevaso', max_length=200, verbose_name=b'stato')),
                ('da_evadere', models.BooleanField(default=False, verbose_name=b'da evadere')),
            ],
            options={
                'ordering': ('-data_inserimento', '-id'),
                'verbose_name_plural': 'acquisti',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='RigaAcquisto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('codice_interno', models.CharField(max_length=200, verbose_name=b'cod. int.', blank=True)),
                ('codice_fornitore', models.CharField(max_length=200, verbose_name=b'cod. forn.', blank=True)),
                ('fornitore', models.CharField(max_length=200, blank=True)),
                ('descrizione', models.CharField(max_length=200, blank=True)),
                ('quantita', models.PositiveIntegerField(default=1, verbose_name=b'q.ta')),
                ('quantita_scorte', models.PositiveIntegerField(default=0, verbose_name=b'scorte')),
                ('quantita_evasa', models.PositiveIntegerField(default=0, verbose_name=b'evasa')),
                ('quantita_da_evadere', models.PositiveIntegerField(default=0, verbose_name=b'da evad.')),
                ('acquisto', models.ForeignKey(to='acquisti.Acquisto', on_delete=models.deletion.CASCADE)),
            ],
            options={
                'ordering': ('-id',),
                'verbose_name_plural': 'righe acquisto',
            },
            bases=(models.Model,),
        ),
    ]
