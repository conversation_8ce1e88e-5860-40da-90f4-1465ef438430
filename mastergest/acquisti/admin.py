from django.contrib import admin
from mastergest.acquisti.models import RigaAcquisto
from mastergest.acquisti import forms


class RigaAcquistoInline(admin.TabularInline):
    raw_id_fields = ('prodotto',)
    fields = (
        'prodotto', 'codice_fornitore', 'fornitore', 'descrizione', 'quantita',
        'quantita_scorte', 'quantita_evasa', 'quantita_da_evadere',
        'get_sito_fornitore_link'
    )
    form = forms.RigaAcquistoForm
    model = RigaAcquisto
    readonly_fields = ('quantita_da_evadere', 'get_sito_fornitore_link')


class RigaAcquistoAdmin(admin.ModelAdmin):
    list_display = (
        'acquisto', 'get_note_acquisto', 'get_stato_autorizzazione',
        'codice_interno', 'codice_fornitore', 'fornitore', 'descrizione',
        'quantita', 'quantita_scorte', 'quantita_evasa', 'quantita_da_evadere',
        'get_da_evadere', 'get_sito_fornitore_link',
    )
    list_filter = ('acquisto__autorizzato', 'acquisto__stato_evasione', 'acquisto__settore', 'fornitore',)
    search_fields = (
        'codice_fornitore', 'codice_interno', 'descrizione',
    )


class AcquistoAdmin(admin.ModelAdmin):
    fieldsets = (
        (None, {'fields': (
            ('settore', 'priorita', 'riferimento'),
            ('richiedente', 'data_inserimento',),
            ('richiedi_autorizzazione', 'data_richiesta'),
            ('autorizzato', 'data_autorizzazione'),
            ('stato_evasione',),
            ('note',),
        )}),
    )
    list_display = (
        'id', 'data_inserimento', 'richiedente',
        'settore', 'priorita', 'riferimento', 'note', 'stato_evasione',
        'richiedi_autorizzazione', 'autorizzato', 'data_autorizzazione',
    )
    list_filter = ('autorizzato', 'stato_evasione', 'settore', 'da_evadere')
    search_fields = (
        'settore', 'riferimento', 'priorita', 'note', 'richiedente__username'
    )
    readonly_fields = (
        'richiedente', 'autorizzato', 'data_inserimento', 'data_richiesta',
        'data_autorizzazione', 'stato_evasione'
    )
    date_hierarchy = 'data_autorizzazione'
    inlines = [RigaAcquistoInline]
    form = forms.AcquistoForm

    def get_readonly_fields(self, request, obj=None):
        if request.user.is_superuser or request.user.has_perm('acquisti.puo_autorizzare_acquisti'):
            return (
                'richiedente', 'data_inserimento', 'data_richiesta',
                'data_autorizzazione', 'stato_evasione'
            )
        return self.readonly_fields

    def save_model(self, request, obj, form, change):
        if not obj.richiedente:
            obj.richiedente = request.user
        return super(AcquistoAdmin, self).save_model(request, obj, form, change)
