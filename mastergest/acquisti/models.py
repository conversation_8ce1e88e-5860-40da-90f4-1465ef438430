from django.db import models
from django.conf import settings
from django.utils import timezone
from django.urls import reverse
from django.contrib.contenttypes.models import ContentType

from mastergest.listino.models import Prodotto
from mastergest.utils.web import web_link, elenco_links
from mastergest.utils.tasks import invia_email_task


SETTORI = (
    ('mastervoice', 'MasterVoice'),
    ('mastercom', 'MasterCom'),
    ('interno', 'Interno'),
)

DESTINATARI_EMAIL_CONFERME = [
    '<EMAIL>',
    '<EMAIL>'
]


class Acquisto(models.Model):
    richiedente = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True,
        related_name='acquisti_acquisto_richiedente_pk',
        on_delete=models.PROTECT
    )
    settore = models.CharField(max_length=200, choices=SETTORI, default='mastervoice')
    riferimento = models.CharField(max_length=200, blank=True)
    priorita = models.CharField(max_length=200, blank=True)
    richiedi_autorizzazione = models.BooleanField('rich. aut.', default=False)
    autorizzato = models.BooleanField('aut.', default=False)
    data_inserimento = models.DateTimeField(default=timezone.now)
    data_richiesta = models.DateTimeField('data inoltro richesta di autorizzazione', blank=True, null=True)
    data_autorizzazione = models.DateTimeField('data di autorizzazione', blank=True, null=True)
    note = models.TextField(blank=True)
    stato_evasione = models.CharField('stato', max_length=200, default='inevaso')
    da_evadere = models.BooleanField('da evadere', default=False)

    class Meta:
        verbose_name_plural = 'acquisti'
        ordering = ('-data_inserimento', '-id',)

    def __str__(self):
        return '%s - %s/%s/%s' % (self.id, self.data_inserimento.day, self.data_inserimento.month, self.data_inserimento.year)

    def get_stato_evasione(self):
        elenco_righe = self.rigaacquisto_set.all()
        stato_evasione = 'inevaso'
        if elenco_righe:
            totale_da_evadere = 0
            totale_evaso = 0
            for riga in elenco_righe:
                totale_da_evadere += riga.quantita_da_evadere
                totale_evaso += riga.quantita_evasa
            if totale_da_evadere > 0:
                if totale_evaso > 0:
                    stato_evasione = 'parziale'
            else:
                stato_evasione = 'evaso'
        return stato_evasione

    def save(self, *args, **kwargs):
        self.stato_evasione = self.get_stato_evasione()
        richiesta_autorizzazione = False
        autorizzato_acquisto = False
        if self.pk:
            try:
                old_acquisto = Acquisto.objects.get(pk=self.pk)
                if not old_acquisto.richiedi_autorizzazione and self.richiedi_autorizzazione:
                    richiesta_autorizzazione = True
                    self.data_richiesta = timezone.now()
                if not old_acquisto.autorizzato and self.autorizzato:
                    autorizzato_acquisto = True
                    self.data_autorizzazione = timezone.now()
            except Acquisto.DoesNotExist:
                pass
        else:
            if self.richiedi_autorizzazione:
                self.data_richiesta = timezone.now()
                richiesta_autorizzazione = True
            if self.autorizzato:
                self.data_autorizzazione = timezone.now()
                autorizzato_acquisto = True
        self.da_evadere = False
        if not self.stato_evasione == 'evaso' and self.autorizzato:
            self.da_evadere = True
        super(Acquisto, self).save(*args, **kwargs)
        if richiesta_autorizzazione:
            self.email_richiedi_autorizzazione()
        if autorizzato_acquisto:
            self.email_autorizzato()

    def get_messaggio_righe(self):
        elenco_righe_acquisto = ''
        for riga in self.rigaacquisto_set.all():
            elenco_righe_acquisto += '%s%s%s' % (elenco_righe_acquisto, '\n\r', riga)
        return elenco_righe_acquisto

    def get_url(self):
        url = reverse('admin:acquisti_acquisto_change', args=(self.id,))
        return url

    def get_link_acquisto(self):
        return 'https://%s%s' % (settings.URL_GESTIONALE, self.get_url())

    def email_autorizzato(self):
        titolo = '[ACQUISTI] Autorizzato acquisto %s - codice %s - richiesto da %s' % (self.settore, self.pk, self.richiedente)
        link_acquisto = self.get_link_acquisto()
        elenco_righe_acquisto = self.get_messaggio_righe()
        messaggio = '%s\n\r%s\n\r%s' % (titolo, link_acquisto, elenco_righe_acquisto)
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo, messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_CONFERME + [self.richiedente.email],
        )

    def email_richiedi_autorizzazione(self):
        titolo = '[ACQUISTI] Richiesta autorizzazione per acquisto %s - codice %s - da parte di %s' % (self.settore, self.pk, self.richiedente)
        link_acquisto = self.get_link_acquisto()
        elenco_righe_acquisto = self.get_messaggio_righe()
        messaggio = '%s\n\r%s\n\r%s' % (titolo, link_acquisto, elenco_righe_acquisto)
        object_type = ContentType.objects.get_for_model(self)
        invia_email_task.delay(
            object_id=self.id,
            content_type_id=object_type.id,
            oggetto=titolo, messaggio=messaggio,
            destinatari=DESTINATARI_EMAIL_CONFERME + [self.richiedente.email],
        )


class RigaAcquisto(models.Model):
    acquisto = models.ForeignKey(Acquisto, on_delete=models.CASCADE)
    prodotto = models.ForeignKey(
        Prodotto, limit_choices_to=dict(a_listino=True), null=True, blank=True,
        on_delete=models.SET_NULL
    )
    codice_interno = models.CharField('cod. int.', max_length=200, blank=True)
    codice_fornitore = models.CharField('cod. forn.', max_length=200, blank=True)
    fornitore = models.CharField(max_length=200, blank=True)
    descrizione = models.CharField(max_length=200, blank=True)
    quantita = models.PositiveIntegerField('q.ta', default=1)
    quantita_scorte = models.PositiveIntegerField('scorte', default=0)
    quantita_evasa = models.PositiveIntegerField('evasa', default=0)
    quantita_da_evadere = models.PositiveIntegerField('da evad.', default=0)

    class Meta:
        verbose_name_plural = 'righe acquisto'
        ordering = ('-id',)

    def __str__(self):
        return '%sx %s (%s) da %s' % (self.quantita, self.descrizione, self.codice_fornitore, self.fornitore)

    def save(self, *args, **kwargs):
        self.quantita_da_evadere = self.quantita - (self.quantita_scorte + self.quantita_evasa)
        if self.prodotto:
            self.descrizione = self.prodotto.__str__()
            self.codice_fornitore = self.prodotto.codice_fornitore
            self.codice_interno = self.prodotto.codice
            self.fornitore = self.prodotto.fornitore.__str__()
            self.prodotto = None
        super(RigaAcquisto, self).save(*args, **kwargs)
        stato_corrente = self.acquisto.get_stato_evasione()
        if not self.acquisto.stato_evasione == stato_corrente:
            self.acquisto.stato_evasione = stato_corrente
            self.acquisto.save()

    def get_sito_fornitore_link(self):
        link = ''
        if self.fornitore and self.codice_fornitore:
            if self.fornitore.upper().startswith('ALLNET'):
                if '_' in str(self.codice_fornitore):
                    elenco_codici = self.codice_fornitore.split('_')
                    elenco_oggetti = []
                    if self.codice_fornitore.upper().startswith('BUNDLE'):
                        for codice in elenco_codici[1:]:
                            url = 'https://www.allnet-italia.it/search?sSearch=%s' % codice
                            elenco_oggetti.append(web_link(url))
                    else:
                        for codice in elenco_codici:
                            url = 'https://www.allnet-italia.it/search?sSearch=%s' % codice
                            elenco_oggetti.append(web_link(url))
                    return elenco_links(elenco_oggetti, False)
                else:
                    url = 'https://www.allnet-italia.it/search?sSearch=%s' % self.codice_fornitore
                    return web_link(url)
            if self.fornitore.upper().startswith('EMMEGI'):
                url = 'http://www.emmegiricambi.it/Prodotti.aspx?show=%s' % self.codice_fornitore
                return web_link(url)
        return link
    get_sito_fornitore_link.short_description = 'Link'
    get_sito_fornitore_link.allow_tags = True
    get_sito_fornitore_link.admin_order_field = 'codice_fornitore'

    def get_note_acquisto(self):
        if self.acquisto.note:
            return self.acquisto.note
        else:
            return ''
    get_note_acquisto.short_description = 'Note'

    def get_stato_autorizzazione(self):
        return self.acquisto.autorizzato
    get_stato_autorizzazione.short_description = 'Aut.'
    get_stato_autorizzazione.boolean = True

    def get_da_evadere(self):
        if self.quantita_da_evadere > 0:
            return True
        else:
            return False
    get_da_evadere.short_description = 'Evadi'
    get_da_evadere.admin_order_field = 'quantita_da_evadere'
    get_da_evadere.boolean = True
