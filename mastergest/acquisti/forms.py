from django import forms
from mastergest.acquisti import models
from suit.widgets import NumberInput


class <PERSON><PERSON><PERSON><PERSON>Form(forms.ModelForm):

    class Meta:
        model = models.Acquisto
        fields = '__all__'
        widgets = dict(
            note=forms.Textarea(attrs={'rows': 3, 'class': 'input-xxlarge'}),
        )


class RigaAcquistoForm(forms.ModelForm):
    get_sito_fornitore_link = forms.CharField(label='Link', required=False)

    class Meta:
        model = models.RigaAcquisto
        fields = '__all__'
        widgets = dict(
            codice_fornitore=forms.TextInput(attrs={'class': 'input-small'}),
            fornitore=forms.TextInput(attrs={'class': 'input-small'}),
            descrizione=forms.TextInput(attrs={'style': 'width:340px'}),
            quantita=NumberInput(attrs={'style': 'width:40px'}),
            quantita_scorte=NumberInput(attrs={'style': 'width:40px'}),
            quantita_evasa=NumberInput(attrs={'style': 'width:40px'}),
        )
