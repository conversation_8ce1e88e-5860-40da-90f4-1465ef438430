import factory

from .. import models
from mastergest.anagrafe.tests.factories import UserFactory


class AcquistoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.Acquisto

    richiedente = factory.SubFactory(UserFactory)


class RigaAcquistoFactory(factory.DjangoModelFactory):
    class Meta:
        model = models.RigaAcquisto

    acquisto = factory.SubFactory(AcquistoFactory)
    descrizione = factory.Sequence(lambda n: 'prodotto richiesto %s' % n)
