from django.urls import reverse
from django.test import TestCase

from . import factories
from mastergest.anagrafe.tests.factories import UserFactory


class TestAcquistoAdmin(TestCase):
    def setUp(self):
        self.utente = UserFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('admin:acquisti_acquisto_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('admin:acquisti_acquisto_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        acquisto = factories.AcquistoFactory(richiedente=self.utente)
        url = reverse('admin:acquisti_acquisto_change', args=(acquisto.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        acquisto = factories.AcquistoFactory(richiedente=self.utente)
        url = reverse('admin:acquisti_acquisto_delete', args=(acquisto.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
