from django.contrib.auth import get_user_model
from django.core import mail
from django.test import TestCase

from mastergest.acquisti.models import DESTINATARI_EMAIL_CONFERME
from mastergest.acquisti.tests import factories
from mastergest.anagrafe.tests.factories import UserFactory


class TestMail(TestCase):

    def setUp(self):
        mail.outbox = []
        self.user_model = get_user_model()

    def test_ok(self):
        mara = UserFactory(
            username='mara', last_name='<PERSON><PERSON><PERSON><PERSON>', first_name='<PERSON>',
            email='<EMAIL>'
        )
        acquisto = factories.AcquistoFactory(
            settore='mastervoice', priorita='alta', richiedente=mara,
            richiedi_autorizzazione=False, autorizzato=False
        )
        self.assertEqual(len(mail.outbox), 0)
        factories.RigaAcquistoFactory(
            acquisto=acquisto, quantita=10,
            codice_fornitore='HW9999', descrizione='Telefono IP Snom 9999',
            fornitore='Prova SPA'
        )
        acquisto.richiedi_autorizzazione = True
        acquisto.save()
        self.assertEqual(len(mail.outbox), 1)
        message = mail.outbox[0]
        self.assertEqual(
            message.subject,
            '[ACQUISTI] Richiesta autorizzazione per acquisto mastervoice - codice %s - da parte di Bizzarri Mara' % acquisto.id
        )
        self.assertEqual(
            message.recipients(),
            DESTINATARI_EMAIL_CONFERME + [mara.email, ]
        )
        self.assertEqual(
            message.body,
            '[ACQUISTI] Richiesta autorizzazione per acquisto mastervoice - codice %s - da parte di Bizzarri Mara\n\rhttps://gestionale.mastertraining.it/acquisti/acquisto/%s/change/\n\r\n\r10x Telefono IP Snom 9999 (HW9999) da Prova SPA' % (acquisto.id, acquisto.id)
        )
        acquisto.autorizzato = True
        acquisto.save()
        self.assertEqual(len(mail.outbox), 2)
        message = mail.outbox[1]
        self.assertEqual(
            message.subject,
            '[ACQUISTI] Autorizzato acquisto mastervoice - codice %s - richiesto da Bizzarri Mara' % acquisto.id
        )
        self.assertEqual(
            message.recipients(),
            DESTINATARI_EMAIL_CONFERME + [mara.email, ]
        )
        self.assertEqual(
            message.body,
            '[ACQUISTI] Autorizzato acquisto mastervoice - codice %s - richiesto da Bizzarri Mara\n\rhttps://gestionale.mastertraining.it/acquisti/acquisto/%s/change/\n\r\n\r10x Telefono IP Snom 9999 (HW9999) da Prova SPA' % (acquisto.id, acquisto.id)
        )

    def test_acquisti_inserisci_richiesta(self):
        mara = UserFactory(
            username='mara', last_name='Bizzarri', first_name='Mara',
            email='<EMAIL>'
        )
        acquisto = factories.AcquistoFactory(
            settore='mastervoice', priorita='alta', richiedente=mara,
            richiedi_autorizzazione=False, autorizzato=False
        )
        acquisto.richiedi_autorizzazione = True
        acquisto.save()
        self.assertEqual(len(mail.outbox), 1)
        message = mail.outbox[0]
        self.assertEqual(
            message.subject,
            '[ACQUISTI] Richiesta autorizzazione per acquisto mastervoice - codice %s - da parte di Bizzarri Mara' % acquisto.id
        )
        self.assertEqual(
            message.recipients(),
            DESTINATARI_EMAIL_CONFERME + [mara.email, ]
        )
        self.assertEqual(
            message.body,
            '[ACQUISTI] Richiesta autorizzazione per acquisto mastervoice - codice %s - da parte di Bizzarri Mara\n\rhttps://gestionale.mastertraining.it/acquisti/acquisto/%s/change/\n\r' % (acquisto.id, acquisto.id)
        )

    def test_acquisti_inserisci_richiesta_autorizzato(self):
        mara = UserFactory(
            username='mara', last_name='Bizzarri', first_name='Mara',
            email='<EMAIL>'
        )
        acquisto = factories.AcquistoFactory(
            settore='mastervoice', priorita='alta', richiedente=mara,
            richiedi_autorizzazione=False, autorizzato=False
        )
        acquisto.richiedi_autorizzazione = True
        acquisto.autorizzato = True
        acquisto.save()
        self.assertEqual(len(mail.outbox), 2)
        message = mail.outbox[0]
        self.assertEqual(
            message.subject,
            '[ACQUISTI] Richiesta autorizzazione per acquisto mastervoice - codice %s - da parte di Bizzarri Mara' % acquisto.id
        )
        self.assertEqual(
            message.recipients(),
            DESTINATARI_EMAIL_CONFERME + [mara.email, ]
        )
        self.assertEqual(
            message.body,
            '[ACQUISTI] Richiesta autorizzazione per acquisto mastervoice - codice %s - da parte di Bizzarri Mara\n\rhttps://gestionale.mastertraining.it/acquisti/acquisto/%s/change/\n\r' % (acquisto.id, acquisto.id)
        )
        message = mail.outbox[1]
        self.assertEqual(
            message.subject,
            '[ACQUISTI] Autorizzato acquisto mastervoice - codice %s - richiesto da Bizzarri Mara' % acquisto.id
        )
        self.assertEqual(
            message.recipients(),
            DESTINATARI_EMAIL_CONFERME + [mara.email, ]
        )
        self.assertEqual(
            message.body,
            '[ACQUISTI] Autorizzato acquisto mastervoice - codice %s - richiesto da Bizzarri Mara\n\rhttps://gestionale.mastertraining.it/acquisti/acquisto/%s/change/\n\r' % (acquisto.id, acquisto.id)
        )
