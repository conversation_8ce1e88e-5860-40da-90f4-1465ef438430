import base64
import os.path
import email

from email.utils import parseaddr, parsedate_to_datetime
from email.header import decode_header

import html2text

from django.conf import settings
from django.core.files.base import ContentFile
from django.contrib.contenttypes.models import ContentType

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SCOPES = ['https://www.googleapis.com/auth/gmail.modify']

MITTENTI_ESCLUSI = ['<EMAIL>', ]
# MITTENTI_ESCLUSI = []


def cerca_sede_da_email(email):
    from mastergest.anagrafe.models import Sede, Referente
    try:
        sede = Sede.objects.get(email=email)
        return sede
    except Sede.DoesNotExist:
        pass
    elenco_referenti = Referente.objects.filter(email=email)
    if elenco_referenti:
        return elenco_referenti[0].sede_cliente


def get_servizio_google_gmail():
    file_credenziali = settings.APP_PATH + '/gmail/credenziali_gmail.json'
    file_token = settings.APP_PATH + '/gmail/token.json'
    creds = None
    if os.path.exists(file_token):
        creds = Credentials.from_authorized_user_file(file_token, SCOPES)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                file_credenziali, SCOPES)
            creds = flow.run_local_server(port=0)
        with open(file_token, 'w') as token:
            token.write(creds.to_json())
    try:
        service = build('gmail', 'v1', credentials=creds)
        return service
    except HttpError as error:
        print(f'An error occurred: {error}')


def estrai_testo_messaggio(messaggio_email):
    if messaggio_email.is_multipart():
        for part in messaggio_email.walk():
            payload = part.get_payload()
            if isinstance(payload, list):
                for part in payload:
                    testo_messaggio = estrai_testo_messaggio(part)
                    if testo_messaggio:
                        return testo_messaggio
    else:
        content_type = messaggio_email.get_content_type()
        if content_type == 'text/plain':
            testo_messaggio = messaggio_email.get_body(('plain', )).get_content()
        else:
            body_messaggio = messaggio_email.get_body()
            if body_messaggio:
                contenuto = body_messaggio.get_content()
                testo_messaggio = html2text.html2text(contenuto)
        return testo_messaggio


def aggiorna_elenco_email():
    from mastergest.gmail.models import EmailGoogle
    from mastergest.attachments.models import Attachment
    EmailGoogle.objects.all().delete()
    google_gmail = get_servizio_google_gmail()
    result = google_gmail.users().messages().list(userId='me', labelIds=['INBOX'], q="is:unread").execute()
    elenco_email = result.get('messages')
    if elenco_email:
        for email_google in elenco_email:
            google_id = email_google['id']
            messaggio_email = google_gmail.users().messages().get(userId='me', id=google_id, format='raw').execute()
            corpo_messaggio = base64.urlsafe_b64decode(messaggio_email['raw'])
            try:
                corpo_decodificato = corpo_messaggio.decode('utf-8')
            except UnicodeDecodeError:
                corpo_decodificato = corpo_messaggio.decode('latin-1')
            msg = email.message_from_string(corpo_decodificato, policy=email.policy.default)
            for testata in msg._headers:
                if testata[0] == 'Subject':
                    stringa_oggetto, encoding = decode_header(testata[1])[0]
                    if isinstance(stringa_oggetto, bytes):
                        try:
                            oggetto = stringa_oggetto.decode('utf-8')
                        except UnicodeDecodeError:
                            oggetto = stringa_oggetto.decode('latin-1')
                    else:
                        oggetto = stringa_oggetto
                if testata[0] == 'From':
                    header_decodificato = decode_header(testata[1])
                    mittente = ''
                    for parte_mittente in header_decodificato:
                        stringa_mittente = parte_mittente[0]
                        if isinstance(stringa_mittente, bytes):
                            try:
                                mittente_decodificato = stringa_mittente.decode('utf-8')
                            except UnicodeDecodeError:
                                mittente_decodificato = stringa_mittente.decode('latin-1')
                        else:
                            mittente_decodificato = stringa_mittente
                        mittente += mittente_decodificato
            if msg['date']:
                data_arrivo = parsedate_to_datetime(msg['date'])
            elenco_mittente = parseaddr(mittente)
            nome_mittente = elenco_mittente[0]
            email_mittente = elenco_mittente[1]
            if email_mittente not in MITTENTI_ESCLUSI:
                testo_messaggio = estrai_testo_messaggio(msg)
                if testo_messaggio:
                    # CREA RECORD EMAIL GOOGLE
                    nuova_email = EmailGoogle()
                    nuova_email.google_id = google_id
                    nuova_email.oggetto = oggetto
                    nuova_email.data_arrivo = data_arrivo
                    nuova_email.nome_mittente = nome_mittente
                    nuova_email.email_mittente = email_mittente
                    nuova_email.testo = testo_messaggio
                    nuova_email.save()
                    # LEGGI ALLEGATI
                    for part in msg.walk():
                        filename = part.get_filename()
                        if filename:
                            file_data = part.get_content()
                            nuovo_allegato = Attachment()
                            object_type = ContentType.objects.get_for_model(EmailGoogle)
                            nuovo_allegato.content_type = object_type
                            nuovo_allegato.object_id = nuova_email.id
                            nuovo_allegato.descrizione = filename
                            nuovo_allegato.save()
                            nuovo_allegato.attachment_file.save(filename, ContentFile(file_data), save=True)


def imposta_email_letta(google_id):
    google_gmail = get_servizio_google_gmail()
    result = google_gmail.users().messages().modify(userId='me', id=google_id, body={'removeLabelIds': ['UNREAD']}).execute()
    if result:
        return True
    return False
