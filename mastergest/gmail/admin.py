from django.conf.urls import url
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404

from mastergest.gmail.utils import aggiorna_elenco_email
from mastergest.utils.admin import MastergestAdmin
from mastergest.gmail import forms
from mastergest.attachments.admin import AttachmentInlinesReadOnly
from mastergest.gmail.models import EmailGoogle


class EmailGoogleAdmin(MastergestAdmin):
    list_display = (
        'nome_mittente', 'oggetto', 'email_mittente', 'data_arrivo', 'google_id', 'sede_cliente'
    )
    list_display_links = ('nome_mittente', 'oggetto')
    readonly_fields = (
        'google_id', 'email_mittente', 'nome_mittente', 'data_arrivo',
    )
    autocomplete_fields = ('sede_cliente', )
    form = forms.EmailGoogleForm
    inlines = [AttachmentInlinesReadOnly, ]
    fieldsets = (
        (
            'Dati generali', dict(
                fields=(
                    ('data_arrivo', 'google_id'),
                    'oggetto',
                    ('nome_mittente', 'email_mittente'),
                )),
        ),
        (
            'Cliente', dict(
                fields=(
                    'sede_cliente',
                )),
        ),
        (
            'Contenuto', dict(
                classes=('suit-tab suit-tab-dati_generali',),
                fields=(
                    'testo',
                )),
        ),
    )
    suit_form_tabs = (
        ('dati_generali', 'Dati Generali'),
        ('allegati', 'Allegati'),
    )

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'^aggiorna/',
                self.admin_site.admin_view(self.aggiorna),
                name='gmail_emailgoogle_aggiorna',
            ),
            url(
                r'^(.+)/generachiamata/$',
                self.admin_site.admin_view(self.genera_chiamata_email),
                name='%s_%s_generachiamata' % info
            ),
        ]
        url_patterns += super(EmailGoogleAdmin, self).get_urls()
        return url_patterns

    def aggiorna(self, request):
        aggiorna_elenco_email()
        url = reverse('admin:gmail_emailgoogle_changelist')
        return HttpResponseRedirect(url)

    def genera_chiamata_email(self, request, object_id):
        email_google = get_object_or_404(EmailGoogle, pk=object_id)
        chiamata = email_google.genera_chiamata(request.user)
        email_google.delete()
        url = reverse('admin:assistenza_chiamata_change', args=(chiamata.pk,))
        return HttpResponseRedirect(url)
