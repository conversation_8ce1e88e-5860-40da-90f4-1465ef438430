import pytz

from django.conf import settings
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse

from mastergest.anagrafe.models import Sede
from mastergest.gmail.utils import cerca_sede_da_email, imposta_email_letta


class EmailGoogle(models.Model):
    oggetto = models.CharField(max_length=500)
    sede_cliente = models.ForeignKey(
        Sede, null=True, blank=True, on_delete=models.SET_NULL
    )
    testo = models.TextField(null=True, blank=True)
    data_arrivo = models.DateTimeField()
    nome_mittente = models.Char<PERSON>ield(max_length=500, null=True, blank=True)
    email_mittente = models.Char<PERSON>ield(max_length=500)
    google_id = models.CharField(max_length=500, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'email Google'
        ordering = ('-data_arrivo', 'nome_mittente', )
        app_label = 'gmail'

    def __str__(self):
        return '%s - %s' % (self.oggetto, self.data_arrivo)

    def get_cliente(self):
        if self.sede_cliente:
            return self.sede_cliente.anagrafica

    def get_url(self):
        url = reverse('admin:gmail_emailgoogle_change', args=(self.id,))
        return url

    def save(self, *args, **kwargs):
        if self.email_mittente:
            if not self.sede_cliente:
                sede = cerca_sede_da_email(self.email_mittente)
                if sede:
                    self.sede_cliente = sede
        return super(EmailGoogle, self).save(*args, **kwargs)

    def genera_chiamata(self, proprietario):
        if self.sede_cliente:
            from mastergest.assistenza.models import Chiamata
            nuova_chiamata = Chiamata()
            nuova_chiamata.titolo = self.oggetto
            nuova_chiamata.sede_cliente = self.sede_cliente
            nuova_chiamata.tipo = 'email'
            date_format = '%d/%m/%Y - ore %H:%M'
            data_arrivo = self.data_arrivo.astimezone(pytz.timezone(settings.TIME_ZONE))
            nuova_chiamata.note = 'mail del %s' % data_arrivo.strftime(date_format) + '\r\n' + self.testo
            nuova_chiamata.nome_chiamante = self.nome_mittente
            nuova_chiamata.proprietario = proprietario
            nuova_chiamata.email_chiamante = self.email_mittente
            nuova_chiamata.save()
            from mastergest.attachments.models import Attachment
            elenco_allegati = Attachment.objects.attachments_for_object(self).all()
            if elenco_allegati:
                for allegato in elenco_allegati:
                    object_type = ContentType.objects.get_for_model(Chiamata)
                    allegato.content_type = object_type
                    allegato.object_id = nuova_chiamata.id
                    allegato.save()
            if nuova_chiamata:
                imposta_email_letta(self.google_id)
            return nuova_chiamata
