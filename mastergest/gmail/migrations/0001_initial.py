# Generated by Django 2.2.12 on 2022-10-11 09:16

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EmailGoogle',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('oggetto', models.CharField(max_length=200)),
                ('testo', models.TextField(blank=True, null=True)),
                ('data_arrivo', models.DateTimeField(default=django.utils.timezone.now)),
                ('mittente', models.CharField(max_length=200)),
                ('google_id', models.CharField(blank=True, max_length=500, null=True)),
            ],
            options={
                'verbose_name_plural': 'email Google',
                'ordering': ('data_arrivo', 'mittente'),
            },
        ),
    ]
