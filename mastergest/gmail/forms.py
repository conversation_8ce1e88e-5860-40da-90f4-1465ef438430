from django import forms

from mastergest.gmail.models import EmailGoogle

from mastergest.anagrafe.widgets import SedeWidget


class EmailGoogleForm(forms.ModelForm):

    class Meta:
        model = EmailGoogle
        fields = '__all__'
        widgets = dict(
            testo=forms.Textarea(attrs={'rows': 25, 'class': 'input-xxlarge'}),
            sede_cliente=SedeWidget(),
            oggetto=forms.TextInput(attrs={'class': 'input-xxlarge'}),
        )
