from django.contrib import admin


class AdozioneStoricoAdmin(admin.ModelAdmin):
    list_display = (
        'codice_scuola', 'scuola', 'classe', 'sezione', 'opera', 'anno_adozione', 'editore'
    )
    list_filter = ('tipo_scuola', 'classe', 'sezione', 'anno_adozione')
    autocomplete_fields = ('opera', 'editore', 'scuola', 'materia', 'indirizzo')


class OperaAdmin(admin.ModelAdmin):
    list_display = (
        'codice_prodotto', 'editore', 'autore',
        'titolo', 'anno_edizione', 'tipo_scuola', 'prezzo', 'progressivo_volume'
    )
    list_filter = ('anno_edizione', 'editore', 'tipo_scuola', 'novita_anno_corrente', )
    search_fields = (
        'codice_prodotto', 'titolo', 'autore', 'autore_2', 'autore_3',
        'codice_fondamentale', 'codice_isbn', 'codice_2', 'codice_3'
    )


class EditoreAdmin(admin.ModelAdmin):
    list_display = (
        'codice_editore', 'descrizione_editore', 'annullamento_logico',
        'codice_editore_distributore'
    )
    search_fields = ('codice_editore', 'descrizione_editore', 'codice_editore_distributore')


class DisciplinaAdmin(admin.ModelAdmin):
    list_display = (
        'codice', 'descrizione',
    )
    search_fields = ('codice', 'descrizione')


class MateriaAdmin(admin.ModelAdmin):
    list_display = (
        'codice', 'descrizione_1', 'descrizione_2', 'annullamento_logico',
    )
    search_fields = ('codice', 'descrizione_1', 'descrizione_2', )


class MateriaDisciplinaAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'materia', 'disciplina',
    )


class TipoScuolaAdmin(admin.ModelAdmin):
    list_display = (
        'sigla', 'descrizione', 'tipi_scuola_dipendenti', 'annullamento_logico',
        'ordinamento'
    )


class IndirizzoAdmin(admin.ModelAdmin):
    list_display = (
        'tipo_scuola', 'descrizione', 'codice_sperimentazione', 'codice_specializzazione',
    )
    list_filter = ('tipo_scuola', 'codice_sperimentazione', 'codice_specializzazione')
    search_fields = ('descrizione', 'codice_sperimentazione', 'codice_specializzazione')


class ClasseAdmin(admin.ModelAdmin):
    list_display = (
        'scuola', 'tipo_scuola', 'classe', 'suffisso', 'indirizzo',
        'codice_sperimentazione', 'codice_specializzazione', 'alunni',
    )
    list_filter = (
        'classe', 'tipo_scuola', 'codice_sperimentazione', 'codice_specializzazione'
    )


class ScuolaAdmin(admin.ModelAdmin):
    list_display = ('codice_meccanografico', 'nome', 'indirizzo', 'cap', 'sede')
    search_fields = ('codice_meccanografico', 'nome')


class SedeScolasticaAdmin(admin.ModelAdmin):
    list_display = ('codice_ministeriale', 'nome', 'indirizzo', 'cap', 'telefono')


class ModalitaTipoAdmin(admin.ModelAdmin):
    pass
