import zipfile
import tempfile

from django.utils import timezone
from django.core.management.base import BaseCommand

from mastergest.aie.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>dalitaTipo
from mastergest.aie.models import Editore, Disciplina, Materia, MateriaDisciplina
from mastergest.aie.models import TipoScuola, Indirizzo, Classe, Opera, AdozioneStorico

ELENCO_FILES = [
    'FILE014.TXT',  # ANAGRAFICA DELLE SCUOLE, DEI PUNTI DI EROGAZIONE DEL SERVIZIO
    'FILE022.TXT',  # MODALITÀ DI TIPO
    'FILE030.TXT',  # EDITORI
    'FILE046.TXT',  # DISCIPLINE
    'FILE301.TXT',  # MATERIE
    'FILE023.TXT',  # DISCIPLINE/MATERIE
    'FILE302.TXT',  # TIPI SCUOLA
    'FILE306.TXT',  # INDIRIZZI
    'FILE012.TXT',  # <PERSON><PERSON><PERSON><PERSON><PERSON> OPERE
]

DIRECTORY_DEFAULT = '/home/<USER>/Scrivania/AIE/aggiornamento'


class Command(BaseCommand):
    help = 'AGGIORNA files AIE'

    def aggiorna_modalita_tipo(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            codice = stringa_record[0:3].strip()
            nuova_modalita_tipo, created = ModalitaTipo.objects.get_or_create(codice=codice)
            nuova_modalita_tipo.codice = codice
            nuova_modalita_tipo.descrizione = stringa_record[3:259].strip()
            nuova_modalita_tipo.save()
            if created:
                print('IMPORTATA MODALITA TIPO: %s - %s' % (nuova_modalita_tipo.codice, nuova_modalita_tipo.descrizione))
            else:
                print('AGGIORNATA MODALITA TIPO: %s - %s' % (nuova_modalita_tipo.codice, nuova_modalita_tipo.descrizione))

    def aggiorna_disciplina(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            codice = stringa_record[0:4].strip()
            nuova_disciplina, created = Disciplina.objects.get_or_create(codice=codice)
            nuova_disciplina.codice = codice
            nuova_disciplina.descrizione = stringa_record[4:104].strip()
            nuova_disciplina.save()
            if created:
                print('IMPORTATA DISCIPLINA: %s - %s' % (nuova_disciplina.codice, nuova_disciplina.descrizione))
            else:
                print('AGGIORNATA DISCIPLINA: %s - %s' % (nuova_disciplina.codice, nuova_disciplina.descrizione))

    def aggiorna_materia(self, stringa_record):
        if stringa_record:
            codice = stringa_record[0:6].strip()
            nuova_materia, created = Materia.objects.get_or_create(codice=codice)
            nuova_materia.codice = codice
            nuova_materia.descrizione_1 = stringa_record[6:49].strip()
            nuova_materia.descrizione_2 = stringa_record[49:102].strip()
            nuova_materia.annullamento_logico = stringa_record[102:103].strip()
            nuova_materia.save()
            if created:
                print('IMPORTATA MATERIA: %s - %s' % (nuova_materia.codice, nuova_materia.descrizione_1))
            else:
                print('AGGIORNATA MATERIA: %s - %s' % (nuova_materia.codice, nuova_materia.descrizione_1))

    def aggiorna_materia_disciplina(self, stringa_record):
        if stringa_record:
            materia_cercata = None
            codice_materia = stringa_record[0:6].strip()
            if codice_materia:
                try:
                    materia_cercata = Materia.objects.get(codice=codice_materia)
                except Materia.DoesNotExist:
                    print('MATERIA con codice %s NON TROVATA!' % codice_materia)
            disciplina_cercata = None
            codice_disciplina = stringa_record[6:10].strip()
            if codice_disciplina:
                try:
                    disciplina_cercata = Disciplina.objects.get(codice=codice_disciplina)
                except Disciplina.DoesNotExist:
                    print('DISCIPLINA con codice %s NON TROVATA!' % codice_disciplina)
            if materia_cercata and disciplina_cercata:
                nuova_materia_disciplina, created = MateriaDisciplina.objects.get_or_create(materia=materia_cercata, disciplina=disciplina_cercata)
                nuova_materia_disciplina.materia = materia_cercata
                nuova_materia_disciplina.disciplina = disciplina_cercata
                nuova_materia_disciplina.save()
                if created:
                    print('IMPORTATA MATERIA-DISCIPLINA: %s - %s' % (materia_cercata, disciplina_cercata))
                else:
                    print('AGGIORNATA MATERIA-DISCIPLINA: %s - %s' % (materia_cercata, disciplina_cercata))

    def aggiorna_editore(self, stringa_record):
        if stringa_record:
            codice_editore = stringa_record[0:7].strip()
            nuovo_editore, created = Editore.objects.get_or_create(codice_editore=codice_editore)
            nuovo_editore.codice_editore = codice_editore
            nuovo_editore.descrizione_editore = stringa_record[7:37].strip()
            nuovo_editore.annullamento_logico = stringa_record[37:38].strip()
            nuovo_editore.codice_editore_distributore = stringa_record[38:44].strip()
            nuovo_editore.save()
            if created:
                print('IMPORTATO EDITORE: %s - %s' % (nuovo_editore.codice_editore, nuovo_editore.descrizione_editore))
            else:
                print('AGGIORNATO EDITORE: %s - %s' % (nuovo_editore.codice_editore, nuovo_editore.descrizione_editore))

    def aggiorna_tipo_scuola(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            sigla = stringa_record[0:2].strip()
            nuovo_tipo_scuola, created = TipoScuola.objects.get_or_create(sigla=sigla)
            nuovo_tipo_scuola.sigla = sigla
            nuovo_tipo_scuola.descrizione = stringa_record[2:33].strip()
            nuovo_tipo_scuola.tipi_scuola_dipendenti = stringa_record[33:73].strip()
            nuovo_tipo_scuola.annullamento_logico = stringa_record[73:74].strip()
            nuovo_tipo_scuola.ordinamento = stringa_record[74:76].strip()
            nuovo_tipo_scuola.save()
            if created:
                print('IMPORTATO TIPO SCUOLA: %s - %s' % (nuovo_tipo_scuola.sigla, nuovo_tipo_scuola.descrizione))
            else:
                print('AGGIORNATO TIPO SCUOLA: %s - %s' % (nuovo_tipo_scuola.sigla, nuovo_tipo_scuola.descrizione))

    def aggiorna_scuola(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            codice_meccanografico = stringa_record[0:10].strip()
            nuova_scuola, created = Scuola.objects.get_or_create(codice_meccanografico=codice_meccanografico)
            nuova_scuola.codice_meccanografico = codice_meccanografico
            nuova_scuola.codice_precedente = stringa_record[10:20].strip()
            nuova_scuola.nome = stringa_record[20:50].strip()
            nuova_scuola.indirizzo = stringa_record[50:85].strip()
            nuova_scuola.frazione = stringa_record[85:115].strip()
            nuova_scuola.comune = stringa_record[115:150].strip()
            nuova_scuola.cap = stringa_record[170:175].strip()
            nuova_scuola.telefono = stringa_record[175:187].strip()
            nuova_scuola.fax = stringa_record[187:199].strip()
            nuova_scuola.annullamento_logico = stringa_record[199:200].strip()
            codice_sede = stringa_record[200:210].strip()
            if codice_sede:
                try:
                    sede_cercata = SedeScolastica.objects.get(codice_ministeriale=codice_sede)
                    nuova_scuola.sede = sede_cercata
                except SedeScolastica.DoesNotExist:
                    print('SEDE con codice %s per scuola %s NON TROVATA!' % (codice_sede, nuova_scuola.nome))
            nuova_scuola.email = stringa_record[210:290].strip()
            nuova_scuola.sito = stringa_record[290:340].strip()
            nuova_scuola.privata = stringa_record[340:341].strip()
            nuova_scuola.save()
            if created:
                print('IMPORTATA SCUOLA: %s - %s' % (nuova_scuola.codice_meccanografico, nuova_scuola.nome))
            else:
                print('AGGIORNATA SCUOLA: %s - %s' % (nuova_scuola.codice_meccanografico, nuova_scuola.nome))

    def aggiorna_indirizzo(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            tipo_scuola = stringa_record[0:2].strip()
            codice_sperimentazione = stringa_record[2:8].strip()
            codice_specializzazione = stringa_record[8:14].strip()
            nuovo_indirizzo, created = Indirizzo.objects.get_or_create(
                tipo_scuola=tipo_scuola,
                codice_sperimentazione=codice_sperimentazione,
                codice_specializzazione=codice_specializzazione
            )
            nuovo_indirizzo.tipo_scuola = tipo_scuola
            nuovo_indirizzo.codice_sperimentazione = codice_sperimentazione
            nuovo_indirizzo.codice_specializzazione = codice_specializzazione
            nuovo_indirizzo.descrizione = stringa_record[14:64].strip()
            nuovo_indirizzo.save()
            if created:
                print('IMPORTATO INDIRIZZO: %s' % nuovo_indirizzo.descrizione)
            else:
                print('AGGIORNATO INDIRIZZO: %s' % nuovo_indirizzo.descrizione)

    def aggiorna_opera(self, stringa_record):
        if stringa_record:
            codice_prodotto = stringa_record[0:7].strip()
            nuova_opera, created = Opera.objects.get_or_create(codice_prodotto=codice_prodotto)
            nuova_opera.codice_prodotto = codice_prodotto
            nuova_opera.autore = stringa_record[7:57].strip()
            nuova_opera.autore_2 = stringa_record[57:107].strip()
            nuova_opera.autore_3 = stringa_record[107:157].strip()
            nuova_opera.curatore = stringa_record[157:207].strip()
            nuova_opera.curatore_2 = stringa_record[207:257].strip()
            nuova_opera.curatore_3 = stringa_record[257:307].strip()
            nuova_opera.traduttore = stringa_record[307:357].strip()
            nuova_opera.traduttore_2 = stringa_record[357:407].strip()
            nuova_opera.traduttore_3 = stringa_record[407:457].strip()
            nuova_opera.titolo = stringa_record[457:534].strip()
            nuova_opera.numero_volumi = stringa_record[534:535].strip()
            nuova_opera.progressivo_volume = stringa_record[535:536].strip()
            codice_editore = stringa_record[536:542].strip()
            if codice_editore:
                try:
                    editore_cercato = Editore.objects.get(pk=codice_editore)
                    nuova_opera.editore = editore_cercato
                except Editore.DoesNotExist:
                    print('EDITORE con codice %s NON TROVATO!' % codice_editore)
            nuova_opera.anno_edizione = stringa_record[542:546].strip()
            codice_materia = stringa_record[546:551].strip()
            if codice_materia:
                try:
                    materia_cercata = Materia.objects.get(codice=codice_materia)
                    nuova_opera.materia = materia_cercata
                except Materia.DoesNotExist:
                    print('MATERIA con codice %s NON TROVATA!' % codice_materia)
            nuova_opera.codice_fondamentale = stringa_record[551:564].strip()
            nuova_opera.codice_isbn = stringa_record[564:577].strip()
            nuova_opera.codice_2 = stringa_record[577:590].strip()
            nuova_opera.codice_3 = stringa_record[590:603].strip()
            nuova_opera.codice_4 = stringa_record[603:616].strip()
            nuova_opera.codice_5 = stringa_record[616:629].strip()
            codice_tipo_scuola = stringa_record[629:631].strip()
            if codice_tipo_scuola:
                try:
                    tipo_scuola_cercato = TipoScuola.objects.get(pk=codice_tipo_scuola)
                    nuova_opera.tipo_scuola = tipo_scuola_cercato
                except TipoScuola.DoesNotExist:
                    print('TIPO SCUOLA con codice %s NON TROVATO!' % codice_tipo_scuola)
            nuova_opera.prezzo = stringa_record[631:641].strip()
            nuova_opera.annullamento_logico = stringa_record[641:642].strip()
            codice_materia_alternativa = stringa_record[642:647].strip()
            if codice_materia_alternativa:
                try:
                    materia_alternativa_cercata = Materia.objects.get(codice=codice_materia_alternativa)
                    nuova_opera.materia_alternativa = materia_alternativa_cercata
                except Materia.DoesNotExist:
                    pass
                    # print('MATERIA ALTERNATIVA con codice %s NON TROVATA!' % codice_materia_alternativa)
            nuova_opera.sottotitolo = stringa_record[647:723].strip()
            nuova_opera.novita_anno_corrente = stringa_record[723:724].strip()
            nuova_opera.novita_anno_precedente = stringa_record[724:725].strip()
            nuova_opera.fuori_catalogo = stringa_record[725:726].strip()
            nuova_opera.fascicolata = stringa_record[726:727].strip()
            nuova_opera.novita_2_anni_precedenti = stringa_record[727:728].strip()
            codice_modalita_tipo = stringa_record[728:730].strip()
            if codice_modalita_tipo:
                try:
                    modalita_tipo_cercata = ModalitaTipo.objects.get(pk=codice_modalita_tipo)
                    nuova_opera.modalita_tipo = modalita_tipo_cercata
                except ModalitaTipo.DoesNotExist:
                    print('MODALITA TIPO con codice %s NON TROVATA!' % codice_modalita_tipo)
            nuova_opera.url_copertina = stringa_record[730:930].strip()
            nuova_opera.save()
            if created:
                print('IMPORTATA OPERA: %s' % nuova_opera.titolo)
            else:
                print('AGGIORNATA OPERA: %s' % nuova_opera.titolo)

    def aggiorna_file(self, nome_file, esegui=True, nome_dir=DIRECTORY_DEFAULT):
        funzione_inserimento = None
        if nome_file == 'FILE014.TXT':
            funzione_inserimento = self.aggiorna_scuola
        elif nome_file == 'FILE022.TXT':
            funzione_inserimento = self.aggiorna_modalita_tipo
        elif nome_file == 'FILE030.TXT':
            funzione_inserimento = self.aggiorna_editore
        elif nome_file == 'FILE046.TXT':
            funzione_inserimento = self.aggiorna_disciplina
        elif nome_file == 'FILE301.TXT':
            funzione_inserimento = self.aggiorna_materia
        elif nome_file == 'FILE023.TXT':
            funzione_inserimento = self.aggiorna_materia_disciplina
        elif nome_file == 'FILE302.TXT':
            funzione_inserimento = self.aggiorna_tipo_scuola
        elif nome_file == 'FILE306.TXT':
            funzione_inserimento = self.aggiorna_indirizzo
        elif nome_file == 'FILE012.TXT':
            funzione_inserimento = self.aggiorna_opera
        f = open(nome_dir + '/%s' % nome_file, 'r', encoding="latin-1")
        righe = f.readlines()
        numero_righe = len(righe) - 1
        print(
            '%s - TROVATI %s RECORD - INIZIO INSERIMENTO' % (timezone.now(), numero_righe)
        )
        for i, riga in enumerate(righe):
            if i == numero_righe:
                pass
            else:
                if i > 0:
                    funzione_inserimento(riga)
        f.close()

    def add_arguments(self, parser):
        parser.add_argument('-f', '--nome_file', type=str, help='nome del singolo file da importare', )
        parser.add_argument('-z', '--zipfile', type=str, help='nome del file zippato da importare', )

    def handle(self, *args, **kwargs):
        nome_file = kwargs['nome_file']
        nome_file_zip = kwargs['zipfile']
        if nome_file_zip:
            cartella_temp = tempfile.mkdtemp()
            print(cartella_temp)
            zf = zipfile.ZipFile(nome_file_zip)
            zf.extractall(cartella_temp)
            for nome_file in ELENCO_FILES:
                print('%s - AGGIORNAMENTO FILE: %s' % (timezone.now(), nome_file))
                self.aggiorna_file(nome_file=nome_file, nome_dir=cartella_temp)
        else:
            if nome_file:
                if nome_file in ELENCO_FILES:
                    print('%s - AGGIORNAMENTO FILE: %s' % (timezone.now(), nome_file))
                    self.aggiorna_file(nome_file)
            else:
                for nome_file in ELENCO_FILES:
                    print('%s - AGGIORNAMENTO FILE: %s' % (timezone.now(), nome_file))
                    self.aggiorna_file(nome_file)
