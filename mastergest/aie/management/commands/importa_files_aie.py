from django.utils import timezone
from django.core.management.base import BaseCommand

from mastergest.aie.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>itaTip<PERSON>
from mastergest.aie.models import Editore, Disciplina, Materia, MateriaDisciplina
from mastergest.aie.models import TipoScuola, Indirizzo, Classe, Opera, AdozioneStorico

ELENCO_FILES = [
    'FILE006.TXT',  # ANAGRAFICA DELLE SEDI SCOLASTICHE, DELLE ISTITUZIONI SCOLASTICHE
    'FILE014.TXT',  # ANAGRAFICA DELLE SCUOLE, DEI PUNTI DI EROGAZIONE DEL SERVIZIO
    'FILE022.TXT',  # MODALITÀ DI TIPO
    'FILE030.TXT',  # EDITORI
    'FILE046.TXT',  # DISCIPLINE
    'FILE301.TXT',  # MATERIE
    'FILE023.TXT',  # DISCIPLINE/MATERIE
    'FILE302.TXT',  # TIPI SCUOLA
    'FILE306.TXT',  # INDIRIZZI
    #'FILE622.TXT',  # MAPPA DELLE CLASSI
    'FILE622ST.TXT',  # MAPPA DELLE CLASSI
    'FILE012.TXT',  # ANAGRAFICA OPERE
    #'FILE815.TXT',  # ADOZIONI STORICO
    'FILE815ST.TXT',  # ADOZIONI STORICO
]


class Command(BaseCommand):
    help = 'Importa file AIE'

    def importa_sede_scolastica(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuova_sede = SedeScolastica()
            nuova_sede.codice_ministeriale = stringa_record[0:10].strip()
            nuova_sede.nome = stringa_record[10:60].strip()
            nuova_sede.indirizzo = stringa_record[60:95].strip()
            nuova_sede.frazione = stringa_record[95:125].strip()
            nuova_sede.localita = stringa_record[125:160].strip()
            nuova_sede.cap = stringa_record[160:165].strip()
            nuova_sede.telefono = stringa_record[165:177].strip()
            nuova_sede.fax = stringa_record[177:189].strip()
            nuova_sede.annullamento_logico = stringa_record[189:190].strip()
            return nuova_sede
            print('IMPORTATA SEDE: %s - %s' % (nuova_sede.codice_ministeriale, nuova_sede.nome))

    def importa_modalita_tipo(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuova_modalita_tipo = ModalitaTipo()
            nuova_modalita_tipo.codice = stringa_record[0:3].strip()
            nuova_modalita_tipo.descrizione = stringa_record[3:259].strip()
            return nuova_modalita_tipo
            print('IMPORTATA MODALITA TIPO: %s - %s' % (nuova_modalita_tipo.codice, nuova_modalita_tipo.descrizione))

    def importa_disciplina(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuova_disciplina = Disciplina()
            nuova_disciplina.codice = stringa_record[0:4].strip()
            nuova_disciplina.descrizione = stringa_record[4:104].strip()
            # print('IMPORTATA DISCIPLINA: %s - %s' % (nuova_disciplina.codice, nuova_disciplina.descrizione))
            return nuova_disciplina

    def importa_materia(self, stringa_record):
        if stringa_record:
            nuova_materia = Materia()
            nuova_materia.codice = stringa_record[0:6].strip()
            nuova_materia.descrizione_1 = stringa_record[6:49].strip()
            nuova_materia.descrizione_2 = stringa_record[49:102].strip()
            nuova_materia.annullamento_logico = stringa_record[102:103].strip()
            # print('IMPORTATA MATERIA: %s - %s' % (nuova_materia.codice, nuova_materia.descrizione_1))
            return nuova_materia

    def importa_materia_disciplina(self, stringa_record):
        if stringa_record:
            materia_cercata = None
            codice_materia = stringa_record[0:6].strip()
            if codice_materia:
                try:
                    materia_cercata = Materia.objects.get(codice=codice_materia)
                except Materia.DoesNotExist:
                    print('MATERIA con codice %s NON TROVATA!' % codice_materia)
            disciplina_cercata = None
            codice_disciplina = stringa_record[6:10].strip()
            if codice_disciplina:
                try:
                    disciplina_cercata = Disciplina.objects.get(codice=codice_disciplina)
                except Disciplina.DoesNotExist:
                    print('DISCIPLINA con codice %s NON TROVATA!' % codice_disciplina)
            if materia_cercata and disciplina_cercata:
                nuova_materia_disciplina = MateriaDisciplina()
                nuova_materia_disciplina.materia = materia_cercata
                nuova_materia_disciplina.disciplina = disciplina_cercata
                # print('IMPORTATA MATERIA-DISCIPLINA: %s - %s' % (materia_cercata, disciplina_cercata))
                return nuova_materia_disciplina

    def importa_editore(self, stringa_record):
        if stringa_record:
            nuovo_editore = Editore()
            nuovo_editore.codice_editore = stringa_record[0:7].strip()
            nuovo_editore.descrizione_editore = stringa_record[7:37].strip()
            nuovo_editore.annullamento_logico = stringa_record[37:38].strip()
            nuovo_editore.codice_editore_distributore = stringa_record[38:44].strip()
            # print('IMPORTATO EDITORE: %s - %s' % (nuovo_editore.codice_editore, nuovo_editore.descrizione_editore))
            return nuovo_editore

    def importa_tipo_scuola(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuovo_tipo_scuola = TipoScuola()
            nuovo_tipo_scuola.sigla = stringa_record[0:2].strip()
            nuovo_tipo_scuola.descrizione = stringa_record[2:33].strip()
            nuovo_tipo_scuola.tipi_scuola_dipendenti = stringa_record[33:73].strip()
            nuovo_tipo_scuola.annullamento_logico = stringa_record[73:74].strip()
            nuovo_tipo_scuola.ordinamento = stringa_record[74:76].strip()
            # print('IMPORTATO TIPO SCUOLA: %s - %s' % (nuovo_tipo_scuola.sigla, nuovo_tipo_scuola.descrizione))
            return nuovo_tipo_scuola

    def importa_scuola(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuova_scuola = Scuola()
            nuova_scuola.codice_meccanografico = stringa_record[0:10].strip()
            nuova_scuola.codice_precedente = stringa_record[10:20].strip()
            nuova_scuola.nome = stringa_record[20:70].strip()
            nuova_scuola.indirizzo = stringa_record[70:105].strip()
            nuova_scuola.frazione = stringa_record[105:135].strip()
            nuova_scuola.comune = stringa_record[135:170].strip()
            nuova_scuola.cap = stringa_record[170:175].strip()
            nuova_scuola.telefono = stringa_record[175:187].strip()
            nuova_scuola.fax = stringa_record[187:199].strip()
            nuova_scuola.annullamento_logico = stringa_record[199:200].strip()
            codice_sede = stringa_record[200:210].strip()
            if codice_sede:
                try:
                    sede_cercata = SedeScolastica.objects.get(codice_ministeriale=codice_sede)
                    nuova_scuola.sede = sede_cercata
                except SedeScolastica.DoesNotExist:
                    print('SEDE con codice %s per scuola %s NON TROVATA!' % (codice_sede, nuova_scuola.nome))
            nuova_scuola.email = stringa_record[210:290].strip()
            nuova_scuola.sito = stringa_record[290:340].strip()
            nuova_scuola.privata = stringa_record[340:341].strip()
            # print('IMPORTATA SCUOLA: %s - %s' % (nuova_scuola.codice_meccanografico, nuova_scuola.nome))
            return nuova_scuola

    def importa_indirizzo(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuovo_indirizzo = Indirizzo()
            nuovo_indirizzo.tipo_scuola = stringa_record[0:2].strip()
            nuovo_indirizzo.codice_sperimentazione = stringa_record[2:8].strip()
            nuovo_indirizzo.codice_specializzazione = stringa_record[8:14].strip()
            nuovo_indirizzo.descrizione = stringa_record[14:64].strip()
            # print('IMPORTATO INDIRIZZO: %s' % nuovo_indirizzo.descrizione)
            return nuovo_indirizzo

    def importa_opera(self, stringa_record):
        if stringa_record:
            nuova_opera = Opera()
            nuova_opera.codice_prodotto = stringa_record[0:7].strip()
            nuova_opera.autore = stringa_record[7:57].strip()
            nuova_opera.autore_2 = stringa_record[57:107].strip()
            nuova_opera.autore_3 = stringa_record[107:157].strip()
            nuova_opera.curatore = stringa_record[157:207].strip()
            nuova_opera.curatore_2 = stringa_record[207:257].strip()
            nuova_opera.curatore_3 = stringa_record[257:307].strip()
            nuova_opera.traduttore = stringa_record[307:357].strip()
            nuova_opera.traduttore_2 = stringa_record[357:407].strip()
            nuova_opera.traduttore_3 = stringa_record[407:457].strip()
            nuova_opera.titolo = stringa_record[457:534].strip()
            nuova_opera.numero_volumi = stringa_record[534:535].strip()
            nuova_opera.progressivo_volume = stringa_record[535:536].strip()
            codice_editore = stringa_record[536:542].strip()
            if codice_editore:
                try:
                    editore_cercato = Editore.objects.get(pk=codice_editore)
                    nuova_opera.editore = editore_cercato
                except Editore.DoesNotExist:
                    print('EDITORE con codice %s NON TROVATO!' % codice_editore)
            nuova_opera.anno_edizione = stringa_record[542:546].strip()
            codice_materia = stringa_record[546:551].strip()
            if codice_materia:
                try:
                    materia_cercata = Materia.objects.get(codice=codice_materia)
                    nuova_opera.materia = materia_cercata
                except Materia.DoesNotExist:
                    print('MATERIA con codice %s NON TROVATA!' % codice_materia)
            nuova_opera.codice_fondamentale = stringa_record[551:564].strip()
            nuova_opera.codice_isbn = stringa_record[564:577].strip()
            nuova_opera.codice_2 = stringa_record[577:590].strip()
            nuova_opera.codice_3 = stringa_record[590:603].strip()
            nuova_opera.codice_4 = stringa_record[603:616].strip()
            nuova_opera.codice_5 = stringa_record[616:629].strip()
            codice_tipo_scuola = stringa_record[629:631].strip()
            if codice_tipo_scuola:
                try:
                    tipo_scuola_cercato = TipoScuola.objects.get(pk=codice_tipo_scuola)
                    nuova_opera.tipo_scuola = tipo_scuola_cercato
                except TipoScuola.DoesNotExist:
                    print('TIPO SCUOLA con codice %s NON TROVATO!' % codice_tipo_scuola)
            nuova_opera.prezzo = stringa_record[631:641].strip()
            nuova_opera.annullamento_logico = stringa_record[641:642].strip()
            codice_materia_alternativa = stringa_record[642:647].strip()
            if codice_materia_alternativa:
                try:
                    materia_alternativa_cercata = Materia.objects.get(codice=codice_materia_alternativa)
                    nuova_opera.materia_alternativa = materia_alternativa_cercata
                except Materia.DoesNotExist:
                    pass
                    # print('MATERIA ALTERNATIVA con codice %s NON TROVATA!' % codice_materia_alternativa)
            nuova_opera.sottotitolo = stringa_record[647:723].strip()
            nuova_opera.novita_anno_corrente = stringa_record[723:724].strip()
            nuova_opera.novita_anno_precedente = stringa_record[724:725].strip()
            nuova_opera.fuori_catalogo = stringa_record[725:726].strip()
            nuova_opera.fascicolata = stringa_record[726:727].strip()
            nuova_opera.novita_2_anni_precedenti = stringa_record[727:728].strip()
            codice_modalita_tipo = stringa_record[728:730].strip()
            if codice_modalita_tipo:
                try:
                    modalita_tipo_cercata = ModalitaTipo.objects.get(pk=codice_modalita_tipo)
                    nuova_opera.modalita_tipo = modalita_tipo_cercata
                except ModalitaTipo.DoesNotExist:
                    print('MODALITA TIPO con codice %s NON TROVATA!' % codice_modalita_tipo)
            nuova_opera.url_copertina = stringa_record[730:930].strip()
            # print('IMPORTATA OPERA: %s' % nuova_opera.titolo)
            return nuova_opera

    def importa_classe(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuova_classe = Classe()
            codice_scuola = stringa_record[0:10].strip()
            scuola_cercata = None
            if codice_scuola:
                try:
                    scuola_cercata = Scuola.objects.get(codice_meccanografico=codice_scuola)
                except Scuola.DoesNotExist:
                    print('SCUOLA con codice %s NON TROVATA!' % codice_scuola)
            if scuola_cercata:
                nuova_classe.scuola = scuola_cercata
                nuova_classe.sezione = stringa_record[10:14].strip()
                nuova_classe.tipo_scuola = stringa_record[14:16].strip()
                nuova_classe.classe = stringa_record[16:17].strip()
                nuova_classe.alunni = stringa_record[17:19].strip()
                nuova_classe.codice_specializzazione = stringa_record[19:25].strip()
                nuova_classe.codice_sperimentazione = stringa_record[25:31].strip()
                nuova_classe.classe_id = stringa_record[31:40].strip()
                try:
                    if nuova_classe.tipo_scuola:
                        indirizzo_cercato = Indirizzo.objects.filter(
                            tipo_scuola=nuova_classe.tipo_scuola
                        )
                    if nuova_classe.codice_sperimentazione:
                        indirizzo_cercato = indirizzo_cercato.filter(
                            codice_sperimentazione=nuova_classe.codice_sperimentazione,
                        )
                    if nuova_classe.codice_specializzazione:
                        indirizzo_cercato = indirizzo_cercato.filter(
                            codice_specializzazione=nuova_classe.codice_specializzazione,
                        )
                    if indirizzo_cercato:
                        if indirizzo_cercato.count() == 1:
                            nuova_classe.indirizzo = indirizzo_cercato[0]
                except Indirizzo.DoesNotExist:
                    print(
                        'INDIRIZZO con %s-%s-%s NON TROVATO!' % (
                            nuova_classe.tipo_scuola,
                            nuova_classe.codice_specializzazione,
                            nuova_classe.codice_sperimentazione
                        )
                    )
                # print('IMPORTATA CLASSE: %s' % nuova_classe)
                return nuova_classe

    def importa_adozione(self, stringa_record):
        if stringa_record:
            stringa_record = stringa_record.strip()
            nuova_adozione = AdozioneStorico()
            codice_scuola = stringa_record[0:10].strip()
            nuova_adozione.codice_scuola = codice_scuola
            # if codice_scuola:
            #     try:
            #         scuola_cercata = Scuola.objects.get(pk=codice_scuola)
            #         nuova_adozione.scuola = scuola_cercata
            #     except Scuola.DoesNotExist:
            #         print('SCUOLA con codice %s NON TROVATA!' % codice_scuola)
            nuova_adozione.classe = stringa_record[10:11].strip()
            nuova_adozione.sezione = stringa_record[11:15].strip()
            nuova_adozione.tipo_scuola = stringa_record[15:17].strip()
            nuova_adozione.codice_specializzazione = stringa_record[17:23].strip()
            nuova_adozione.codice_sperimentazione = stringa_record[23:29].strip()
            # try:
            #     if nuova_adozione.tipo_scuola:
            #         indirizzo_cercato = Indirizzo.objects.filter(
            #             tipo_scuola=nuova_adozione.tipo_scuola
            #         )
            #     if nuova_adozione.codice_sperimentazione:
            #         indirizzo_cercato = indirizzo_cercato.filter(
            #             codice_sperimentazione=nuova_adozione.codice_sperimentazione,
            #         )
            #     if nuova_adozione.codice_specializzazione:
            #         indirizzo_cercato = indirizzo_cercato.filter(
            #             codice_specializzazione=nuova_adozione.codice_specializzazione,
            #         )
            #     if indirizzo_cercato:
            #         if indirizzo_cercato.count() == 1:
            #             nuova_adozione.indirizzo = indirizzo_cercato[0]
            # except Indirizzo.DoesNotExist:
            #     print(
            #         'INDIRIZZO con %s-%s-%s NON TROVATO!' % (
            #             nuova_adozione.tipo_scuola,
            #             nuova_adozione.codice_specializzazione,
            #             nuova_adozione.codice_sperimentazione
            #         )
            #     )
            codice_opera = stringa_record[29:35].strip()
            nuova_adozione.codice_opera = codice_opera
            # if codice_opera:
            #     try:
            #         opera_cercata = Opera.objects.get(pk=codice_opera)
            #         nuova_adozione.opera = opera_cercata
            #     except Opera.DoesNotExist:
            #         print('OPERA con codice %s NON TROVATA!' % codice_opera)
            nuova_adozione.consigliato = stringa_record[35:36].strip()
            nuova_adozione.in_possesso = stringa_record[36:37].strip()
            codice_materia = stringa_record[37:42].strip()
            nuova_adozione.codice_materia = codice_materia
            # if codice_materia:
            #     try:
            #         materia_cercata = Materia.objects.get(pk=codice_materia)
            #         nuova_adozione.materia = materia_cercata
            #     except Materia.DoesNotExist:
            #         print('MATERIA con codice %s NON TROVATA!' % codice_materia)
            codice_editore = stringa_record[42:48].strip()
            nuova_adozione.codice_editore = codice_editore
            # if codice_editore:
            #     try:
            #         editore_cercato = Editore.objects.get(pk=codice_editore)
            #         nuova_adozione.editore = editore_cercato
            #     except Editore.DoesNotExist:
            #         print('EDITORE con codice %s NON TROVATO!' % codice_editore)
            nuova_adozione.isbn = stringa_record[48:61].strip()
            nuova_adozione.in_uso = stringa_record[61:62].strip()
            if stringa_record[62:66].strip():
                nuova_adozione.anno_adozione = stringa_record[62:66].strip()
            # print('IMPORTATA ADOZIONE STORICO : %s' % nuova_adozione)
            return nuova_adozione

    def importa_file(self, nome_file, esegui=True):
        modello = None
        funzione_inserimento = None
        NUMERO_INSERIMENTI_BULK = 1000
        if nome_file == 'FILE006.TXT':
            modello = SedeScolastica
            funzione_inserimento = self.importa_sede_scolastica
        elif nome_file == 'FILE014.TXT':
            modello = Scuola
            funzione_inserimento = self.importa_scuola
        elif nome_file == 'FILE022.TXT':
            modello = ModalitaTipo
            funzione_inserimento = self.importa_modalita_tipo
        elif nome_file == 'FILE030.TXT':
            modello = Editore
            funzione_inserimento = self.importa_editore
        elif nome_file == 'FILE046.TXT':
            modello = Disciplina
            funzione_inserimento = self.importa_disciplina
        elif nome_file == 'FILE301.TXT':
            modello = Materia
            funzione_inserimento = self.importa_materia
        elif nome_file == 'FILE023.TXT':
            modello = MateriaDisciplina
            funzione_inserimento = self.importa_materia_disciplina
        elif nome_file == 'FILE302.TXT':
            modello = TipoScuola
            funzione_inserimento = self.importa_tipo_scuola
        elif nome_file == 'FILE306.TXT':
            modello = Indirizzo
            funzione_inserimento = self.importa_indirizzo
        elif nome_file in ['FILE622.TXT', 'FILE622ST.TXT']:
            modello = Classe
            funzione_inserimento = self.importa_classe
            NUMERO_INSERIMENTI_BULK = 10000
        elif nome_file == 'FILE012.TXT':
            modello = Opera
            funzione_inserimento = self.importa_opera
            NUMERO_INSERIMENTI_BULK = 10000
        elif nome_file in ['FILE815.TXT', 'FILE815ST.TXT']:
            modello = AdozioneStorico
            funzione_inserimento = self.importa_adozione
            NUMERO_INSERIMENTI_BULK = 10000
        if modello:
            modello.objects.all().delete()
        # f = open('/tmp/aie/%s' % nome_file, 'r', encoding="latin-1")
        f = open('/home/<USER>/Scrivania/AIE/catalogo_2025/%s' % nome_file, 'r', encoding="latin-1")
        righe = f.readlines()
        numero_righe = len(righe) - 1
        elenco_record = []
        print(
            '%s - TROVATI %s RECORD - INIZIO INSERIMENTO' % (timezone.now(), numero_righe)
        )
        numero_inseriti = 0
        for i, riga in enumerate(righe):
            if i == numero_righe:
                pass
            else:
                if i > 0:
                    nuovo_record = funzione_inserimento(riga)
                    if nuovo_record:
                        elenco_record.append(nuovo_record)
                        if len(elenco_record) == NUMERO_INSERIMENTI_BULK:
                            numero_inseriti += NUMERO_INSERIMENTI_BULK
                            modello.objects.bulk_create(elenco_record)
                            percentuale = int((numero_inseriti / numero_righe) * 100)
                            print('%s - ############> INSERITI %s su %s (%s%%) <#############' % (
                                timezone.now(), numero_inseriti, numero_righe, percentuale)
                            )
                            elenco_record = []
        if elenco_record:
            rimasti = len(elenco_record)
            modello.objects.bulk_create(elenco_record)
            print('%s - ##########> INSERITI ULTIMI %s record <###########' % (
                timezone.now(), rimasti)
            )
        f.close()

    def add_arguments(self, parser):
        parser.add_argument('-f', '--file', type=str, help='nome del singolo file da importare', )

    def handle(self, *args, **kwargs):
        nome_file = kwargs['file']
        print('nome file:', nome_file)
        if nome_file:
            if nome_file in ELENCO_FILES:
                print('%s - IMPORTAZIONE FILE: %s' % (timezone.now(), nome_file))
                self.importa_file(nome_file)
            else:
                print('ERRORE! il file:', nome_file, 'non incluso tra quelli importabili!')
        else:
            for nome_file in ELENCO_FILES:
                print('%s - IMPORTAZIONE FILE: %s' % (timezone.now(), nome_file))
                self.importa_file(nome_file)
