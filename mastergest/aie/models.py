from django.db import models

# -----------------------------------------------------------------------------
# 1 file006.txt: aggiornato al 20 FEBBRAIO 2020
# ANAGRAFICA DELLE SEDI SCOLASTICHE, DELLE ISTITUZIONI SCOLASTICHE

# Campo  Tipo Pos.  Dim.Descrizione             Valori ammessi
# COSEDE Alf. 1     10  Cod.minist.della sede
# NOMSED Alf. 11    50  Nome
# INDSED Alf. 61    35  Indirizzo
# FRZSED Alf. 96    30  Frazione
# LOCSED Alf. 126   35  Località
# CAPSED Alf. 161   5   CAP
# TELSED Alf. 166   12  Telefono
# FAXSED Alf. 178   12  Fax
# ANN006 Alf. 190   1   Annullamento logico     Campo vuoto / ”A”

# ATTENZIONE: le adozioni non possono essere caricate sul codice della sede scolastica.
# I codici scuola utilizzabili per le adozioni sono nel FILE014
# Chiave univoca: COSEDE


class SedeScolastica(models.Model):
    codice_ministeriale = models.CharField(max_length=10, primary_key=True)
    nome = models.CharField(max_length=200)
    indirizzo = models.CharField(max_length=200, null=True, blank=True)
    frazione = models.CharField(max_length=200, null=True, blank=True)
    localita = models.CharField(max_length=200, null=True, blank=True)
    cap = models.CharField(max_length=200, null=True, blank=True)
    telefono = models.CharField(max_length=200, null=True, blank=True)
    fax = models.CharField(max_length=200, null=True, blank=True)
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'sedi scolastiche'
        ordering = ('nome',)

    def __str__(self):
        return str(self.nome)
# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 3 file014.txt: aggiornato al 20 FEBBRAIO 2020
# ANAGRAFICA DELLE SCUOLE, DEI PUNTI DI EROGAZIONE DEL SERVIZIO

# Campo   Tipo    Pos.Dim.Descrizione                         Valori ammessi
# COSCUO  Alf.    1   10  Codice meccanografico della scuola
# EXCODS  Alf.    11  10  Ex codice scuola (anno precedente)
# NOMSCU  Alf.    21  50  Nome
# INDSCU  Alf.    71  35  Indirizzo
# FRZSCU  Alf.    106 30  Frazione
# LOCSCU  Alf.    136 35  Comune
# CAPSCU  Alf.    171 5   CAP
# TELEFO  Alf.    176 12  Telefono
# FAX     Alf.    188 12  Fax
# ANN014  Alf.    200 1   Annullamento logico Campo vuoto / ”A”
# COSEDE  Alf.    201 10  Codice meccanografico della sede    [FILE006.COSEDE]
# EMAIL   Alf.    211 80  Indirizzo e-mail
# SITO    Alf.    291 50  Indirizzo sito web
# PRIVATA Alf.    341 1   Scuola privata  “0”: statale, “1”: privata

class Scuola(models.Model):
    codice_meccanografico = models.CharField(max_length=200, primary_key=True)
    codice_precedente = models.CharField(max_length=200, null=True, blank=True)
    nome = models.CharField(max_length=200)
    indirizzo = models.CharField(max_length=200, null=True, blank=True)
    frazione = models.CharField(max_length=200, null=True, blank=True)
    comune = models.CharField(max_length=200, null=True, blank=True)
    cap = models.CharField(max_length=200, null=True, blank=True)
    telefono = models.CharField(max_length=200, null=True, blank=True)
    fax = models.CharField(max_length=200, null=True, blank=True)
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)
    sede = models.ForeignKey(SedeScolastica, null=True, blank=True, on_delete=models.CASCADE)
    email = models.CharField(max_length=200, null=True, blank=True)
    sito = models.CharField(max_length=200, null=True, blank=True)
    privata = models.CharField(max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'scuole'
        ordering = ('nome',)

    def __str__(self):
        return str(self.nome)

# -----------------------------------------------------------------------------

# -----------------------------------------------------------------------------
# 4 file022.txt: aggiornato al 30 maggio 2017
# MODALITÀ DI TIPO

# Campo   Tipo    Pos.    Dim.    Descrizione                     Valori ammessi
# CODICE  Alf.    1       3       Codice identificativo           Caratteri numerici
# DESCR   Alf.    4       256     Descrizione modalità di tipo    Caratteri alfanumerici

# Chiave univoca: CODICE


class ModalitaTipo(models.Model):
    codice = models.CharField(max_length=200, primary_key=True)
    descrizione = models.CharField(max_length=500)

    class Meta:
        verbose_name_plural = 'modalita tipi'
        ordering = ('descrizione',)

    def __str__(self):
        return str(self.descrizione)

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 6 file030.txt: aggiornato al 30 maggio 2017
# EDITORI
# Campo   Tipo    Pos.    Dim.    Descrizione                 Valori ammessi
# CODEDI  Num.    2       6       Codice editore
# DESEDI  Alf.    8       30      Descrizione editore
# ANN030  Alf.    38      1       Annullamento logico (“ “/A) Campo vuoto / ”A”
# CODDIS  Num.    39      6       Codice editore distributore
# Chiave univoca: CODEDI.


class Editore(models.Model):
    codice_editore = models.PositiveIntegerField(primary_key=True)
    descrizione_editore = models.CharField(max_length=200)
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)
    codice_editore_distributore = models.PositiveIntegerField(null=True, blank=True)

    class Meta:
        verbose_name_plural = 'editori'
        ordering = ('descrizione_editore',)

    def __str__(self):
        return str(self.descrizione_editore)

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 7 file046.txt: aggiornato al 30 maggio 2017
# DISCIPLINE

# Campo   Tipo    Pos.    Dim.    Descrizione        Valori ammessi
# IDDISC  Alf.    1       4       Codice disciplina  Vedi elenco sotto riportato [1]
# DESDIS  Alf.    5       100     Descrizione disciplina

# I valori permessi per il campo IDDISC sono i seguenti:
# Scuola primaria: EXXX dove XXX è un codice numerico a 3 cifre
# Scuola secondaria di I grado: MXXX dove XXX è un codice numerico a 3 cifre
# Scuola secondaria di II grado: DXXX dove XXX è un codice numerico a 3 cifre

# Chiave univoca: IDDISC

class Disciplina(models.Model):
    codice = models.CharField(max_length=200, primary_key=True)
    descrizione = models.CharField(max_length=500)

    class Meta:
        verbose_name_plural = 'discipline'
        ordering = ('descrizione',)

    def __str__(self):
        return str(self.descrizione)

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 8 file301.txt: aggiornato al 30 maggio 2017
# MATERIE
# Campo   Tipo  Pos.    Dim.    Descrizione                 Valori ammessi
# CODMAT  Num.  2       5       Codice materia              Vedi elenco sotto riportato
# DESM1   Alf.  7       43      Descrizione materia, 1°
# DESM2   Alf.  50      43      Descrizione materia, 2°
# ANN301  Alf.  93      1       Annullamento logico         Campo vuoto / ”A”

# I valori contenuti nel campo CODMAT seguono la seguente convenzione:
# Scuola primaria: CODMAT compreso tra 0 e 9999
# Scuola secondaria di I grado: CODMAT compreso tra 10000 e 19999
# Scuola secondaria di II grado: CODMAT maggiore o uguale a 20000

# Chiave univoca: CODMAT


class Materia(models.Model):
    codice = models.PositiveIntegerField(primary_key=True)
    descrizione_1 = models.CharField(max_length=200)
    descrizione_2 = models.CharField(max_length=200, null=True, blank=True)
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'materie'
        ordering = ('descrizione_1', 'descrizione_2')

    def __str__(self):
        return str(self.descrizione_1)

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 5 file023.txt: aggiornato al 30 maggio 2017
# DISCIPLINE/MATERIE

# Campo   Tipo    Pos.    Dim.    Descrizione         Valori ammessi
# CODMAT  Num.    2       5       Codice materia      [FILE301.CODMAT]
# IDDISC  Alf.    7       4       Codice disciplina   [FILE046.IDDISC]

# Chiave univoca: CODMAT, IDDISC
# (N.B. la corrispondenza tra CODMAT ed IDDISC è molti-ad-uno, ossia più codici
#  materia possono essere associati alla stessa disciplina ma non vale il viceversa)


class MateriaDisciplina(models.Model):
    materia = models.ForeignKey(Materia, on_delete=models.CASCADE)
    disciplina = models.ForeignKey(Disciplina, on_delete=models.CASCADE)

    def __str__(self):
        return str('%s - %s' % (self.materia, self.disciplina))

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 9 file302.txt: aggiornato al 30 maggio 2017
# TIPI SCUOLA
# Campo   Tipo    Pos.  Dim.    Descrizione                 Valori ammessi
# TIPSCU  Alf.    1     2       Sigla tipo scuola
# DESIS   Alf.    3     31      Descrizione
# VALID   Alf.    34    40      Elenco tipi scuola dipendenti
# ANN302  Alf.    74    1       Annullamento logico         Campo vuoto / ”A”
# ATIPIS  Alf.    75    2       Ordinamento tipo scuola (per le stampe)

# Il file viene utilizzato dall’anagrafica delle opere e dai programmi di inserimento delle adozioni.
# Il file contiene sia i singoli tipi scuola (es. MM, NT, ecc.) sia i raggruppamenti di tipi scuola (K1, K2, K3, K4, K5).
# Relativamente ai soli raggruppamenti il campo VALID contiene l’elenco dei tipi scuola che fanno riferimento al raggruppamento in questione.

# Chiave univoca: TIPSCU
# Casi particolari:
# TIPSCU = NO solo per classi 1 e 2
# TIPSCU = NT solo per classi 3, 4 e 5

class TipoScuola(models.Model):
    sigla = models.CharField(max_length=200, primary_key=True)
    descrizione = models.CharField(max_length=200)
    tipi_scuola_dipendenti = models.CharField(max_length=200, null=True, blank=True)
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)
    ordinamento = models.CharField(max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'tipi scuola'
        ordering = ('descrizione', 'sigla')

    def __str__(self):
        return str(self.descrizione)

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 10 file306.txt: aggiornato al 30 maggio 2017
# COMBINAZIONI DI TIPO SCUOLA / SPERIMENTAZIONE / SPECIALIZZAZIONE
# Campo   Tipo    Pos.  Dim.    Descrizione                 Valori ammessi
# TIPSCU  Alf.    1     2       Sigla tipo scuola           “EE”, “MM”, “NO”, “NT”
# CODSPR  Alf.    3     6       Codice sperimentazione
# CODSPC  Alf.    9     6       Codice specializzazione
# DESCR   Alf.    15    50      Descrizione
# ANN     Alf.    65    1       Annullamento logico         Campo vuoto / ”A”

# Il file viene utilizzato per il controllo della mappa della scuola.

# Chiave univoca: TIPSCU, CODSPR, CODSPC

class Indirizzo(models.Model):
    tipo_scuola = models.CharField(max_length=200, null=True, blank=True)
    codice_sperimentazione = models.CharField(max_length=200, null=True, blank=True)
    codice_specializzazione = models.CharField(max_length=200, null=True, blank=True)
    descrizione = models.CharField(max_length=200, null=True, blank=True)
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'indirizzi'
        ordering = ('tipo_scuola', 'descrizione')

    def __str__(self):
        return str(self.descrizione)

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 11 file622.txt: aggiornato al 30 maggio 2017
# MAPPA DELLE CLASSI

# Campo     Tipo    Pos.   Dim. Descrizione                         Valori ammessi
# COSCUO    Alf.    1      10   Codice ministeriale della scuola    [FILE014.COSCUO]
# SEZION    Alf.    11     4    Sezione                             A~Z
# TIPSCU    Alf.    15     2    Tipo scuola                         “EE”,”MM”,”NO”,”NT”
# CLASSE    Num.    17     1    Classe                              Compresi tra 1 e 6
# ALUNNI    Num.    18     2    N° Alunni                           Compresi tra 1 e 33
# CODSPC *  Alf.    20     6    Codice specializzazione             [FILE306.CODSPC]
# CODSPR *  Alf.    26     6    Codice sperimentazione              [FILE306.CODSPR]
# CLID      Num.    32     9    Identificativo univoco classe       Campo vuoto se classe di nuova formazione / valore presente nello storico


# * a seconda della combinazione di TIPSCU-CODSPC-CODSPR uno dei due campi CODSPC/CODSPR potrebbe essere vuoto.
# Chiave univoca: COSCUO, SEZION, TIPSCU, CLASSE, CODSPC, CODSPR

class Classe(models.Model):
    scuola = models.ForeignKey(Scuola, on_delete=models.CASCADE)
    sezione = models.CharField(max_length=200, null=True, blank=True)
    tipo_scuola = models.CharField(max_length=200, null=True, blank=True)
    suffisso = models.CharField(max_length=200, null=True, blank=True)
    classe = models.PositiveIntegerField(null=True, blank=True)
    alunni = models.PositiveIntegerField(null=True, blank=True)
    codice_sperimentazione = models.CharField(max_length=200, null=True, blank=True)
    codice_specializzazione = models.CharField(max_length=200, null=True, blank=True)
    indirizzo = models.ForeignKey(Indirizzo, null=True, blank=True, on_delete=models.CASCADE)
    classe_id = models.PositiveIntegerField(null=True, blank=True)

    class Meta:
        verbose_name_plural = 'classi'
        ordering = ('scuola', 'classe', 'sezione', 'suffisso', 'indirizzo')

    def __str__(self):
        return str('%s %s %s - %s' % (self.classe, self.sezione, self.indirizzo, self.scuola))

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 2 file012.txt: aggiornato al 30 maggio 2017
# ANAGRAFICA OPERE

# CODPRO      Num. 2      6   Codice prodotto Codice univoco del record
# AUTORE      Alf. 8      50  Autore
# AUTOR2      Alf. 58     50  Autore 2
# AUTOR3      Alf. 108    50  Autore 3
# CUR1        Alf. 158    50  Curatore
# CUR2        Alf. 208    50  Curatore 2
# CUR3        Alf. 258    50  Curatore 3
# TRAD1       Alf. 308    50  Traduttore
# TRAD2       Alf. 358    50  Traduttore 2
# TRAD3       Alf. 408    50  Traduttore 3
# TITOLO      Alf. 458    77  Titolo
# NUMVOL      Num. 535    1   Nr. volumi dell’opera
# PRGVOL      Num. 536    1   Nr. progressivo del volume
# CODEDI      Num. 537    6   Codice editore [FILE030.CODEDI]
# ANNO        Num. 543    4   Anno edizione
# CODMAT      Num. 547    5   Codice materia [FILE301.CODMAT]
# CODOPE      Alf. 552    13  Codice fondamentale
# COD1        Alf. 565    13  Codice 1 (ISBN)
# COD2        Alf. 578    13  Codice 2
# COD3        Alf. 591    13  Codice 3
# COD4        Alf. 604    13  Codice 4
# COD5        Alf. 617    13  Codice 5
# TIPSCU      Alf. 630    2   Codice tipo scuola [FILE302.TIPSCU]
# PREZZO      Num. 633    9   Prezzo in centesimi di Euro
# ANN012      Alf. 642    1   Annullamento logico Campo vuoto / ”A”
# CODMA2      Num. 643    5   Codice materia alternativa [FILE301.CODMAT]
# SOTTOT      Alf. 648    76  Sottotitolo
# SENOVI      Alf. 724    1   Se novità anno corrente Campo vuoto / “S”
# SENOVP      Alf. 725    1   Se novità anno precedente Campo vuoto / “S”
# FUORCT      Alf. 726    1   Fuori catalogo Campo vuoto / “F” / “D”
# FASCIC      Alf. 727    1   Se opera fascicolata o tomo Campo vuoto / “F” / “T”
# SENOPP      Alf. 728    1   Se novità 2 anni precedentiCampo vuoto / “S”
# TIPO        Alf. 729    3   Codice modalità di tipo [FILE022.CODICE]
# IMG_SRC     Alf. 732    200 Url copertina

# Chiave univoca: CODPRO
# Note:
# Valori campo “FUORCT”:
# “vuoto”: titolo in catalogo
# “D”: titolo a disponibilità limitata
# “F”: titolo fuori catalogo

class Opera(models.Model):
    codice_prodotto = models.CharField(max_length=200, primary_key=True)
    autore = models.CharField(max_length=200)
    autore_2 = models.CharField(max_length=200, null=True, blank=True)
    autore_3 = models.CharField(max_length=200, null=True, blank=True)
    curatore = models.CharField(max_length=200, null=True, blank=True)
    curatore_2 = models.CharField(max_length=200, null=True, blank=True)
    curatore_3 = models.CharField(max_length=200, null=True, blank=True)
    traduttore = models.CharField(max_length=200, null=True, blank=True)
    traduttore2 = models.CharField(max_length=200, null=True, blank=True)
    traduttore_3 = models.CharField(max_length=200, null=True, blank=True)
    titolo = models.CharField(max_length=200, null=True, blank=True)
    numero_volumi = models.PositiveIntegerField(null=True, blank=True)
    progressivo_volume = models.PositiveIntegerField(null=True, blank=True)
    editore = models.ForeignKey(Editore, null=True, blank=True, on_delete=models.CASCADE)
    anno_edizione = models.PositiveIntegerField(null=True, blank=True)
    materia = models.ForeignKey(Materia, null=True, blank=True, on_delete=models.CASCADE)
    codice_fondamentale = models.CharField(max_length=200, null=True, blank=True)
    codice_isbn = models.CharField(max_length=200, null=True, blank=True)
    codice_2 = models.CharField(max_length=200, null=True, blank=True)
    codice_3 = models.CharField(max_length=200, null=True, blank=True)
    codice_4 = models.CharField(max_length=200, null=True, blank=True)
    codice_5 = models.CharField(max_length=200, null=True, blank=True)
    tipo_scuola = models.ForeignKey(TipoScuola, null=True, blank=True, on_delete=models.CASCADE)
    prezzo = models.PositiveIntegerField(
        null=True, blank=True, help_text='prezzo in centesimi di euro'
    )
    annullamento_logico = models.CharField(max_length=1, null=True, blank=True)
    materia_alternativa = models.ForeignKey(
        Materia, null=True, blank=True, related_name='materia_alternativa_pk', on_delete=models.CASCADE
    )
    sottotitolo = models.CharField(max_length=200, null=True, blank=True)
    novita_anno_corrente = models.CharField(max_length=1, null=True, blank=True)
    novita_anno_precedente = models.CharField(max_length=1, null=True, blank=True)
    fuori_catalogo = models.CharField(max_length=1, null=True, blank=True)
    fascicolata = models.CharField(max_length=1, null=True, blank=True)
    novita_2_anni_precedenti = models.CharField(max_length=1, null=True, blank=True)
    modalita_tipo = models.ForeignKey(ModalitaTipo, null=True, blank=True, on_delete=models.CASCADE)
    url_copertina = models.CharField(max_length=500, null=True, blank=True)

    class Meta:
        verbose_name_plural = 'opere'
        ordering = ('titolo', 'autore', 'codice_prodotto')

    def __str__(self):
        return str('%s (%s)' % (self.titolo, self.autore))

# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# 12 file815.txt: aggiornato al 30 maggio 2017
# ADOZIONI STORICO

# Campo     Tipo    Pos.   Dim. Descrizione                         Valori ammessi
# COSCUO    Alf.    1       10  Codice ministeriale della scuola    [FILE014.COSCUO]
# CLASSE    Alf.    11      1   Classe                              Compresi tra 1 e 6
# SEZION    Alf.    12      4   Sezione                             A~Z (quindi non caratteri numerici)
# TIPSCU    Alf.    16      2   Codice tipo scuola                  “EE”, “MM”, “NO”, “NT”
# CODSPC *  Alf.    18      6   Codice specializzazione             [FILE306.CODSPC]
# CODSPR *  Alf.    24      6   Codice sperimentazione              [FILE306.CODSPR]
# CODPRO    Num.    30      6   Codice prodotto                     [FILE012.CODPRO]
# CONSIG    Alf.    36      1   Se consigliato                      Campo vuoto / “A” / “M”
# INPOSS    Alf.    37      1   Se in possesso                      “S” / “N”
# CODMAT    Num.    38      5   Codice materia                      [FILE301.CODMAT]
# CODEDI    Num.    43      6   Codice editore                      [FILE030.CODEDI]
# CODICE1   Alf.    49      13  Codice 1 (ISBN 13) relativo all’opera adottata Codice ISBN dell’opera
# INUSO     Alf.    62      1   Se in uso nella classe              “S” / ”N”
# ANNOADO   Alf.    6      4   Anno di prima adozione              Campo vuoto / compreso tra “2009” e anno precedente

# a seconda della combinazione di TIPSCU-CODSPC-CODSPR uno dei due campi CODSPC/CODSPR potrebbe essere vuoto
# Chiave univoca: COSCUO, CLASSE, SEZION, TIPSCU, CODSPC, CODSPR, CODICE1.
# Il campo INPOSS indica il possesso del libro da parte degli alunni:
# se il libro è già in possesso degli alunni (ossia “Da acquistare” = ‘No’) avrà il valore ‘S’,
# se il libro non è in possesso degli alunni e deve essere acquistato (ossia “Da acquistare” = ‘Sì’) avrà il valore ‘N’.
# Il campo INUSO indica l’adozione del libro nella stessa classe dell’anno precedente:
# se il libro era già adottato nella stessa classe dell’anno precedente (ossia “In uso” = “Sì”) avrà il valore ‘S’,
# altrimenti (“In uso” = ‘No’) avrà il valore ‘N’ per identificare la nuova adozione.
# Il campo ANNOADO indica l’anno di prima adozione del titolo.

class AdozioneStorico(models.Model):
    codice_scuola = models.CharField(max_length=200, null=True, blank=True)
    scuola = models.ForeignKey(Scuola, null=True, blank=True, on_delete=models.CASCADE)
    classe = models.PositiveIntegerField(null=True, blank=True)
    sezione = models.CharField(max_length=200, null=True, blank=True)
    tipo_scuola = models.CharField(max_length=200, null=True, blank=True)
    codice_sperimentazione = models.CharField(max_length=200, null=True, blank=True)
    codice_specializzazione = models.CharField(max_length=200, null=True, blank=True)
    indirizzo = models.ForeignKey(Indirizzo, null=True, blank=True, on_delete=models.CASCADE)
    codice_opera = models.CharField(max_length=200, null=True, blank=True)
    opera = models.ForeignKey(Opera, null=True, blank=True, on_delete=models.CASCADE)
    consigliato = models.CharField(max_length=200, null=True, blank=True)
    in_possesso = models.CharField(max_length=200, null=True, blank=True)
    codice_materia = models.CharField(max_length=200, null=True, blank=True)
    materia = models.ForeignKey(Materia, null=True, blank=True, on_delete=models.CASCADE)
    codice_editore = models.CharField(max_length=200, null=True, blank=True)
    editore = models.ForeignKey(Editore, null=True, blank=True, on_delete=models.CASCADE)
    isbn = models.CharField(max_length=13, null=True, blank=True)
    in_uso = models.CharField(max_length=200, null=True, blank=True)
    anno_adozione = models.PositiveIntegerField(null=True, blank=True)

    class Meta:
        verbose_name_plural = 'adozioni storico'
        ordering = ('codice_scuola', 'codice_opera', 'codice_materia')

    def __str__(self):
        return str('%s adotta %s per %s' % (self.codice_scuola, self.codice_opera, self.codice_materia))


# -----------------------------------------------------------------------------
