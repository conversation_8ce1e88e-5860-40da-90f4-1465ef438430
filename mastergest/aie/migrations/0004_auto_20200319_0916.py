# Generated by Django 2.2.9 on 2020-03-19 08:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('aie', '0003_auto_20200318_1541'),
    ]

    operations = [
        migrations.AddField(
            model_name='adozionestorico',
            name='codice_editore',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='adozionestorico',
            name='codice_materia',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='adozionestorico',
            name='codice_opera',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='adozionestorico',
            name='codice_scuola',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='adozionestorico',
            name='scuola',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Scuola'),
        ),
    ]
