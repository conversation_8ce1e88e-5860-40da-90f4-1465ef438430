# Generated by Django 2.2.9 on 2020-03-19 10:41

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('aie', '0004_auto_20200319_0916'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='adozionestorico',
            options={'ordering': ('codice_scuola', 'codice_opera', 'codice_materia'), 'verbose_name_plural': 'adozioni storico'},
        ),
        migrations.AlterModelOptions(
            name='classe',
            options={'ordering': ('scuola', 'classe', 'sezione', 'suffisso', 'indirizzo'), 'verbose_name_plural': 'classi'},
        ),
        migrations.AlterModelOptions(
            name='disciplina',
            options={'ordering': ('descrizione',), 'verbose_name_plural': 'discipline'},
        ),
        migrations.AlterModelOptions(
            name='editore',
            options={'ordering': ('descrizione_editore',), 'verbose_name_plural': 'editori'},
        ),
        migrations.AlterModelOptions(
            name='indirizzo',
            options={'ordering': ('tipo_scuola', 'descrizione'), 'verbose_name_plural': 'indirizzi'},
        ),
        migrations.AlterModelOptions(
            name='materia',
            options={'ordering': ('descrizione_1', 'descrizione_2'), 'verbose_name_plural': 'materie'},
        ),
        migrations.AlterModelOptions(
            name='modalitatipo',
            options={'ordering': ('descrizione',), 'verbose_name_plural': 'modalita tipi'},
        ),
        migrations.AlterModelOptions(
            name='opera',
            options={'ordering': ('titolo', 'autore', 'codice_prodotto'), 'verbose_name_plural': 'opere'},
        ),
        migrations.AlterModelOptions(
            name='scuola',
            options={'ordering': ('nome',), 'verbose_name_plural': 'scuole'},
        ),
        migrations.AlterModelOptions(
            name='tiposcuola',
            options={'ordering': ('descrizione', 'sigla'), 'verbose_name_plural': 'tipi scuola'},
        ),
    ]
