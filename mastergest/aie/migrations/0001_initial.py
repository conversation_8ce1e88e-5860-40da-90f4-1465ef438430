# Generated by Django 2.2.9 on 2020-03-18 09:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Disciplina',
            fields=[
                ('codice', models.CharField(max_length=200, primary_key=True, serialize=False)),
                ('descrizione', models.Char<PERSON>ield(max_length=500)),
            ],
        ),
        migrations.CreateModel(
            name='Editore',
            fields=[
                ('codice_editore', models.PositiveIntegerField(primary_key=True, serialize=False)),
                ('descrizione_editore', models.CharField(max_length=200)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
                ('codice_editore_distributore', models.PositiveIntegerField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Materia',
            fields=[
                ('codice', models.PositiveIntegerField(primary_key=True, serialize=False)),
                ('descrizione_1', models.Char<PERSON>ield(max_length=200)),
                ('descrizione_2', models.Char<PERSON>ield(blank=True, max_length=200, null=True)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='ModalitaTipo',
            fields=[
                ('codice', models.CharField(max_length=200, primary_key=True, serialize=False)),
                ('descrizione', models.CharField(max_length=500)),
            ],
        ),
        migrations.CreateModel(
            name='SedeScolastica',
            fields=[
                ('codice_ministeriale', models.CharField(max_length=10, primary_key=True, serialize=False)),
                ('nome', models.CharField(max_length=200)),
                ('indirizzo', models.CharField(blank=True, max_length=200, null=True)),
                ('frazione', models.CharField(blank=True, max_length=200, null=True)),
                ('localita', models.CharField(blank=True, max_length=200, null=True)),
                ('cap', models.CharField(blank=True, max_length=200, null=True)),
                ('telefono', models.CharField(blank=True, max_length=200, null=True)),
                ('fax', models.CharField(blank=True, max_length=200, null=True)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
            ],
            options={
                'verbose_name_plural': 'sedi scolastiche',
                'ordering': ('nome',),
            },
        ),
        migrations.CreateModel(
            name='TipoScuola',
            fields=[
                ('sigla', models.PositiveIntegerField(primary_key=True, serialize=False)),
                ('descrizione', models.CharField(max_length=200)),
                ('tipi_scuola_dipendenti', models.CharField(blank=True, max_length=200, null=True)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
                ('ordinamento', models.CharField(blank=True, max_length=200, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Scuola',
            fields=[
                ('codice_meccanografico', models.CharField(max_length=200, primary_key=True, serialize=False)),
                ('codice_precedente', models.CharField(blank=True, max_length=200, null=True)),
                ('nome', models.CharField(max_length=200)),
                ('indirizzo', models.CharField(blank=True, max_length=200, null=True)),
                ('frazione', models.CharField(blank=True, max_length=200, null=True)),
                ('comune', models.CharField(blank=True, max_length=200, null=True)),
                ('cap', models.CharField(blank=True, max_length=200, null=True)),
                ('telefono', models.CharField(blank=True, max_length=200, null=True)),
                ('fax', models.CharField(blank=True, max_length=200, null=True)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
                ('email', models.CharField(blank=True, max_length=200, null=True)),
                ('sito', models.CharField(blank=True, max_length=200, null=True)),
                ('privata', models.CharField(blank=True, max_length=200, null=True)),
                ('sede', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.SedeScolastica')),
            ],
        ),
        migrations.CreateModel(
            name='Opera',
            fields=[
                ('codice_prodotto', models.CharField(max_length=200, primary_key=True, serialize=False)),
                ('autore', models.CharField(max_length=200)),
                ('autore_2', models.CharField(blank=True, max_length=200, null=True)),
                ('autore_3', models.CharField(blank=True, max_length=200, null=True)),
                ('curatore', models.CharField(blank=True, max_length=200, null=True)),
                ('curatore_2', models.CharField(blank=True, max_length=200, null=True)),
                ('curatore_3', models.CharField(blank=True, max_length=200, null=True)),
                ('traduttore', models.CharField(blank=True, max_length=200, null=True)),
                ('traduttore2', models.CharField(blank=True, max_length=200, null=True)),
                ('traduttore_3', models.CharField(blank=True, max_length=200, null=True)),
                ('titolo', models.CharField(blank=True, max_length=200, null=True)),
                ('numero_volumi', models.PositiveIntegerField(blank=True, null=True)),
                ('progressivo_volume', models.PositiveIntegerField(blank=True, null=True)),
                ('anno_edizione', models.PositiveIntegerField(blank=True, null=True)),
                ('codice_fondamentale', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_isbn', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_2', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_3', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_4', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_5', models.CharField(blank=True, max_length=200, null=True)),
                ('prezzo', models.PositiveIntegerField(blank=True, help_text='prezzo in centesimi di euro', null=True)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
                ('sottotitolo', models.CharField(blank=True, max_length=200, null=True)),
                ('novita_anno_corrente', models.CharField(blank=True, max_length=1, null=True)),
                ('novita_anno_precedente', models.CharField(blank=True, max_length=1, null=True)),
                ('fuori_catalogo', models.CharField(blank=True, max_length=1, null=True)),
                ('fascicolata', models.CharField(blank=True, max_length=1, null=True)),
                ('novita_2_anni_precedenti', models.CharField(blank=True, max_length=1, null=True)),
                ('url_copertina', models.CharField(blank=True, max_length=500, null=True)),
                ('editore', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Editore')),
                ('materia', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Materia')),
                ('materia_alternativa', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='materia_alternativa_pk', to='aie.Materia')),
                ('modalita_tipo', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.ModalitaTipo')),
                ('tipo_scuola', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.TipoScuola')),
            ],
        ),
        migrations.CreateModel(
            name='MateriaDisciplina',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('disciplina', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aie.Disciplina')),
                ('materia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aie.Materia')),
            ],
        ),
        migrations.CreateModel(
            name='Indirizzo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codice_sperimentazione', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_specializzazione', models.CharField(blank=True, max_length=200, null=True)),
                ('descrizione', models.CharField(blank=True, max_length=200, null=True)),
                ('annullamento_logico', models.CharField(blank=True, max_length=1, null=True)),
                ('tipo_scuola', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aie.TipoScuola')),
            ],
        ),
        migrations.CreateModel(
            name='Classe',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sezione', models.CharField(blank=True, max_length=200, null=True)),
                ('tipo_scuola', models.CharField(blank=True, max_length=200, null=True)),
                ('suffisso', models.CharField(blank=True, max_length=200, null=True)),
                ('classe', models.PositiveIntegerField(blank=True, null=True)),
                ('alunni', models.PositiveIntegerField(blank=True, null=True)),
                ('codice_sperimentazione', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_specializzazione', models.CharField(blank=True, max_length=200, null=True)),
                ('indirizzo', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Indirizzo')),
                ('scuola', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aie.Scuola')),
            ],
        ),
        migrations.CreateModel(
            name='AdozioneStorico',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('classe', models.PositiveIntegerField(blank=True, null=True)),
                ('sezione', models.CharField(blank=True, max_length=200, null=True)),
                ('tipo_scuola', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_sperimentazione', models.CharField(blank=True, max_length=200, null=True)),
                ('codice_specializzazione', models.CharField(blank=True, max_length=200, null=True)),
                ('consigliato', models.CharField(blank=True, max_length=200, null=True)),
                ('in_possesso', models.CharField(blank=True, max_length=200, null=True)),
                ('isbn', models.CharField(blank=True, max_length=13, null=True)),
                ('in_uso', models.CharField(blank=True, max_length=200, null=True)),
                ('anno_adozione', models.PositiveIntegerField(blank=True, null=True)),
                ('editore', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Editore')),
                ('indirizzo', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Indirizzo')),
                ('materia', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Materia')),
                ('opera', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='aie.Opera')),
                ('scuola', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aie.Scuola')),
            ],
        ),
    ]
