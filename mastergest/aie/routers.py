

class AieBaseRouter(object):
    def db_for_read(self, model, **hints):
        if model._meta.app_label == 'aie':
            return 'mastercom_aie'
        return None

    def db_for_write(self, model, **hints):
        return self.db_for_read(model, **hints)

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        if app_label == 'aie':
            if db == 'mastercom_aie':
                return True
            else:
                return False
        if db == 'mastercom_aie':
            if app_label == 'aie':
                return True
            else:
                return False
        return None


class AieRouter(AieBaseRouter):
    def db_for_write(self, model, **hints):
        if model._meta.app_label == 'aie':
            return False
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        migrate = super(AieRouter, self).allow_migrate(db, app_label, model_name=model_name, **hints)
        if migrate:
            return False
        return migrate
